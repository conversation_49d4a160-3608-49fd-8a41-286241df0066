package com.lacus.service.datacollection.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 流程执行上下文
 *
 * <AUTHOR>
 */
@Data
public class FlowContext {

    /**
     * 执行ID
     */
    private String executionId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 流程定义
     */
    private FlowDefinition flowDefinition;

    /**
     * 全局变量
     */
    private Map<String, Object> globalVariables;

    /**
     * 节点变量（每个节点的局部变量）
     */
    private Map<String, Map<String, Object>> nodeVariables;

    /**
     * 循环上下文（支持嵌套循环）
     */
    private Map<String, LoopContext> loopContexts;

    /**
     * 执行状态
     */
    private String status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行日志
     */
    private StringBuilder executionLog;

    /**
     * 数据缓存
     */
    private Map<String, Object> dataCache;

    public FlowContext() {
        this.globalVariables = new ConcurrentHashMap<>();
        this.nodeVariables = new ConcurrentHashMap<>();
        this.loopContexts = new ConcurrentHashMap<>();
        this.dataCache = new ConcurrentHashMap<>();
        this.executionLog = new StringBuilder();
        this.startTime = LocalDateTime.now();
        this.status = "RUNNING";
    }

    /**
     * 设置全局变量
     */
    public void setGlobalVariable(String name, Object value) {
        this.globalVariables.put(name, value);
    }

    /**
     * 获取全局变量
     */
    public Object getGlobalVariable(String name) {
        return this.globalVariables.get(name);
    }

    /**
     * 设置节点变量
     */
    public void setNodeVariable(String nodeId, String name, Object value) {
        this.nodeVariables.computeIfAbsent(nodeId, k -> new HashMap<>()).put(name, value);
    }

    /**
     * 获取节点变量
     */
    public Object getNodeVariable(String nodeId, String name) {
        Map<String, Object> nodeVars = this.nodeVariables.get(nodeId);
        return nodeVars != null ? nodeVars.get(name) : null;
    }

    /**
     * 添加执行日志
     */
    public void addLog(String message) {
        this.executionLog.append("[").append(LocalDateTime.now()).append("] ").append(message).append("\n");
    }

    /**
     * 设置数据缓存
     */
    public void setDataCache(String key, Object data) {
        this.dataCache.put(key, data);
    }

    /**
     * 获取数据缓存
     */
    public Object getDataCache(String key) {
        return this.dataCache.get(key);
    }

    /**
     * 循环上下文
     */
    @Data
    public static class LoopContext {
        /**
         * 循环节点ID
         */
        private String loopNodeId;

        /**
         * 当前循环索引
         */
        private Integer currentIndex;

        /**
         * 总循环次数
         */
        private Integer totalCount;

        /**
         * 循环变量
         */
        private Map<String, Object> loopVariables;

        /**
         * 当前循环数据
         */
        private Object currentData;

        /**
         * 是否继续循环
         */
        private Boolean continueLoop;

        public LoopContext() {
            this.loopVariables = new HashMap<>();
            this.currentIndex = 0;
            this.continueLoop = true;
        }

        /**
         * 下一次循环
         */
        public void nextIteration() {
            this.currentIndex++;
        }

        /**
         * 是否还有下一次循环
         */
        public boolean hasNext() {
            return this.continueLoop && (this.totalCount == null || this.currentIndex < this.totalCount);
        }
    }
}
