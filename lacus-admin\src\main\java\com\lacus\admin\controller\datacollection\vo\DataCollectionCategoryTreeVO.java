package com.lacus.admin.controller.datacollection.vo;

import lombok.Data;

import java.util.List;

/**
 * 数据采集分类树形结构VO
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class DataCollectionCategoryTreeVO {

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 父分类ID
     */
    private Long parentId;

    /**
     * 分类路径
     */
    private String categoryPath;

    /**
     * 分类层级
     */
    private Integer categoryLevel;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 分类图标
     */
    private String categoryIcon;

    /**
     * 分类描述
     */
    private String categoryDesc;

    /**
     * 状态(0:禁用 1:启用)
     */
    private Integer status;

    /**
     * 任务数量
     */
    private Integer taskCount;

    /**
     * 子分类列表
     */
    private List<DataCollectionCategoryTreeVO> children;

    /**
     * 是否有子分类
     */
    private Boolean hasChildren;

    /**
     * 是否展开
     */
    private Boolean expanded;

    /**
     * 是否选中
     */
    private Boolean selected;

    /**
     * 是否禁用
     */
    private Boolean disabled;

    /**
     * 节点标签
     */
    private String label;

    /**
     * 节点值
     */
    private String value;

    /**
     * 节点键
     */
    private String key;
}
