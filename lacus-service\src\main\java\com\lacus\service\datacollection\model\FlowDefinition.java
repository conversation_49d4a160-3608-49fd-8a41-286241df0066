package com.lacus.service.datacollection.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 流程定义模型
 *
 * <AUTHOR>
 */
@Data
public class FlowDefinition {

    /**
     * 流程ID
     */
    private String flowId;

    /**
     * 流程名称
     */
    private String flowName;

    /**
     * 流程版本
     */
    private String version;

    /**
     * 节点列表
     */
    private List<FlowNode> nodes;

    /**
     * 连线列表
     */
    private List<FlowEdge> edges;

    /**
     * 全局变量
     */
    private Map<String, Object> globalVariables;

    /**
     * 流程配置
     */
    private FlowConfig config;

    /**
     * 流程节点
     */
    @Data
    public static class FlowNode {
        /**
         * 节点ID
         */
        private String id;

        /**
         * 节点名称
         */
        private String name;

        /**
         * 节点类型
         */
        private String type;

        /**
         * 节点配置
         */
        private Map<String, Object> config;

        /**
         * 位置信息
         */
        private Position position;

        /**
         * 输入端口
         */
        private List<Port> inputPorts;

        /**
         * 输出端口
         */
        private List<Port> outputPorts;
    }

    /**
     * 流程连线
     */
    @Data
    public static class FlowEdge {
        /**
         * 连线ID
         */
        private String id;

        /**
         * 源节点ID
         */
        private String sourceNodeId;

        /**
         * 源端口ID
         */
        private String sourcePortId;

        /**
         * 目标节点ID
         */
        private String targetNodeId;

        /**
         * 目标端口ID
         */
        private String targetPortId;

        /**
         * 条件表达式
         */
        private String condition;

        /**
         * 连线配置
         */
        private Map<String, Object> config;
    }

    /**
     * 位置信息
     */
    @Data
    public static class Position {
        private Integer x;
        private Integer y;
    }

    /**
     * 端口信息
     */
    @Data
    public static class Port {
        private String id;
        private String name;
        private String type; // input/output
        private String dataType; // string/number/object/array
    }

    /**
     * 流程配置
     */
    @Data
    public static class FlowConfig {
        /**
         * 超时时间(秒)
         */
        private Integer timeout;

        /**
         * 重试次数
         */
        private Integer retryCount;

        /**
         * 并发执行
         */
        private Boolean concurrent;

        /**
         * 错误处理策略
         */
        private String errorHandling; // STOP/CONTINUE/RETRY
    }
}
