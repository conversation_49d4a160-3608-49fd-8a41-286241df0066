# java 环境，目前主要在 spark 数据开发模块使用（可选）
java.home=/www/server/java/jdk1.8.0_371

# hadoop 配置文件目录，目前主要在 spark 数据开发模块使用（可选
hadoop.conf.dir=/home/<USER>/hadoop-3.3.6/etc/hadoop

# spark 客户端路径，目前主要在 spark 数据开发模块使用（可选）
spark.client.home=/home/<USER>/spark-3.5.1-bin-hadoop3

# spark 日志路径，目前主要在 spark 数据开发模块使用（可选）
spark.log.path=/home/<USER>/spark-3.5.1-bin-hadoop3/logs/info.log

# spark sql 文件临时目录，目前主要在 spark 数据开发模块使用（可选）
spark.sql.file.dir=/home/<USER>/spark-3.5.1-bin-hadoop3/data/files

# hdfs 用户配置，目前主要在资源中心使用（必填）
hadoop.username=cy

# defaultFS，Hdfs的核心配置（必填）
hdfs.defaultFS=hdfs://shouhou:9000

# yarn ha模式，请设置yarn ha地址（可选）
yarn.resourcemanager.ha.rm.ids=

# yarn 任务状态地址，请将hadoop1替换为实际的RM地址，目前没用到（可选）
yarn.application.status.address=http://shouhou:%s/ws/v1/cluster/apps/%s

# yarn历史任务地址，请将hadoop1替换为实际的RM地址，目前没用到（可选）
yarn.job.history.status.address=http://shouhou:19888/ws/v1/history/mapreduce/jobs/%s

# yarn rest 地址，请将hadoop2替换为实际的RM地址（必填）
yarn.restapi-address:http://shouhou:8088/proxy/

# yarn节点 rest 地址，请将hadoop2替换为实际的RM地址（必填）
yarn.node-address:http://shouhou:8088/cluster/nodes


# yarn RM 端口（必填）
yarn.resource.manager.httpaddress.port=8088

# 本地临时文件目录，主要用于本地临时文件的生成（必填）
data.basedir.path=/home/<USER>/tmp/data

# hadoop是否启用kerberos认证（可选）
hadoop.security.authentication.startup.state=false

# kerberos认证的krb5.conf文件位置（可选）
java.security.krb5.conf.path=

# kerberos认证的keytab文件位置（可选）
login.user.keytab.path=

# kerberos认证的principal（可选）
login.user.keytab.username=

# kafka 地址，主要用于实时数据采集（必填）
kafka.bootstrapServers=shouhou:9092

# 实时数据采集配置文件在hdfs上的路径（必填）
flink.hdfs.collector.conf-path=/rtc/conf/

# 数据采集主jar包在hdfs上的路径（必填）
flink.hdfs.collector.job-jars-path=/rtc/engine/

# 数据采集主jar包名称（必填）
flink.hdfs.collector.jar-name=lacus-rtc-engine.jar

# 数据采集依赖包在hdfs上的路径（必填）
flink.hdfs.collector.lib-path=/rtc/libs

# 数据采集flink主jar包在hdfs上的路径（必填）
flink.hdfs.dist-jar-path=/rtc/libs/flink-dist-1.16.2.jar

# savepoint默认路径（必填）
flink.default.savepoint.path=hdfs://shouhou:9000/flink/savepoint/lacus/
# 程序部署目录，其中flink sql任务和spark sql任务的jar包也需要放在这里
lacus.application.home=/home/<USER>/lacus/
# flink sql任务jar包
flink.sql.job.jar=lacus-flink-sql-app-2.0.0-jar-with-dependencies.jar
# spark sql app
spark.sql.job.jar=lacus-spark-sql-app-2.0.0-jar-with-dependencies.jar
# Local Cluster模式flink集群地址, docker访问本机网络：host.docker.internal
flink.rest.http.address=http://shouhou:8081
# Standalone Cluster模式flink集群地址
flink.rest.ha.http.address=http://shouhou:8081
# 本地flink客户端路径 /opt/software/flink1.16.3/
flink.client.home=/home/<USER>/flink-1.16.2
# flink任务执行目录，用于生产临时文件 /opt/software/lacus/execute/flink/
flink.job.execute.home=/home/<USER>/flink-1.16.2/temp/
