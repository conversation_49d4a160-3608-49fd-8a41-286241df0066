package com.lacus.dao.datacollection.vo;

import lombok.Data;

import java.util.Map;

/**
 * 数据源连接VO
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class DataSourceConnectionVO {

    /**
     * 数据源类型
     */
    private String sourceType;

    /**
     * 连接名称
     */
    private String connectionName;

    /**
     * 主机地址
     */
    private String host;

    /**
     * 端口
     */
    private Integer port;

    /**
     * 数据库名
     */
    private String database;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 连接URL
     */
    private String url;

    /**
     * 驱动类名
     */
    private String driverClassName;

    /**
     * 连接参数
     */
    private Map<String, Object> connectionParams;

    /**
     * 连接池配置
     */
    private Map<String, Object> poolConfig;

    /**
     * 超时时间
     */
    private Integer timeout;

    /**
     * 最大连接数
     */
    private Integer maxConnections;

    /**
     * 最小连接数
     */
    private Integer minConnections;

    /**
     * 是否启用SSL
     */
    private Boolean enableSsl;

    /**
     * 字符编码
     */
    private String charset;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 扩展配置
     */
    private Map<String, Object> extendConfig;

    /**
     * 连接状态
     */
    private String status;

    /**
     * 连接测试结果
     */
    private String testResult;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 连接时间
     */
    private Long connectTime;

    /**
     * 是否可用
     */
    private Boolean available;
}
