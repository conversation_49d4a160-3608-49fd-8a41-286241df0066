package com.lacus.admin.controller.datawarehouse;

import com.lacus.common.core.base.BaseController;
import com.lacus.common.core.dto.ResponseDTO;
import com.lacus.service.datawarehouse.EtlExecutionService;
import com.lacus.service.datawarehouse.EtlTaskService;
import com.lacus.service.datawarehouse.command.EtlTaskAddCommand;
import com.lacus.service.datawarehouse.dto.EtlTaskDTO;
import com.lacus.service.datawarehouse.dto.EtlTaskStatsDTO;
import com.lacus.service.datawarehouse.query.EtlExecutionHistoryQuery;
import com.lacus.service.datawarehouse.query.EtlTaskQuery;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.lacus.core.security.AuthenticationUtils.getUsername;

/**
 * ETL任务管理控制器
 */
@Api(value = "ETL任务管理控制器", tags = {"ETL任务管理控制器"})
@RestController
@RequestMapping("/datawarehouse/etl")
public class EtlTaskController extends BaseController {

    @Autowired
    private EtlTaskService etlTaskService;

    @Autowired
    private EtlExecutionService etlExecutionService;

    /**
     * 查询ETL任务列表
     */
    @GetMapping("/list")
    public ResponseDTO<?> list(EtlTaskQuery query) {
        return ResponseDTO.ok(etlTaskService.queryEtlTaskDtoList(query));
    }

    /**
     * 获取ETL任务详细信息
     */
    @GetMapping("/{taskId}")
    @PreAuthorize("@permission.has('datawarehouse:etl:query')")
    public ResponseDTO<EtlTaskDTO> getInfo(@PathVariable Long taskId) {
        return ResponseDTO.ok(etlTaskService.queryEtlTaskById(taskId));
    }

    /**
     * 新增ETL任务
     */
    @PostMapping
    @PreAuthorize("@permission.has('datawarehouse:etl:add')")
    public ResponseDTO<Long> add(@Validated @RequestBody EtlTaskAddCommand command) {
        Long taskId = etlTaskService.saveEtlTask(command);
        return ResponseDTO.ok(taskId);
    }

    /**
     * 修改ETL任务
     */
    @PutMapping
    @PreAuthorize("@permission.has('datawarehouse:etl:edit')")
    public ResponseDTO<Void> edit(@Validated @RequestBody EtlTaskAddCommand command) {
        etlTaskService.updateEtlTask(command);
        return ResponseDTO.ok();
    }

    /**
     * 删除ETL任务
     */
    @PostMapping("/delete")
    @PreAuthorize("@permission.has('datawarehouse:etl:remove')")
    public ResponseDTO<Void> remove(@RequestBody List<Long> taskIds) {
        etlTaskService.batchDeleteEtlTask(taskIds);
        return ResponseDTO.ok();
    }

    /**
     * 更新ETL任务状态
     */
    @PutMapping("/status")
    @PreAuthorize("@permission.has('datawarehouse:etl:edit')")
    public ResponseDTO<Void> updateStatus(@RequestBody Map<String, Object> params) {
        Long taskId = Long.valueOf(params.get("taskId").toString());
        Integer status = Integer.valueOf(params.get("status").toString());
        etlTaskService.updateEtlTaskStatus(taskId, status);
        return ResponseDTO.ok();
    }

    /**
     * 执行ETL任务
     */
    @PostMapping("/run/{taskId}")
    @PreAuthorize("@permission.has('datawarehouse:etl:run')")
    public ResponseDTO<String> run(@PathVariable Long taskId) {
        String executionId = etlTaskService.runEtlTask(taskId);
        return ResponseDTO.ok(executionId);
    }

    /**
     * 停止ETL任务
     */
    @PostMapping("/stop/{taskId}")
    @PreAuthorize("@permission.has('datawarehouse:etl:run')")
    public ResponseDTO<Void> stop(@PathVariable Long taskId) {
        etlTaskService.stopEtlTask(taskId);
        return ResponseDTO.ok();
    }

    /**
     * 获取执行历史
     */
    @GetMapping("/history/{taskId}")
    @PreAuthorize("@permission.has('datawarehouse:etl:query')")
    public ResponseDTO<?> getHistory(@PathVariable String taskId) {
        return ResponseDTO.ok(etlTaskService.getExecutionHistory(taskId));
    }

    /**
     * 获取所有ETL任务执行历史（分页）
     */
    @PostMapping("/historylist")
    public ResponseDTO<?> getHistoryList(@RequestBody EtlExecutionHistoryQuery query) {
        return ResponseDTO.ok(etlTaskService.getHistoryList(query));
    }

    /**
     * 获取执行日志
     */
    @GetMapping("/log/{executionId}")
    @PreAuthorize("@permission.has('datawarehouse:etl:query')")
    public ResponseDTO<String> getLog(@PathVariable String executionId) {
        String log = etlTaskService.getExecutionLog(executionId);
        return ResponseDTO.ok(log);
    }

    /**
     * 获取执行指标
     */
    @GetMapping("/metrics/{executionId}")
    @PreAuthorize("@permission.has('datawarehouse:etl:query')")
    public ResponseDTO<Object> getMetrics(@PathVariable String executionId) {
        Object metrics = etlTaskService.getExecutionMetrics(executionId);
        return ResponseDTO.ok(metrics);
    }

    /**
     * 验证ETL配置
     */
    @PostMapping("/validate")
    @PreAuthorize("@permission.has('datawarehouse:etl:add')")
    public ResponseDTO<Boolean> validate(@RequestBody EtlTaskAddCommand command) {
        Boolean isValid = etlTaskService.validateEtlConfig(command);
        return ResponseDTO.ok(isValid);
    }

    /**
     * 预览ETL结果
     */
    @PostMapping("/preview")
    @PreAuthorize("@permission.has('datawarehouse:etl:add')")
    public ResponseDTO<Object> preview(@RequestBody EtlTaskAddCommand command) {
        Object result = etlTaskService.previewEtlResult(command);
        return ResponseDTO.ok(result);
    }

    /**
     * 获取字段映射建议
     */
    @PostMapping("/mapping-suggestions")
    @PreAuthorize("@permission.has('datawarehouse:etl:add')")
    public ResponseDTO<Object> getMappingSuggestions(@RequestBody Map<String, Object> params) {
        String sourceLayer = (String) params.get("sourceLayer");
        String targetLayer = (String) params.get("targetLayer");
        @SuppressWarnings("unchecked")
        List<String> sourceTables = (List<String>) params.get("sourceTables");

        Object suggestions = etlTaskService.getFieldMappingSuggestions(sourceLayer, targetLayer, sourceTables);
        return ResponseDTO.ok(suggestions);
    }

    /**
     * 根据目标表生成智能字段映射
     */
    @PostMapping("/intelligent-mapping")
    @PreAuthorize("@permission.has('datawarehouse:etl:add')")
    public ResponseDTO<Object> generateIntelligentMapping(@RequestBody Map<String, Object> params) {
        String sourceLayer = (String) params.get("sourceLayer");
        String targetLayer = (String) params.get("targetLayer");
        @SuppressWarnings("unchecked")
        List<String> sourceTables = (List<String>) params.get("sourceTables");
        String targetTable = (String) params.get("targetTable");

        Object mappings = etlExecutionService.generateFieldMappingsByTargetTable(sourceLayer, targetLayer, sourceTables, targetTable);
        return ResponseDTO.ok(mappings);
    }

    /**
     * 获取数据血缘关系
     */
    @GetMapping("/lineage/{taskId}")
    @PreAuthorize("@permission.has('datawarehouse:etl:query')")
    public ResponseDTO<Object> getLineage(@PathVariable Long taskId) {
        Object lineage = etlTaskService.getDataLineage(taskId);
        return ResponseDTO.ok(lineage);
    }

    /**
     * 获取任务依赖关系
     */
    @GetMapping("/dependencies/{taskId}")
    @PreAuthorize("@permission.has('datawarehouse:etl:query')")
    public ResponseDTO<Object> getDependencies(@PathVariable Long taskId) {
        Object dependencies = etlTaskService.getTaskDependencies(taskId);
        return ResponseDTO.ok(dependencies);
    }

    /**
     * 批量执行ETL任务
     */
    @PostMapping("/batch-run")
    @PreAuthorize("@permission.has('datawarehouse:etl:run')")
    public ResponseDTO<Void> batchRun(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> taskIds = (List<Long>) params.get("taskIds");

        for (Long taskId : taskIds) {
            etlTaskService.runEtlTask(taskId);
        }
        return ResponseDTO.ok();
    }

    /**
     * 获取ETL任务统计信息
     */
    @GetMapping("/stats")
    @PreAuthorize("@permission.has('datawarehouse:etl:list')")
    public ResponseDTO<EtlTaskStatsDTO> getStats() {
        return ResponseDTO.ok(etlTaskService.getEtlTaskStats());
    }

    /**
     * 导出ETL任务配置
     */
    @GetMapping("/export/{taskId}")
    @PreAuthorize("@permission.has('datawarehouse:etl:export')")
    public ResponseDTO<Object> export(@PathVariable Long taskId) {
        Object config = etlTaskService.exportEtlTaskConfig(taskId);
        return ResponseDTO.ok(config);
    }

    /**
     * 导入ETL任务配置
     */
    @PostMapping("/import")
    @PreAuthorize("@permission.has('datawarehouse:etl:import')")
    public ResponseDTO<Void> importConfig(@RequestBody Object data) {
        etlTaskService.importEtlTaskConfig(data);
        return ResponseDTO.ok();
    }
}
