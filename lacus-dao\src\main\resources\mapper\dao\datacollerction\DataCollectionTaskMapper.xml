<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lacus.dao.datacollection.mapper.DataCollectionTaskMapper">
    
    <resultMap type="com.lacus.dao.datacollection.entity.DataCollectionTaskEntity" id="DataCollectionTaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="taskCode"    column="task_code"    />
        <result property="categoryId"    column="category_id"    />
        <result property="taskDesc"    column="task_desc"    />
        <result property="collectionType"    column="collection_type"    />
        <result property="sourceType"    column="source_type"    />
        <result property="sourceConfig"    column="source_config"    />
        <result property="targetDatabase"    column="target_database"    />
        <result property="targetTable"    column="target_table"    />
        <result property="writeMode"    column="write_mode"    />
        <result property="scheduleType"    column="schedule_type"    />
        <result property="cronExpression"    column="cron_expression"    />
        <result property="flowDefinition"    column="flow_definition"    />
        <result property="taskConfig"    column="task_config"    />
        <result property="priority"    column="priority"    />
        <result property="timeout"    column="timeout"    />
        <result property="retryCount"    column="retry_count"    />
        <result property="status"    column="status"    />
        <result property="version"    column="version"    />
        <result property="creatorId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updaterId"    column="update_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="categoryName"    column="category_name"    />
    </resultMap>

    <sql id="selectDataCollectionTaskVo">
        select t.task_id, t.task_name, t.task_code, t.category_id, t.task_desc, t.collection_type, 
               t.source_type, t.source_config, t.target_database, t.target_table, t.write_mode, 
               t.schedule_type, t.cron_expression, t.flow_definition, t.task_config, t.priority, 
               t.timeout, t.retry_count, t.status, t.version, t.create_id, t.create_time,
               t.update_id, t.update_time, t.remark, c.category_name
        from data_collection_task t
        left join data_collection_category c on t.category_id = c.category_id
    </sql>

    <!-- 分页查询任务列表 -->
    <select id="selectTaskPage" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        <where>
            <if test="taskName != null and taskName != ''">
                and t.task_name like concat('%', #{taskName}, '%')
            </if>
            <if test="taskCode != null and taskCode != ''">
                and t.task_code = #{taskCode}
            </if>
            <if test="categoryId != null">
                and t.category_id = #{categoryId}
            </if>
            <if test="collectionType != null and collectionType != ''">
                and t.collection_type = #{collectionType}
            </if>
            <if test="sourceType != null and sourceType != ''">
                and t.source_type = #{sourceType}
            </if>
            <if test="scheduleType != null and scheduleType != ''">
                and t.schedule_type = #{scheduleType}
            </if>
            <if test="status != null">
                and t.status = #{status}
            </if>
            <if test="targetDatabase != null and targetDatabase != ''">
                and t.target_database = #{targetDatabase}
            </if>
            <if test="targetTable != null and targetTable != ''">
                and t.target_table = #{targetTable}
            </if>
            <if test="createTimeStart != null and createTimeStart != ''">
                and t.create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                and t.create_time &lt;= #{createTimeEnd}
            </if>
            <if test="keyword != null and keyword != ''">
                and (t.task_name like concat('%', #{keyword}, '%') or t.task_code like concat('%', #{keyword}, '%'))
            </if>
        </where>
        order by t.create_time desc
    </select>

    <!-- 检查任务编码是否唯一 -->
    <select id="checkTaskCodeUnique" resultMap="DataCollectionTaskResult">
        select task_id, task_name, task_code from data_collection_task 
        where task_code = #{taskCode}
        <if test="taskId != null">
            and task_id != #{taskId}
        </if>
        limit 1
    </select>

    <!-- 检查任务名称是否唯一 -->
    <select id="checkTaskNameUnique" resultMap="DataCollectionTaskResult">
        select task_id, task_name, task_code from data_collection_task 
        where task_name = #{taskName} and category_id = #{categoryId}
        <if test="taskId != null">
            and task_id != #{taskId}
        </if>
        limit 1
    </select>

    <!-- 根据任务名称查询任务 -->
    <select id="selectByTaskName" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.task_name = #{taskName} limit 1
    </select>

    <!-- 根据任务编码查询任务 -->
    <select id="selectByTaskCode" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.task_code = #{taskCode} limit 1
    </select>

    <!-- 查询启用状态的任务列表 -->
    <select id="selectEnabledTasks" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.status = 1
        order by t.priority desc, t.create_time desc
    </select>

    <!-- 根据调度类型查询任务列表 -->
    <select id="selectByScheduleType" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.schedule_type = #{scheduleType} and t.status = 1
        order by t.priority desc, t.create_time desc
    </select>

    <!-- 根据分类ID查询任务列表 -->
    <select id="selectByCategoryId" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.category_id = #{categoryId}
        <if test="status != null">
            and t.status = #{status}
        </if>
        order by t.create_time desc
    </select>

    <!-- 更新任务状态 -->
    <update id="updateTaskStatus">
        update data_collection_task set status = #{status} where task_id = #{taskId}
    </update>

    <!-- 批量更新任务状态 -->
    <update id="batchUpdateTaskStatus">
        update data_collection_task set status = #{status} where task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>

    <!-- 批量删除任务 -->
    <delete id="deleteBatchByIds">
        delete from data_collection_task where task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>

    <!-- 批量移动任务到指定分类 -->
    <update id="batchMoveTasksToCategory">
        update data_collection_task set category_id = #{categoryId} where task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>

    <!-- 根据分类ID统计任务数量 -->
    <select id="countTasksByCategory" resultType="int">
        select count(1) from data_collection_task where category_id = #{categoryId}
    </select>

    <!-- 获取任务统计信息 -->
    <select id="selectTaskStats" resultType="java.util.Map">
        select 
            count(*) as total_count,
            sum(case when status = 1 then 1 else 0 end) as enabled_count,
            sum(case when status = 0 then 1 else 0 end) as disabled_count,
            count(distinct category_id) as category_count,
            count(distinct collection_type) as collection_type_count,
            count(distinct source_type) as source_type_count
        from data_collection_task
        <if test="categoryId != null">
            where category_id = #{categoryId}
        </if>
    </select>

    <!-- 查询最近执行的任务 -->
    <select id="selectRecentExecutedTasks" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.status = 1
        order by t.update_time desc
        limit #{limit}
    </select>

    <!-- 查询执行失败的任务 -->
    <select id="selectFailedTasks" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.status = 1
        order by t.update_time desc
    </select>

    <!-- 查询长时间未执行的任务 -->
    <select id="selectLongTimeNoExecuteTasks" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.status = 1 
        and t.update_time &lt; DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        order by t.update_time asc
    </select>

    <!-- 查询正在运行的任务 -->
    <select id="selectRunningTasks" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.status = 1
        order by t.update_time desc
    </select>

    <!-- 根据目标数据库查询任务 -->
    <select id="selectByTargetDatabase" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.target_database = #{targetDatabase}
        order by t.create_time desc
    </select>

    <!-- 根据目标表查询任务 -->
    <select id="selectByTargetTable" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.target_table = #{targetTable}
        order by t.create_time desc
    </select>

    <!-- 更新任务版本号 -->
    <update id="updateTaskVersion">
        update data_collection_task set version = version + 1 where task_id = #{taskId}
    </update>

    <!-- 获取任务的依赖关系 -->
    <select id="selectTaskDependencies" resultType="java.util.Map">
        select 
            'dependency' as type,
            task_id,
            task_name,
            task_code
        from data_collection_task
        where task_id = #{taskId}
    </select>

    <!-- 查询任务的前置依赖 -->
    <select id="selectTaskPreDependencies" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.task_id = #{taskId}
    </select>

    <!-- 查询任务的后置依赖 -->
    <select id="selectTaskPostDependencies" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.task_id = #{taskId}
    </select>

    <!-- 根据创建时间范围查询任务 -->
    <select id="selectTasksByCreateTime" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.create_time between #{startTime} and #{endTime}
        order by t.create_time desc
    </select>

    <!-- 获取任务创建统计（按日期分组） -->
    <select id="selectTaskCreateStats" resultType="java.util.Map">
        select DATE(create_time) as date, count(*) as count
        from data_collection_task
        where create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        group by DATE(create_time)
        order by date
    </select>

    <!-- 查询热门任务（按执行次数排序） -->
    <select id="selectHotTasks" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.status = 1
        order by t.priority desc, t.update_time desc
        limit #{limit}
    </select>

    <!-- 根据采集类型查询任务 -->
    <select id="selectByCollectionType" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.collection_type = #{collectionType}
        <if test="status != null">
            and t.status = #{status}
        </if>
        order by t.create_time desc
    </select>

    <!-- 根据数据源类型查询任务 -->
    <select id="selectBySourceType" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.source_type = #{sourceType}
        <if test="status != null">
            and t.status = #{status}
        </if>
        order by t.create_time desc
    </select>

</mapper>
