<template>
  <div class="flow-designer">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h2 class="page-title">
          <el-icon><Connection /></el-icon>
          数据采集流程设计器
        </h2>
      </div>
      <div class="toolbar-right">
        <el-button-group>
          <el-button @click="saveFlow" type="primary" :loading="saving">
            <el-icon><DocumentAdd /></el-icon>
            保存
          </el-button>
          <el-button @click="validateFlow">
            <el-icon><CircleCheck /></el-icon>
            验证
          </el-button>
          <el-button @click="previewFlow">
            <el-icon><View /></el-icon>
            预览
          </el-button>
          <el-button @click="executeFlow" type="success">
            <el-icon><VideoPlay /></el-icon>
            执行
          </el-button>
        </el-button-group>
      </div>
    </div>

    <div class="designer-container">
      <!-- 左侧节点面板 -->
      <div class="node-panel">
        <div class="panel-header">
          <h3>组件库</h3>
        </div>
        <div class="panel-content">
          <el-collapse v-model="activeCollapse">
            <!-- 基础节点 -->
            <el-collapse-item title="基础节点" name="basic">
              <div class="node-group">
                <div 
                  v-for="node in basicNodes" 
                  :key="node.type"
                  class="node-item"
                  draggable="true"
                  @dragstart="handleDragStart($event, node)"
                >
                  <el-icon :class="node.icon"></el-icon>
                  <span>{{ node.name }}</span>
                </div>
              </div>
            </el-collapse-item>

            <!-- 数据处理节点 -->
            <el-collapse-item title="数据处理" name="data">
              <div class="node-group">
                <div 
                  v-for="node in dataNodes" 
                  :key="node.type"
                  class="node-item"
                  draggable="true"
                  @dragstart="handleDragStart($event, node)"
                >
                  <el-icon :class="node.icon"></el-icon>
                  <span>{{ node.name }}</span>
                </div>
              </div>
            </el-collapse-item>

            <!-- 控制流节点 -->
            <el-collapse-item title="控制流" name="control">
              <div class="node-group">
                <div 
                  v-for="node in controlNodes" 
                  :key="node.type"
                  class="node-item"
                  draggable="true"
                  @dragstart="handleDragStart($event, node)"
                >
                  <el-icon :class="node.icon"></el-icon>
                  <span>{{ node.name }}</span>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-container">
        <div class="canvas-header">
          <div class="canvas-tools">
            <el-button-group size="small">
              <el-button @click="zoomIn">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
              <el-button @click="zoomOut">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button @click="resetZoom">
                <el-icon><RefreshRight /></el-icon>
              </el-button>
              <el-button @click="fitCanvas">
                <el-icon><FullScreen /></el-icon>
              </el-button>
            </el-button-group>
            <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
          </div>
        </div>
        
        <!-- 流程画布 -->
        <div 
          ref="canvasRef" 
          class="flow-canvas"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @click="handleCanvasClick"
          :style="{ transform: `scale(${zoomLevel})` }"
        >
          <!-- 网格背景 -->
          <div class="grid-background"></div>
          
          <!-- 流程节点 -->
          <div
            v-for="node in flowNodes"
            :key="node.id"
            class="flow-node"
            :class="{ 
              'selected': selectedNodeId === node.id,
              [`node-${node.type.toLowerCase()}`]: true 
            }"
            :style="{
              left: node.position.x + 'px',
              top: node.position.y + 'px'
            }"
            @click.stop="selectNode(node.id)"
            @mousedown="startDrag($event, node)"
          >
            <div class="node-header">
              <el-icon :class="getNodeIcon(node.type)"></el-icon>
              <span class="node-title">{{ node.name }}</span>
              <el-button 
                class="node-delete"
                size="small"
                type="danger"
                text
                @click.stop="deleteNode(node.id)"
              >
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
            <div class="node-content">
              <div class="node-description">{{ getNodeDescription(node) }}</div>
            </div>
            
            <!-- 连接点 -->
            <div class="connection-points">
              <div 
                class="connection-point input"
                v-if="node.type !== 'START'"
                @mousedown.stop="startConnection($event, node.id, 'input')"
              ></div>
              <div 
                class="connection-point output"
                v-if="node.type !== 'END'"
                @mousedown.stop="startConnection($event, node.id, 'output')"
              ></div>
            </div>
          </div>

          <!-- 连接线 -->
          <svg class="connections-layer" :style="{ width: '100%', height: '100%' }">
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                      refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
              </marker>
            </defs>
            <path
              v-for="edge in flowEdges"
              :key="edge.id"
              :d="getEdgePath(edge)"
              class="connection-line"
              :class="{ 'selected': selectedEdgeId === edge.id }"
              marker-end="url(#arrowhead)"
              @click.stop="selectEdge(edge.id)"
            />
          </svg>

          <!-- 临时连接线 -->
          <svg v-if="tempConnection" class="temp-connection-layer">
            <path
              :d="tempConnection.path"
              class="temp-connection-line"
              stroke-dasharray="5,5"
            />
          </svg>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="property-panel">
        <div class="panel-header">
          <h3>属性配置</h3>
        </div>
        <div class="panel-content">
          <!-- 节点属性配置 -->
          <div v-if="selectedNode" class="property-form">
            <el-form :model="selectedNode" label-width="80px" size="small">
              <el-form-item label="节点名称">
                <el-input v-model="selectedNode.name" placeholder="请输入节点名称" />
              </el-form-item>
              
              <!-- 根据节点类型显示不同的配置项 -->
              <component 
                :is="getNodeConfigComponent(selectedNode.type)"
                v-model="selectedNode.config"
                :node="selectedNode"
              />
            </el-form>
          </div>

          <!-- 连接线属性配置 -->
          <div v-else-if="selectedEdge" class="property-form">
            <el-form :model="selectedEdge" label-width="80px" size="small">
              <el-form-item label="条件表达式">
                <el-input 
                  v-model="selectedEdge.condition" 
                  type="textarea"
                  placeholder="请输入条件表达式"
                  :rows="3"
                />
              </el-form-item>
            </el-form>
          </div>

          <!-- 流程属性配置 -->
          <div v-else class="property-form">
            <el-form :model="flowConfig" label-width="80px" size="small">
              <el-form-item label="流程名称">
                <el-input v-model="flowConfig.name" placeholder="请输入流程名称" />
              </el-form-item>
              <el-form-item label="流程描述">
                <el-input 
                  v-model="flowConfig.description" 
                  type="textarea"
                  placeholder="请输入流程描述"
                  :rows="3"
                />
              </el-form-item>
              <el-form-item label="超时时间">
                <el-input-number 
                  v-model="flowConfig.timeout" 
                  :min="1"
                  :max="3600"
                  placeholder="秒"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>

    <!-- 节点配置对话框 -->
    <el-dialog 
      v-model="nodeConfigVisible" 
      :title="'配置 - ' + (editingNode?.name || '')"
      width="60%"
      :before-close="handleConfigClose"
    >
      <component 
        v-if="editingNode"
        :is="getNodeConfigComponent(editingNode.type)"
        v-model="editingNode.config"
        :node="editingNode"
        :detailed="true"
      />
      <template #footer>
        <el-button @click="nodeConfigVisible = false">取消</el-button>
        <el-button type="primary" @click="saveNodeConfig">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FlowDesigner">
import { ref, reactive, computed, nextTick, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import {
  Connection, DocumentAdd, CircleCheck, View, VideoPlay,
  ZoomIn, ZoomOut, RefreshRight, FullScreen, Close
} from '@element-plus/icons-vue'

// 导入节点配置组件
import StartNodeConfig from './components/StartNodeConfig.vue'
import EndNodeConfig from './components/EndNodeConfig.vue'
import ApiCallNodeConfig from './components/ApiCallNodeConfig.vue'
import SqlQueryNodeConfig from './components/SqlQueryNodeConfig.vue'
import LoopNodeConfig from './components/LoopNodeConfig.vue'
import ConditionNodeConfig from './components/ConditionNodeConfig.vue'
import DataTransformNodeConfig from './components/DataTransformNodeConfig.vue'
import DataWriteNodeConfig from './components/DataWriteNodeConfig.vue'

const { proxy } = getCurrentInstance()

// 注册组件
const nodeConfigComponents = {
  StartNodeConfig,
  EndNodeConfig,
  ApiCallNodeConfig,
  SqlQueryNodeConfig,
  LoopNodeConfig,
  ConditionNodeConfig,
  DataTransformNodeConfig,
  DataWriteNodeConfig
}

// 响应式数据
const activeCollapse = ref(['basic', 'data', 'control'])
const canvasRef = ref(null)
const zoomLevel = ref(1)
const saving = ref(false)
const nodeConfigVisible = ref(false)
const editingNode = ref(null)

// 流程数据
const flowNodes = ref([])
const flowEdges = ref([])
const selectedNodeId = ref(null)
const selectedEdgeId = ref(null)
const tempConnection = ref(null)

// 流程配置
const flowConfig = reactive({
  name: '新建流程',
  description: '',
  timeout: 300
})

// 计算属性
const selectedNode = computed(() => {
  return flowNodes.value.find(node => node.id === selectedNodeId.value)
})

const selectedEdge = computed(() => {
  return flowEdges.value.find(edge => edge.id === selectedEdgeId.value)
})

// 节点类型定义
const basicNodes = ref([
  { type: 'START', name: '开始', icon: 'VideoPlay', description: '流程开始节点' },
  { type: 'END', name: '结束', icon: 'VideoPause', description: '流程结束节点' }
])

const dataNodes = ref([
  { type: 'API_CALL', name: 'API调用', icon: 'Link', description: '调用外部API接口' },
  { type: 'SQL_QUERY', name: 'SQL查询', icon: 'Search', description: '执行SQL查询' },
  { type: 'DATA_TRANSFORM', name: '数据转换', icon: 'Refresh', description: '数据格式转换' },
  { type: 'DATA_WRITE', name: '数据写入', icon: 'DocumentAdd', description: '写入目标数据库' }
])

const controlNodes = ref([
  { type: 'LOOP', name: '循环', icon: 'Refresh', description: '循环执行节点' },
  { type: 'CONDITION', name: '条件判断', icon: 'Switch', description: '条件分支节点' }
])

// 拖拽相关
let draggedNode = null
let isDragging = false
let dragOffset = { x: 0, y: 0 }
let isConnecting = false
let connectionStart = null

// 节点操作方法
function handleDragStart(event, nodeTemplate) {
  draggedNode = nodeTemplate
  event.dataTransfer.effectAllowed = 'copy'
}

function handleDragOver(event) {
  event.preventDefault()
  event.dataTransfer.dropEffect = 'copy'
}

function handleDrop(event) {
  event.preventDefault()
  if (!draggedNode) return

  const rect = canvasRef.value.getBoundingClientRect()
  const x = (event.clientX - rect.left) / zoomLevel.value
  const y = (event.clientY - rect.top) / zoomLevel.value

  const newNode = {
    id: generateId(),
    type: draggedNode.type,
    name: draggedNode.name,
    position: { x: x - 75, y: y - 40 }, // 居中放置
    config: getDefaultNodeConfig(draggedNode.type)
  }

  flowNodes.value.push(newNode)
  draggedNode = null

  // 自动选中新创建的节点
  selectNode(newNode.id)
}

function selectNode(nodeId) {
  selectedNodeId.value = nodeId
  selectedEdgeId.value = null
}

function selectEdge(edgeId) {
  selectedEdgeId.value = edgeId
  selectedNodeId.value = null
}

function handleCanvasClick() {
  selectedNodeId.value = null
  selectedEdgeId.value = null
}

function deleteNode(nodeId) {
  // 删除节点
  const nodeIndex = flowNodes.value.findIndex(node => node.id === nodeId)
  if (nodeIndex > -1) {
    flowNodes.value.splice(nodeIndex, 1)
  }

  // 删除相关连接线
  flowEdges.value = flowEdges.value.filter(edge =>
    edge.sourceNodeId !== nodeId && edge.targetNodeId !== nodeId
  )

  // 清除选中状态
  if (selectedNodeId.value === nodeId) {
    selectedNodeId.value = null
  }
}

// 节点拖拽移动
function startDrag(event, node) {
  if (event.target.closest('.node-delete') || event.target.closest('.connection-point')) {
    return
  }

  isDragging = true
  const rect = canvasRef.value.getBoundingClientRect()
  dragOffset.x = (event.clientX - rect.left) / zoomLevel.value - node.position.x
  dragOffset.y = (event.clientY - rect.top) / zoomLevel.value - node.position.y

  selectNode(node.id)

  document.addEventListener('mousemove', handleDragMove)
  document.addEventListener('mouseup', handleDragEnd)
}

function handleDragMove(event) {
  if (!isDragging || !selectedNode.value) return

  const rect = canvasRef.value.getBoundingClientRect()
  const x = (event.clientX - rect.left) / zoomLevel.value - dragOffset.x
  const y = (event.clientY - rect.top) / zoomLevel.value - dragOffset.y

  selectedNode.value.position.x = Math.max(0, x)
  selectedNode.value.position.y = Math.max(0, y)
}

function handleDragEnd() {
  isDragging = false
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
}

// 连接线相关方法
function startConnection(event, nodeId, portType) {
  event.stopPropagation()
  isConnecting = true
  connectionStart = { nodeId, portType }

  document.addEventListener('mousemove', handleConnectionMove)
  document.addEventListener('mouseup', handleConnectionEnd)
}

function handleConnectionMove(event) {
  if (!isConnecting || !connectionStart) return

  const rect = canvasRef.value.getBoundingClientRect()
  const x = (event.clientX - rect.left) / zoomLevel.value
  const y = (event.clientY - rect.top) / zoomLevel.value

  const startNode = flowNodes.value.find(node => node.id === connectionStart.nodeId)
  if (!startNode) return

  const startX = startNode.position.x + 75
  const startY = startNode.position.y + 40

  tempConnection.value = {
    path: `M ${startX} ${startY} L ${x} ${y}`
  }
}

function handleConnectionEnd(event) {
  if (!isConnecting) return

  // 检查是否连接到了目标节点
  const targetElement = event.target.closest('.flow-node')
  if (targetElement) {
    const targetNodeId = targetElement.getAttribute('data-node-id') ||
                        flowNodes.value.find(node =>
                          targetElement.textContent.includes(node.name)
                        )?.id

    if (targetNodeId && targetNodeId !== connectionStart.nodeId) {
      createConnection(connectionStart.nodeId, targetNodeId)
    }
  }

  // 清理临时连接
  isConnecting = false
  connectionStart = null
  tempConnection.value = null

  document.removeEventListener('mousemove', handleConnectionMove)
  document.removeEventListener('mouseup', handleConnectionEnd)
}

function createConnection(sourceNodeId, targetNodeId) {
  // 检查是否已存在连接
  const existingEdge = flowEdges.value.find(edge =>
    edge.sourceNodeId === sourceNodeId && edge.targetNodeId === targetNodeId
  )

  if (existingEdge) {
    proxy.$modal.msgWarning('连接已存在')
    return
  }

  const newEdge = {
    id: generateId(),
    sourceNodeId,
    targetNodeId,
    condition: ''
  }

  flowEdges.value.push(newEdge)
}

// 工具方法
function generateId() {
  return 'node_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

function getDefaultNodeConfig(nodeType) {
  const configs = {
    START: {},
    END: {},
    API_CALL: {
      url: '',
      method: 'GET',
      headers: {},
      params: {},
      timeout: 30000
    },
    SQL_QUERY: {
      dataSource: '',
      sql: '',
      parameters: {}
    },
    LOOP: {
      loopType: 'COUNT',
      count: 10,
      condition: '',
      loopVariable: 'i'
    },
    CONDITION: {
      condition: '',
      trueNodeId: '',
      falseNodeId: ''
    },
    DATA_TRANSFORM: {
      transformType: 'MAPPING',
      mappingRules: []
    },
    DATA_WRITE: {
      targetDatabase: '',
      targetTable: '',
      writeMode: 'APPEND',
      batchSize: 1000
    }
  }
  return configs[nodeType] || {}
}

function getNodeIcon(nodeType) {
  const icons = {
    START: 'VideoPlay',
    END: 'VideoPause',
    API_CALL: 'Link',
    SQL_QUERY: 'Search',
    LOOP: 'Refresh',
    CONDITION: 'Switch',
    DATA_TRANSFORM: 'Refresh',
    DATA_WRITE: 'DocumentAdd'
  }
  return icons[nodeType] || 'Document'
}

function getNodeDescription(node) {
  const descriptions = {
    START: '流程开始',
    END: '流程结束',
    API_CALL: node.config?.url || '未配置API地址',
    SQL_QUERY: node.config?.sql ? '已配置SQL' : '未配置SQL',
    LOOP: `循环类型: ${node.config?.loopType || '未配置'}`,
    CONDITION: node.config?.condition || '未配置条件',
    DATA_TRANSFORM: `转换类型: ${node.config?.transformType || '未配置'}`,
    DATA_WRITE: `目标表: ${node.config?.targetTable || '未配置'}`
  }
  return descriptions[node.type] || ''
}

function getNodeConfigComponent(nodeType) {
  const components = {
    START: StartNodeConfig,
    END: EndNodeConfig,
    API_CALL: ApiCallNodeConfig,
    SQL_QUERY: SqlQueryNodeConfig,
    LOOP: LoopNodeConfig,
    CONDITION: ConditionNodeConfig,
    DATA_TRANSFORM: DataTransformNodeConfig,
    DATA_WRITE: DataWriteNodeConfig
  }
  return components[nodeType] || 'div'
}

// 连接线路径计算
function getEdgePath(edge) {
  const sourceNode = flowNodes.value.find(node => node.id === edge.sourceNodeId)
  const targetNode = flowNodes.value.find(node => node.id === edge.targetNodeId)

  if (!sourceNode || !targetNode) return ''

  const startX = sourceNode.position.x + 150
  const startY = sourceNode.position.y + 40
  const endX = targetNode.position.x
  const endY = targetNode.position.y + 40

  // 计算贝塞尔曲线控制点
  const controlX1 = startX + (endX - startX) * 0.5
  const controlY1 = startY
  const controlX2 = startX + (endX - startX) * 0.5
  const controlY2 = endY

  return `M ${startX} ${startY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${endX} ${endY}`
}

// 画布操作方法
function zoomIn() {
  zoomLevel.value = Math.min(zoomLevel.value * 1.2, 3)
}

function zoomOut() {
  zoomLevel.value = Math.max(zoomLevel.value / 1.2, 0.2)
}

function resetZoom() {
  zoomLevel.value = 1
}

function fitCanvas() {
  // 计算所有节点的边界
  if (flowNodes.value.length === 0) return

  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity

  flowNodes.value.forEach(node => {
    minX = Math.min(minX, node.position.x)
    minY = Math.min(minY, node.position.y)
    maxX = Math.max(maxX, node.position.x + 150)
    maxY = Math.max(maxY, node.position.y + 80)
  })

  const canvasRect = canvasRef.value.getBoundingClientRect()
  const contentWidth = maxX - minX + 100
  const contentHeight = maxY - minY + 100

  const scaleX = canvasRect.width / contentWidth
  const scaleY = canvasRect.height / contentHeight

  zoomLevel.value = Math.min(scaleX, scaleY, 1)
}

// 流程操作方法
function saveFlow() {
  if (flowNodes.value.length === 0) {
    proxy.$modal.msgWarning('流程不能为空')
    return
  }

  const flowDefinition = {
    name: flowConfig.name,
    description: flowConfig.description,
    nodes: flowNodes.value,
    edges: flowEdges.value,
    config: {
      timeout: flowConfig.timeout
    }
  }

  saving.value = true

  // 这里调用保存API
  setTimeout(() => {
    saving.value = false
    proxy.$modal.msgSuccess('保存成功')
  }, 1000)
}

function validateFlow() {
  const errors = []

  // 检查是否有开始节点
  const startNodes = flowNodes.value.filter(node => node.type === 'START')
  if (startNodes.length === 0) {
    errors.push('缺少开始节点')
  } else if (startNodes.length > 1) {
    errors.push('只能有一个开始节点')
  }

  // 检查是否有结束节点
  const endNodes = flowNodes.value.filter(node => node.type === 'END')
  if (endNodes.length === 0) {
    errors.push('缺少结束节点')
  }

  // 检查节点连接
  flowNodes.value.forEach(node => {
    if (node.type !== 'START') {
      const hasInput = flowEdges.value.some(edge => edge.targetNodeId === node.id)
      if (!hasInput) {
        errors.push(`节点 "${node.name}" 没有输入连接`)
      }
    }

    if (node.type !== 'END') {
      const hasOutput = flowEdges.value.some(edge => edge.sourceNodeId === node.id)
      if (!hasOutput) {
        errors.push(`节点 "${node.name}" 没有输出连接`)
      }
    }
  })

  if (errors.length > 0) {
    proxy.$modal.msgError('验证失败：\n' + errors.join('\n'))
  } else {
    proxy.$modal.msgSuccess('验证通过')
  }
}

function previewFlow() {
  // 显示流程预览
  const flowDefinition = {
    name: flowConfig.name,
    nodes: flowNodes.value,
    edges: flowEdges.value
  }

  console.log('流程定义:', flowDefinition)
  proxy.$modal.msgInfo('请查看控制台输出')
}

function executeFlow() {
  if (flowNodes.value.length === 0) {
    proxy.$modal.msgWarning('流程不能为空')
    return
  }

  proxy.$modal.confirm('确定要执行此流程吗？', '确认执行', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 这里调用执行API
    proxy.$modal.msgSuccess('流程执行已启动')
  })
}

// 节点配置相关
function openNodeConfig(node) {
  editingNode.value = { ...node }
  nodeConfigVisible.value = true
}

function saveNodeConfig() {
  if (editingNode.value) {
    const nodeIndex = flowNodes.value.findIndex(node => node.id === editingNode.value.id)
    if (nodeIndex > -1) {
      flowNodes.value[nodeIndex] = { ...editingNode.value }
    }
  }
  nodeConfigVisible.value = false
  editingNode.value = null
}

function handleConfigClose() {
  nodeConfigVisible.value = false
  editingNode.value = null
}

// 生命周期
onMounted(() => {
  // 初始化画布
  nextTick(() => {
    // 添加一些示例节点
    const startNode = {
      id: generateId(),
      type: 'START',
      name: '开始',
      position: { x: 100, y: 100 },
      config: {}
    }

    flowNodes.value.push(startNode)
  })
})

onUnmounted(() => {
  // 清理事件监听
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
  document.removeEventListener('mousemove', handleConnectionMove)
  document.removeEventListener('mouseup', handleConnectionEnd)
})
</script>

<style scoped>
.flow-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

/* 顶部工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 设计器容器 */
.designer-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧节点面板 */
.node-panel {
  width: 250px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.node-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  padding: 8px 0;
}

.node-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: white;
  cursor: grab;
  transition: all 0.3s ease;
  user-select: none;
}

.node-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.node-item:active {
  cursor: grabbing;
}

.node-item .el-icon {
  font-size: 20px;
  color: #409eff;
  margin-bottom: 4px;
}

.node-item span {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

/* 中间画布区域 */
.canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
}

.canvas-tools {
  display: flex;
  align-items: center;
  gap: 12px;
}

.zoom-level {
  font-size: 12px;
  color: #909399;
  min-width: 40px;
  text-align: center;
}

.flow-canvas {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: white;
  transform-origin: top left;
  transition: transform 0.3s ease;
}

/* 网格背景 */
.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(to right, #f0f0f0 1px, transparent 1px),
    linear-gradient(to bottom, #f0f0f0 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
}

/* 流程节点 */
.flow-node {
  position: absolute;
  width: 150px;
  min-height: 80px;
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: move;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.flow-node:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.flow-node.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.node-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 6px 6px 0 0;
}

.node-header .el-icon {
  font-size: 16px;
  color: #409eff;
  margin-right: 6px;
}

.node-title {
  flex: 1;
  font-size: 12px;
  font-weight: 600;
  color: #303133;
}

.node-delete {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.flow-node:hover .node-delete {
  opacity: 1;
}

.node-content {
  padding: 8px 12px;
}

.node-description {
  font-size: 11px;
  color: #909399;
  line-height: 1.4;
}

/* 连接点 */
.connection-points {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connection-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #409eff;
  border: 2px solid white;
  border-radius: 50%;
  cursor: crosshair;
  pointer-events: all;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.flow-node:hover .connection-point {
  opacity: 1;
}

.connection-point.input {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.connection-point.output {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

/* 连接线 */
.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.connection-line {
  fill: none;
  stroke: #666;
  stroke-width: 2;
  pointer-events: stroke;
  cursor: pointer;
  transition: stroke 0.3s ease;
}

.connection-line:hover,
.connection-line.selected {
  stroke: #409eff;
  stroke-width: 3;
}

.temp-connection-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.temp-connection-line {
  fill: none;
  stroke: #409eff;
  stroke-width: 2;
  opacity: 0.6;
}

/* 右侧属性面板 */
.property-panel {
  width: 300px;
  background: white;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.property-form {
  padding: 16px;
}

/* 节点类型样式 */
.node-start {
  border-color: #67c23a;
}

.node-start .node-header {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
}

.node-start .node-header .el-icon {
  color: white;
}

.node-end {
  border-color: #f56c6c;
}

.node-end .node-header {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  color: white;
}

.node-end .node-header .el-icon {
  color: white;
}

.node-api_call {
  border-color: #409eff;
}

.node-sql_query {
  border-color: #e6a23c;
}

.node-loop {
  border-color: #9c27b0;
}

.node-condition {
  border-color: #ff9800;
}

.node-data_transform {
  border-color: #00bcd4;
}

.node-data_write {
  border-color: #4caf50;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .node-panel {
    width: 200px;
  }

  .property-panel {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .designer-container {
    flex-direction: column;
  }

  .node-panel,
  .property-panel {
    width: 100%;
    height: 200px;
  }

  .canvas-container {
    flex: 1;
    min-height: 400px;
  }
}
</style>
