package com.lacus.service.datacollection.engine;

import com.lacus.service.datacollection.model.FlowContext;
import com.lacus.service.datacollection.model.FlowDefinition;

/**
 * 节点执行器接口
 *
 * <AUTHOR>
 */
public interface NodeExecutor {

    /**
     * 获取支持的节点类型
     *
     * @return 节点类型
     */
    String getSupportedNodeType();

    /**
     * 执行节点
     *
     * @param node 节点定义
     * @param context 流程上下文
     * @return 执行结果
     */
    NodeExecutionResult execute(FlowDefinition.FlowNode node, FlowContext context);

    /**
     * 验证节点配置
     *
     * @param node 节点定义
     * @return 验证结果
     */
    ValidationResult validate(FlowDefinition.FlowNode node);

    /**
     * 节点执行结果
     */
    class NodeExecutionResult {
        private String status; // SUCCESS, FAILED, SKIP
        private String message;
        private Object result;
        private String nextNodeId; // 下一个要执行的节点ID（用于条件分支）

        public NodeExecutionResult(String status, String message, Object result) {
            this.status = status;
            this.message = message;
            this.result = result;
        }

        public NodeExecutionResult(String status, String message, Object result, String nextNodeId) {
            this.status = status;
            this.message = message;
            this.result = result;
            this.nextNodeId = nextNodeId;
        }

        // Getters and Setters
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Object getResult() { return result; }
        public void setResult(Object result) { this.result = result; }
        public String getNextNodeId() { return nextNodeId; }
        public void setNextNodeId(String nextNodeId) { this.nextNodeId = nextNodeId; }
    }

    /**
     * 验证结果
     */
    class ValidationResult {
        private boolean valid;
        private String message;

        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
