import request from '@/utils/request'

// 查询数据采集分类列表
export function listDataCollectionCategory(query) {
  return request({
    url: '/datacollection/category/list',
    method: 'get',
    params: query
  })
}

// 查询数据采集分类详细
export function getDataCollectionCategory(categoryId) {
  return request({
    url: '/datacollection/category/' + categoryId,
    method: 'get'
  })
}

// 新增数据采集分类
export function addDataCollectionCategory(data) {
  return request({
    url: '/datacollection/category',
    method: 'post',
    data: data
  })
}

// 修改数据采集分类
export function updateDataCollectionCategory(data) {
  return request({
    url: '/datacollection/category',
    method: 'put',
    data: data
  })
}

// 删除数据采集分类
export function delDataCollectionCategory(categoryId) {
  return request({
    url: '/datacollection/category/' + categoryId,
    method: 'delete'
  })
}

// 获取分类树形结构
export function getCategoryTree() {
  return request({
    url: '/datacollection/category/tree',
    method: 'get'
  })
}

// 移动分类
export function moveCategory(data) {
  return request({
    url: '/datacollection/category/move',
    method: 'put',
    data: data
  })
}

// 批量删除分类
export function batchDelCategory(categoryIds) {
  return request({
    url: '/datacollection/category/batch',
    method: 'delete',
    data: categoryIds
  })
}

// 检查分类编码是否唯一
export function checkCategoryCode(categoryCode, categoryId) {
  return request({
    url: '/datacollection/category/checkCode',
    method: 'get',
    params: {
      categoryCode,
      categoryId
    }
  })
}

// 获取分类统计信息
export function getCategoryStats(categoryId) {
  return request({
    url: '/datacollection/category/stats/' + categoryId,
    method: 'get'
  })
}
