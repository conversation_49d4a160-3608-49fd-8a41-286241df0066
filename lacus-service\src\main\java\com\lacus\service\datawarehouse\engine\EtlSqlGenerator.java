package com.lacus.service.datawarehouse.engine;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lacus.dao.datawarehouse.entity.EtlFieldMappingEntity;
import com.lacus.dao.datawarehouse.entity.EtlQualityRulesEntity;
import com.lacus.dao.datawarehouse.entity.EtlSourceTableEntity;
import com.lacus.dao.datawarehouse.mapper.EtlFieldMappingMapper;
import com.lacus.dao.datawarehouse.mapper.EtlQualityRulesMapper;
import com.lacus.dao.datawarehouse.mapper.EtlSourceTableMapper;
import com.lacus.service.datawarehouse.dto.EtlFieldMappingDTO;
import com.lacus.service.datawarehouse.dto.EtlQualityRulesDTO;
import com.lacus.service.datawarehouse.dto.EtlSourceTableDTO;
import com.lacus.service.datawarehouse.model.EtlTaskModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
/**
 * ETL SQL生成器
 * 根据ETL任务配置生成对应的SQL语句
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class EtlSqlGenerator {

    @Autowired
    private EtlSourceTableMapper etlSourceTableMapper;

    @Autowired
    private EtlFieldMappingMapper etlFieldMappingMapper;

    @Autowired
    private EtlQualityRulesMapper etlQualityRulesMapper;

    /**
     * 生成ETL SQL语句
     */
    public List<String> generateSql(EtlTaskModel taskModel) {
        List<String> sqlStatements = new ArrayList<>();

        log.info("开始生成ETL SQL: taskId={}, sourceLayer={}, targetLayer={}",
                taskModel.getTaskId(), taskModel.getSourceLayer(), taskModel.getTargetLayer());

        try {
            // 从数据库加载配置数据
            loadTaskConfigFromDatabase(taskModel);

            // 验证配置
            validateTaskConfig(taskModel);

            // 根据写入模式生成不同的SQL
            String writeMode = taskModel.getWriteMode();

            switch (writeMode) {
                case "OVERWRITE":
                    sqlStatements.addAll(generateOverwriteSql(taskModel));
                    break;
                case "APPEND":
                    sqlStatements.addAll(generateAppendSql(taskModel));
                    break;
                case "UPSERT":
                    sqlStatements.addAll(generateUpsertSql(taskModel));
                    break;
                case "AUTO_CREATE":
                    sqlStatements.addAll(generateAutoCreateSql(taskModel));
                    break;
                default:
                    throw new IllegalArgumentException("不支持的写入模式: " + writeMode);
            }

            log.info("ETL SQL生成完成: taskId={}, sqlCount={}", taskModel.getTaskId(), sqlStatements.size());

        } catch (Exception e) {
            log.error("ETL SQL生成失败: taskId={}", taskModel.getTaskId(), e);
            throw new RuntimeException("SQL生成失败: " + e.getMessage(), e);
        }

        return sqlStatements;
    }

    /**
     * 从数据库加载任务配置
     */
    private void loadTaskConfigFromDatabase(EtlTaskModel taskModel) {
        Long taskId = taskModel.getTaskId();

        // 加载源表配置
        List<EtlSourceTableEntity> sourceTables = etlSourceTableMapper.selectList(
                new QueryWrapper<EtlSourceTableEntity>().eq("etl_task_id", taskId));

        // 加载字段映射配置
        List<EtlFieldMappingEntity> fieldMappings = etlFieldMappingMapper.selectList(
                new QueryWrapper<EtlFieldMappingEntity>().eq("etl_task_id", taskId));

        // 加载质量规则配置
        List<EtlQualityRulesEntity> qualityRules = etlQualityRulesMapper.selectList(
                new QueryWrapper<EtlQualityRulesEntity>().eq("etl_task_id", taskId));

        // 转换为模型对象
        taskModel.setSourceTables(convertSourceTables(sourceTables));
        taskModel.setFieldMappings(convertFieldMappings(fieldMappings));
        taskModel.setQualityRules(convertQualityRules(qualityRules));

        log.info("加载任务配置完成: taskId={}, sourceTables={}, fieldMappings={}, qualityRules={}",
                taskId, sourceTables.size(), fieldMappings.size(), qualityRules.size());
    }

    /**
     * 转换源表实体为模型
     */
    private List<EtlSourceTableDTO> convertSourceTables(List<EtlSourceTableEntity> entities) {
        return entities.stream().map(entity -> {
            EtlSourceTableDTO config = new EtlSourceTableDTO();
            config.setTableName(entity.getTableName());
            config.setAlias(entity.getAlias());
            config.setWhereCondition(entity.getWhereCondition());
            return config;
        }).collect(Collectors.toList());
    }

    /**
     * 转换字段映射实体为模型
     */
    private List<EtlFieldMappingDTO> convertFieldMappings(List<EtlFieldMappingEntity> entities) {
        return entities.stream().map(entity -> {
            EtlFieldMappingDTO config = new EtlFieldMappingDTO();
            config.setTargetField(entity.getTargetField());
            config.setSourceField(entity.getSourceField());
            config.setSourceTable(entity.getSourceTable());
            config.setFieldType(entity.getFieldType());
            config.setExpression(entity.getExpression());
            config.setDefaultValue(entity.getDefaultValue());
            return config;
        }).collect(Collectors.toList());
    }

    /**
     * 转换质量规则实体为模型
     */
    private List<EtlQualityRulesDTO> convertQualityRules(List<EtlQualityRulesEntity> entities) {
        return entities.stream().map(entity -> {
            EtlQualityRulesDTO config = new EtlQualityRulesDTO();
            config.setRuleName(entity.getRuleName());
            config.setRuleType(entity.getRuleType());
            config.setCheckField(entity.getCheckField());
            config.setRuleConfig(entity.getRuleConfig());
            config.setSeverity(entity.getSeverity());
            return config;
        }).collect(Collectors.toList());
    }

    /**
     * 验证任务配置
     */
    private void validateTaskConfig(EtlTaskModel taskModel) {
        if (taskModel.getSourceTables() == null || taskModel.getSourceTables().isEmpty()) {
            throw new IllegalArgumentException("源表配置不能为空");
        }

        if (taskModel.getFieldMappings() == null || taskModel.getFieldMappings().isEmpty()) {
            throw new IllegalArgumentException("字段映射配置不能为空");
        }

        if (!StringUtils.hasText(taskModel.getTargetTable())) {
            throw new IllegalArgumentException("目标表名不能为空");
        }
    }

    /**
     * 生成覆盖写入SQL
     */
    private List<String> generateOverwriteSql(EtlTaskModel taskModel) {
        List<String> sqlStatements = new ArrayList<>();

        // 1. 删除目标表数据（如果需要）
        if (needTruncateTarget(taskModel)) {
            sqlStatements.add(generateTruncateSql(taskModel));
        }

        // 2. 生成INSERT SQL
        sqlStatements.add(generateInsertSql(taskModel));

        return sqlStatements;
    }

    /**
     * 生成追加写入SQL
     */
    private List<String> generateAppendSql(EtlTaskModel taskModel) {
        List<String> sqlStatements = new ArrayList<>();

        // 直接生成INSERT SQL
        sqlStatements.add(generateInsertSql(taskModel));

        return sqlStatements;
    }

    /**
     * 生成更新插入SQL
     */
    private List<String> generateUpsertSql(EtlTaskModel taskModel) {
        List<String> sqlStatements = new ArrayList<>();

        // Doris支持的UPSERT语法
        sqlStatements.add(generateUpsertInsertSql(taskModel));

        return sqlStatements;
    }

    /**
     * 生成自动创建表SQL
     */
    private List<String> generateAutoCreateSql(EtlTaskModel taskModel) {
        List<String> sqlStatements = new ArrayList<>();

        // 1. 创建表SQL（如果不存在）
        sqlStatements.add(generateCreateTableSql(taskModel));

        // 2. 插入数据SQL
        sqlStatements.add(generateInsertSql(taskModel));

        return sqlStatements;
    }

    /**
     * 生成TRUNCATE SQL
     */
    private String generateTruncateSql(EtlTaskModel taskModel) {
        return String.format("TRUNCATE TABLE %s.%s",
                getTargetDatabase(taskModel), taskModel.getTargetTable());
    }

    /**
     * 生成CREATE TABLE SQL
     */
    private String generateCreateTableSql(EtlTaskModel taskModel) {
        StringBuilder sql = new StringBuilder();

        sql.append("CREATE TABLE IF NOT EXISTS ").append(getTargetDatabase(taskModel))
                .append(".").append(taskModel.getTargetTable()).append(" (\n");

        // 根据字段映射生成字段定义
        List<String> fieldDefinitions = taskModel.getFieldMappings().stream()
                .map(mapping -> String.format("  %s %s",
                        mapping.getTargetField(),
                        convertFieldType(mapping.getFieldType())))
                .collect(Collectors.toList());

        sql.append(String.join(",\n", fieldDefinitions));
        sql.append("\n) ENGINE=OLAP\n");
        sql.append("DISTRIBUTED BY HASH(").append(getFirstField(taskModel)).append(") BUCKETS 10\n");
        sql.append("PROPERTIES (\n");
        sql.append("  \"replication_num\" = \"1\"\n");
        sql.append(")");

        return sql.toString();
    }

    /**
     * 转换字段类型为Doris类型
     */
    private String convertFieldType(String fieldType) {
        if (fieldType == null) {
            return "VARCHAR(255)";
        }

        switch (fieldType.toUpperCase()) {
            case "BIGINT":
                return "BIGINT";
            case "STRING":
                return "VARCHAR(255)";
            case "DATETIME":
                return "DATETIME";
            case "INT":
                return "INT";
            case "DECIMAL":
                return "DECIMAL(18,2)";
            default:
                return "VARCHAR(255)";
        }
    }

    /**
     * 获取第一个字段作为分桶字段
     */
    private String getFirstField(EtlTaskModel taskModel) {
        if (taskModel.getFieldMappings() != null && !taskModel.getFieldMappings().isEmpty()) {
            return taskModel.getFieldMappings().get(0).getTargetField();
        }
        return "id";
    }

    /**
     * 生成INSERT SQL
     */
    private String generateInsertSql(EtlTaskModel taskModel) {
        StringBuilder sql = new StringBuilder();

        // INSERT INTO 目标表
        sql.append("INSERT INTO ").append(getTargetDatabase(taskModel))
                .append(".").append(taskModel.getTargetTable());

        // 字段列表
        List<String> targetFields = getTargetFields(taskModel);
        sql.append(" (").append(String.join(", ", targetFields)).append(")");

        // SELECT 子句
        sql.append("\nSELECT ");
        sql.append(generateSelectFields(taskModel));

        // FROM 子句
        sql.append("\nFROM ");
        sql.append(generateFromClause(taskModel));

        // WHERE 子句
        String whereClause = generateWhereClause(taskModel);
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append("\nWHERE ").append(whereClause);
        }

        return sql.toString();
    }

    /**
     * 生成UPSERT INSERT SQL
     */
    private String generateUpsertInsertSql(EtlTaskModel taskModel) {
        StringBuilder sql = new StringBuilder();

        // INSERT INTO 目标表
        sql.append("INSERT INTO ").append(getTargetDatabase(taskModel))
                .append(".").append(taskModel.getTargetTable());

        // 字段列表
        List<String> targetFields = getTargetFields(taskModel);
        sql.append(" (").append(String.join(", ", targetFields)).append(")");

        // SELECT 子句
        sql.append("\nSELECT ");
        sql.append(generateSelectFields(taskModel));

        // FROM 子句
        sql.append("\nFROM ");
        sql.append(generateFromClause(taskModel));

        // WHERE 子句
        String whereClause = generateWhereClause(taskModel);
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append("\nWHERE ").append(whereClause);
        }

        // ON DUPLICATE KEY UPDATE（Doris语法）
        sql.append("\nON DUPLICATE KEY UPDATE ");
        sql.append(generateUpdateFields(taskModel));

        return sql.toString();
    }

    /**
     * 生成SELECT字段
     */
    private String generateSelectFields(EtlTaskModel taskModel) {
        List<String> selectFields = new ArrayList<>();

        for (EtlFieldMappingDTO mapping : taskModel.getFieldMappings()) {
            String selectField = generateSingleSelectField(mapping);
            selectFields.add(selectField);
        }

        return String.join(",\n  ", selectFields);
    }

    /**
     * 生成单个SELECT字段
     */
    private String generateSingleSelectField(EtlFieldMappingDTO mapping) {
        String targetField = mapping.getTargetField();
        String sourceField = mapping.getSourceField();
        String sourceTable = mapping.getSourceTable();
        String expression = mapping.getExpression();
        String defaultValue = mapping.getDefaultValue();

        // 如果有转换表达式，直接使用
        if (StringUtils.hasText(expression)) {
            return String.format("%s AS %s", expression, targetField);
        }

        // 如果有源字段，使用源字段
        if (StringUtils.hasText(sourceField)) {
            String fullSourceField = sourceField;
            if (StringUtils.hasText(sourceTable)) {
                fullSourceField = sourceTable + "." + sourceField;
            }
            return String.format("%s AS %s", fullSourceField, targetField);
        }

        // 如果有默认值，使用默认值
        if (StringUtils.hasText(defaultValue)) {
            return String.format("'%s' AS %s", defaultValue, targetField);
        }

        // 否则使用NULL
        return String.format("NULL AS %s", targetField);
    }

    /**
     * 生成FROM子句
     */
    private String generateFromClause(EtlTaskModel taskModel) {
        if (taskModel.getSourceTables() == null || taskModel.getSourceTables().isEmpty()) {
            throw new IllegalArgumentException("源表配置不能为空");
        }

        StringBuilder fromClause = new StringBuilder();
        String sourceDatabase = getSourceDatabase(taskModel);

        // 主表
        EtlSourceTableDTO mainTable = taskModel.getSourceTables().get(0);
        fromClause.append(sourceDatabase).append(".").append(mainTable.getTableName());

        if (StringUtils.hasText(mainTable.getAlias())) {
            fromClause.append(" AS ").append(mainTable.getAlias());
        }

        // 关联表（如果有多个源表）
        for (int i = 1; i < taskModel.getSourceTables().size(); i++) {
            EtlSourceTableDTO joinTable = taskModel.getSourceTables().get(i);
            fromClause.append("\nLEFT JOIN ").append(sourceDatabase)
                    .append(".").append(joinTable.getTableName());

            if (StringUtils.hasText(joinTable.getAlias())) {
                fromClause.append(" AS ").append(joinTable.getAlias());
            }

            // TODO: 添加JOIN条件配置
        }

        return fromClause.toString();
    }

    /**
     * 生成WHERE子句
     */
    private String generateWhereClause(EtlTaskModel taskModel) {
        List<String> conditions = new ArrayList<>();

        // 添加源表的WHERE条件
        if (taskModel.getSourceTables() != null) {
            for (EtlSourceTableDTO sourceTable : taskModel.getSourceTables()) {
                if (StringUtils.hasText(sourceTable.getWhereCondition())) {
                    conditions.add("(" + sourceTable.getWhereCondition() + ")");
                }
            }
        }

        // 添加增量同步条件
        String incrementalCondition = generateIncrementalCondition(taskModel);
        if (StringUtils.hasText(incrementalCondition)) {
            conditions.add(incrementalCondition);
        }

        return conditions.isEmpty() ? null : String.join(" AND ", conditions);
    }

    /**
     * 生成增量同步条件
     */
    private String generateIncrementalCondition(EtlTaskModel taskModel) {
        // TODO: 根据任务配置生成增量同步条件
        // 例如：基于时间戳、版本号等
        return null;
    }

    /**
     * 生成UPDATE字段（用于UPSERT）
     */
    private String generateUpdateFields(EtlTaskModel taskModel) {
        List<String> targetFields = getTargetFields(taskModel);

        return targetFields.stream()
                .filter(field -> !isPrimaryKey(field, taskModel))
                .map(field -> String.format("%s = VALUES(%s)", field, field))
                .collect(Collectors.joining(", "));
    }

    /**
     * 获取目标字段列表
     */
    private List<String> getTargetFields(EtlTaskModel taskModel) {
        if (taskModel.getFieldMappings() == null || taskModel.getFieldMappings().isEmpty()) {
            throw new IllegalArgumentException("字段映射配置不能为空");
        }

        return taskModel.getFieldMappings().stream()
                .map(EtlFieldMappingDTO::getTargetField)
                .collect(Collectors.toList());
    }

    /**
     * 获取源数据库名
     */
    private String getSourceDatabase(EtlTaskModel taskModel) {
        return taskModel.getSourceLayer();
    }

    /**
     * 获取目标数据库名
     */
    private String getTargetDatabase(EtlTaskModel taskModel) {
        return taskModel.getTargetLayer();
    }

    /**
     * 判断是否需要清空目标表
     */
    private boolean needTruncateTarget(EtlTaskModel taskModel) {
        // 全量覆盖模式需要清空目标表
        return "OVERWRITE".equals(taskModel.getWriteMode());
    }

    /**
     * 判断是否为主键字段
     */
    private boolean isPrimaryKey(String field, EtlTaskModel taskModel) {
        // TODO: 实现主键判断逻辑，可以从taskModel.getPrimaryKeys()中获取
        return false;
    }
}