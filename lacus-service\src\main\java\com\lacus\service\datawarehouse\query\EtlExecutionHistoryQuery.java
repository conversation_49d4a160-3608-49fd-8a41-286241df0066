package com.lacus.service.datawarehouse.query;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lacus.dao.datawarehouse.entity.EtlExecutionHistoryEntity;
import com.lacus.dao.system.query.AbstractPageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.ObjectUtils;

/**
 * ETL任务历史查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EtlExecutionHistoryQuery extends AbstractPageQuery {

    /**
     * 执行ID
     */
    private String executionId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 执行状态
     */
    private String status;

    /**
     * 开始时间
     */
    private DateTime startTime;

    /**
     * 结束时间
     */
    private DateTime endTime;

    @Override
    public QueryWrapper<EtlExecutionHistoryEntity> toQueryWrapper() {
        QueryWrapper<EtlExecutionHistoryEntity> wrapper = new QueryWrapper<>();
        wrapper.like(ObjectUtils.isNotEmpty(taskName), "task_name", taskName);
        wrapper.eq(ObjectUtils.isNotEmpty(status), "status", status);
        // 添加时间范围筛选逻辑
        if (ObjectUtils.isNotEmpty(startTime)) {
            wrapper.ge("start_time", startTime);
        }
        if (ObjectUtils.isNotEmpty(endTime)) {
            wrapper.le("end_time", endTime);
        }
        wrapper.orderByDesc("execution_id");
        return wrapper;
    }
}
