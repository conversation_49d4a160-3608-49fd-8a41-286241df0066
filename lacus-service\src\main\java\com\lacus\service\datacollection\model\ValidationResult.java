package com.lacus.service.datacollection.model;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.ArrayList;

/**
 * 验证结果类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ValidationResult {
    
    /**
     * 验证是否通过
     */
    private boolean valid;
    
    /**
     * 验证消息
     */
    private String message;
    
    /**
     * 错误详情列表
     */
    private List<String> errors;
    
    /**
     * 构造函数 - 只有验证结果和消息
     */
    public ValidationResult(boolean valid, String message) {
        this.valid = valid;
        this.message = message;
        this.errors = new ArrayList<>();
    }
    
    /**
     * 创建成功的验证结果
     */
    public static ValidationResult success() {
        return new ValidationResult(true, "验证通过");
    }
    
    /**
     * 创建成功的验证结果，带消息
     */
    public static ValidationResult success(String message) {
        return new ValidationResult(true, message);
    }
    
    /**
     * 创建失败的验证结果
     */
    public static ValidationResult failure(String message) {
        return new ValidationResult(false, message);
    }
    
    /**
     * 创建失败的验证结果，带错误列表
     */
    public static ValidationResult failure(String message, List<String> errors) {
        return new ValidationResult(false, message, errors);
    }
    
    /**
     * 添加错误信息
     */
    public void addError(String error) {
        if (this.errors == null) {
            this.errors = new ArrayList<>();
        }
        this.errors.add(error);
        this.valid = false;
    }
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * 获取错误数量
     */
    public int getErrorCount() {
        return errors != null ? errors.size() : 0;
    }
}
