package com.lacus.admin.controller.datacollection.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据采集分类VO
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class DataCollectionCategoryVO {

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    @Size(min = 2, max = 100, message = "分类名称长度必须在2-100个字符之间")
    private String categoryName;

    /**
     * 分类编码
     */
    @NotBlank(message = "分类编码不能为空")
    @Size(min = 2, max = 50, message = "分类编码长度必须在2-50个字符之间")
    private String categoryCode;

    /**
     * 父分类ID
     */
    private Long parentId;

    /**
     * 分类路径
     */
    private String categoryPath;

    /**
     * 分类层级
     */
    private Integer categoryLevel;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 分类图标
     */
    private String categoryIcon;

    /**
     * 分类描述
     */
    private String categoryDesc;

    /**
     * 状态(0:禁用 1:启用)
     */
    private Integer status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 子分类列表
     */
    private List<DataCollectionCategoryVO> children;

    /**
     * 任务数量
     */
    private Integer taskCount;

    /**
     * 分类完整名称
     */
    private String fullName;

    /**
     * 是否有子分类
     */
    private Boolean hasChildren;

    /**
     * 是否可以删除
     */
    private Boolean canDelete;

    /**
     * 父分类名称
     */
    private String parentName;

    /**
     * 状态文本
     */
    private String statusText;

    /**
     * 分类层级文本
     */
    private String levelText;
}
