package com.lacus.admin.service.datacollection;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lacus.admin.controller.datacollection.vo.DataCollectionCategoryPageQuery;
import com.lacus.admin.controller.datacollection.vo.DataCollectionCategoryTreeVO;
import com.lacus.admin.controller.datacollection.vo.DataCollectionCategoryVO;

import java.util.List;
import java.util.Map;

/**
 * 数据采集分类Service接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IDataCollectionCategoryService {
    /**
     * 分页查询数据采集分类列表
     *
     * @param pageQuery 分页查询参数
     * @return 分类列表
     */
    IPage<DataCollectionCategoryVO> selectCategoryPage(DataCollectionCategoryPageQuery pageQuery);

    /**
     * 查询数据采集分类
     *
     * @param categoryId 数据采集分类主键
     * @return 数据采集分类
     */
    DataCollectionCategoryVO selectCategoryById(Long categoryId);

    /**
     * 查询分类树形结构
     *
     * @param onlyEnabled 是否只查询启用状态
     * @return 分类树
     */
    List<DataCollectionCategoryTreeVO> selectCategoryTree(Boolean onlyEnabled);

    /**
     * 根据父级ID查询子分类列表
     *
     * @param parentId 父级ID
     * @param onlyEnabled 是否只查询启用状态
     * @return 子分类列表
     */
    List<DataCollectionCategoryVO> selectCategoryByParentId(Long parentId, Boolean onlyEnabled);

    /**
     * 新增数据采集分类
     *
     * @param categoryVO 数据采集分类
     * @return 结果
     */
    boolean insertCategory(DataCollectionCategoryVO categoryVO);

    /**
     * 修改数据采集分类
     *
     * @param categoryVO 数据采集分类
     * @return 结果
     */
    boolean updateCategory(DataCollectionCategoryVO categoryVO);

    /**
     * 批量删除数据采集分类
     *
     * @param categoryIds 需要删除的数据采集分类主键集合
     * @return 结果
     */
    boolean deleteCategoryByIds(List<Long> categoryIds);

    /**
     * 删除数据采集分类信息
     *
     * @param categoryId 数据采集分类主键
     * @return 结果
     */
    boolean deleteCategoryById(Long categoryId);

    /**
     * 检查分类编码是否唯一
     *
     * @param categoryCode 分类编码
     * @param categoryId 分类ID（排除自己）
     * @return 结果
     */
    boolean checkCategoryCodeUnique(String categoryCode, Long categoryId);

    /**
     * 检查分类名称是否唯一
     *
     * @param categoryName 分类名称
     * @param parentId 父级ID
     * @param categoryId 分类ID（排除自己）
     * @return 结果
     */
    boolean checkCategoryNameUnique(String categoryName, Long parentId, Long categoryId);

    /**
     * 检查是否存在子分类
     *
     * @param categoryId 分类ID
     * @return 结果
     */
    boolean hasChildCategory(Long categoryId);

    /**
     * 检查分类下是否存在任务
     *
     * @param categoryId 分类ID
     * @return 结果
     */
    boolean hasTask(Long categoryId);

    /**
     * 移动分类
     *
     * @param categoryId 分类ID
     * @param targetParentId 目标父级ID
     * @return 结果
     */
    boolean moveCategory(Long categoryId, Long targetParentId);

    /**
     * 获取分类统计信息
     *
     * @param categoryId 分类ID
     * @return 统计信息
     */
    Map<String, Object> getCategoryStats(Long categoryId);

    /**
     * 批量更新分类状态
     *
     * @param categoryIds 分类ID列表
     * @param status 状态
     * @return 结果
     */
    boolean batchUpdateStatus(List<Long> categoryIds, Integer status);

    /**
     * 构建分类路径
     *
     * @param categoryId 分类ID
     * @return 分类路径
     */
    String buildCategoryPath(Long categoryId);

    /**
     * 更新分类路径
     *
     * @param categoryId 分类ID
     * @return 结果
     */
    boolean updateCategoryPath(Long categoryId);

    /**
     * 获取分类层级
     *
     * @param parentId 父级ID
     * @return 层级
     */
    int getCategoryLevel(Long parentId);

    /**
     * 获取分类的所有子分类ID
     *
     * @param categoryId 分类ID
     * @return 子分类ID列表
     */
    List<Long> getChildCategoryIds(Long categoryId);

    /**
     * 根据分类路径查询分类
     *
     * @param categoryPath 分类路径
     * @return 分类信息
     */
    DataCollectionCategoryVO selectCategoryByPath(String categoryPath);

    /**
     * 获取分类的完整路径名称
     *
     * @param categoryId 分类ID
     * @return 完整路径名称
     */
    String getCategoryFullName(Long categoryId);

    /**
     * 检查分类是否可以删除
     *
     * @param categoryId 分类ID
     * @return 结果
     */
    boolean canDeleteCategory(Long categoryId);

    /**
     * 获取分类的任务数量统计
     *
     * @param categoryId 分类ID
     * @return 任务数量
     */
    int getTaskCountByCategory(Long categoryId);

    /**
     * 获取所有启用的分类
     *
     * @return 分类列表
     */
    List<DataCollectionCategoryVO> selectEnabledCategories();

    /**
     * 根据分类编码查询分类
     *
     * @param categoryCode 分类编码
     * @return 分类信息
     */
    DataCollectionCategoryVO selectCategoryByCode(String categoryCode);

    /**
     * 根据分类名称模糊查询
     *
     * @param categoryName 分类名称
     * @param onlyEnabled 是否只查询启用状态
     * @return 分类列表
     */
    List<DataCollectionCategoryVO> searchCategoryByName(String categoryName, Boolean onlyEnabled);

    /**
     * 获取分类层级统计
     *
     * @return 层级统计
     */
    List<Map<String, Object>> getCategoryLevelStats();

    /**
     * 获取分类状态统计
     *
     * @return 状态统计
     */
    List<Map<String, Object>> getCategoryStatusStats();

    /**
     * 获取分类使用情况统计
     *
     * @return 使用情况统计
     */
    List<Map<String, Object>> getCategoryUsageStats();

    /**
     * 查询热门分类
     *
     * @param limit 限制数量
     * @return 热门分类列表
     */
    List<DataCollectionCategoryVO> selectHotCategories(Integer limit);

    /**
     * 查询空分类
     *
     * @return 空分类列表
     */
    List<DataCollectionCategoryVO> selectEmptyCategories();

    /**
     * 检查循环引用
     *
     * @param categoryId 分类ID
     * @param parentId 父级ID
     * @return 是否存在循环引用
     */
    boolean checkCircularReference(Long categoryId, Long parentId);
}
