# ETL执行引擎设计文档

## 概述

ETL执行引擎是一个高效、可扩展的数据处理系统，支持三种调度类型：手动执行（MANUAL）、定时执行（CRON）、实时处理（REALTIME）。系统基于DORIS数据库，实现了ODS、DWD、DWS、ADS四层数据架构的ETL处理。

## 核心组件

### 1. EtlScheduleManager - 调度管理器
- **功能**: 统一管理三种调度类型的ETL任务
- **特性**: 
  - 支持任务状态变更时自动启动/停止调度
  - 使用Quartz实现定时调度
  - 支持实时任务的生命周期管理
  - 应用启动时自动恢复已启用任务的调度

### 2. EtlExecutionEngine - 执行引擎
- **功能**: 负责实际执行ETL任务
- **特性**:
  - 异步执行，支持并发处理
  - 完整的事务管理和错误处理
  - 支持任务停止和状态监控
  - 详细的执行记录和监控指标

### 3. EtlSqlGenerator - SQL生成器
- **功能**: 根据ETL任务配置动态生成SQL语句
- **支持的写入模式**:
  - OVERWRITE: 覆盖写入
  - APPEND: 追加写入
  - UPSERT: 更新插入
- **特性**:
  - 支持多表关联
  - 字段映射和转换
  - 条件过滤和增量同步

### 4. DorisExecutionEngine - Doris执行引擎
- **功能**: 在Doris数据库中执行SQL语句
- **特性**:
  - 连接池管理
  - 事务支持
  - 执行监控和停止功能
  - 错误处理和重试机制

### 5. RealtimeProcessor - 实时处理器
- **功能**: 处理REALTIME类型的ETL任务
- **策略**:
  - ODS层: CDC监听数据库变更
  - DWD/DWS/ADS层: 定期增量同步
- **特性**:
  - 可配置的检查频率
  - 智能的变更检测
  - 资源优化和错误恢复

### 6. DataQualityChecker - 数据质量检查器
- **功能**: 执行ETL任务的数据质量检查
- **支持的规则类型**:
  - NOT_NULL: 非空检查
  - UNIQUE: 唯一性检查
  - RANGE: 范围检查
  - PATTERN: 模式匹配检查
  - CUSTOM_SQL: 自定义SQL检查
- **特性**:
  - 质量分数计算
  - 多级别严重程度（ERROR/WARNING/INFO）
  - 详细的检查报告

## 调度类型详解

### 1. MANUAL（手动执行）
- **触发方式**: 用户手动点击执行按钮
- **使用场景**: 临时数据处理、测试验证
- **实现**: 直接调用执行引擎

### 2. CRON（定时执行）
- **触发方式**: 基于Cron表达式的定时调度
- **使用场景**: 定期数据同步、批量处理
- **实现**: 使用Quartz调度框架
- **特性**: 
  - 支持复杂的时间表达式
  - 错过执行时间不补执行
  - 任务冲突检测

### 3. REALTIME（实时处理）
- **触发方式**: 数据变更驱动或定期检查
- **使用场景**: 实时数据同步、流式处理
- **实现策略**:
  - **ODS层**: 每30秒检查数据变更（模拟CDC）
  - **其他层**: 每5分钟检查增量同步需求

## 数据层级架构

### ODS (操作数据存储层)
- **数据源**: 业务系统数据库
- **处理方式**: 原始数据抽取和清洗
- **实时策略**: CDC监听变更

### DWD (明细数据层)
- **数据源**: ODS层数据
- **处理方式**: 数据标准化和维度建模
- **实时策略**: 增量同步

### DWS (汇总数据层)
- **数据源**: DWD层数据
- **处理方式**: 数据聚合和指标计算
- **实时策略**: 增量聚合

### ADS (应用数据层)
- **数据源**: DWS层数据
- **处理方式**: 面向应用的数据组织
- **实时策略**: 按需更新

## 使用示例

### 1. 启用ETL任务
```java
// 更新任务状态为启用
etlTaskService.updateEtlTaskStatus(taskId, 1);
// 系统会自动根据调度类型启动相应的调度
```

### 2. 手动执行任务
```java
// 手动触发执行
String executionId = etlTaskService.runEtlTask(taskId);
```

### 3. 停止任务
```java
// 停止正在运行的任务
etlTaskService.stopEtlTask(taskId);
```

## 配置说明

### 线程池配置
- **核心线程数**: 5
- **最大线程数**: 20
- **队列容量**: 100
- **拒绝策略**: CallerRunsPolicy

### 实时处理配置
- **CDC检查频率**: 30秒
- **增量同步频率**: 5分钟
- **线程池大小**: 10

### 数据质量配置
- **质量分数阈值**: 80分
- **检查超时时间**: 30分钟

## 监控和日志

### 执行监控
- 任务执行状态实时跟踪
- 处理行数和错误统计
- 执行时长和性能指标

### 日志记录
- 详细的执行日志
- 错误信息和堆栈跟踪
- 性能和资源使用情况

### 质量监控
- 数据质量分数趋势
- 质量规则执行结果
- 异常数据报告

## 扩展点

### 1. 自定义SQL生成器
实现特定业务逻辑的SQL生成

### 2. 自定义数据质量规则
扩展质量检查规则类型

### 3. 自定义实时处理策略
实现特定的实时数据处理逻辑

### 4. 自定义执行引擎
支持其他数据库或计算引擎

## 注意事项

1. **资源管理**: 合理配置线程池大小，避免资源耗尽
2. **错误处理**: 实现完善的错误恢复机制
3. **监控告警**: 建立完善的监控和告警体系
4. **性能优化**: 定期优化SQL和调度策略
5. **数据一致性**: 确保ETL过程的数据一致性

## 未来优化方向

1. **分布式执行**: 支持多节点分布式ETL处理
2. **智能调度**: 基于资源使用情况的智能调度
3. **可视化监控**: 提供更丰富的监控界面
4. **自动优化**: 基于历史数据的自动性能优化
5. **容错机制**: 更强的容错和自动恢复能力
