import request from '@/utils/request'

// 查询数据采集任务列表
export function listDataCollectionTask(query) {
  return request({
    url: '/datacollection/task/list',
    method: 'get',
    params: query
  })
}

// 查询数据采集任务详细
export function getDataCollectionTask(taskId) {
  return request({
    url: '/datacollection/task/' + taskId,
    method: 'get'
  })
}

// 新增数据采集任务
export function addDataCollectionTask(data) {
  return request({
    url: '/datacollection/task',
    method: 'post',
    data: data
  })
}

// 修改数据采集任务
export function updateDataCollectionTask(data) {
  return request({
    url: '/datacollection/task',
    method: 'put',
    data: data
  })
}

// 删除数据采集任务
export function delDataCollectionTask(taskIds) {
  return request({
    url: '/datacollection/task/delete',
    method: 'post',
    data: taskIds
  })
}

// 批量删除任务
export function batchDelTask(taskIds) {
  return request({
    url: '/datacollection/task/batch',
    method: 'delete',
    data: taskIds
  })
}

// 改变任务状态
export function changeTaskStatus(taskId, status) {
  return request({
    url: '/datacollection/task/changeStatus',
    method: 'put',
    data: {
      taskId,
      status
    }
  })
}

// 执行任务
export function executeTask(taskId) {
  return request({
    url: '/datacollection/task/execute/' + taskId,
    method: 'post'
  })
}

// 停止任务
export function stopTask(taskId) {
  return request({
    url: '/datacollection/task/stop/' + taskId,
    method: 'post'
  })
}

// 复制任务
export function copyTask(taskId, newTaskName) {
  return request({
    url: '/datacollection/task/copy',
    method: 'post',
    data: {
      taskId,
      newTaskName
    }
  })
}

// 移动任务到其他分类
export function moveDataCollectionTask(data) {
  return request({
    url: '/datacollection/task/move',
    method: 'put',
    data: data
  })
}

// 获取任务执行历史
export function getTaskHistory(query) {
  return request({
    url: '/datacollection/task/history',
    method: 'get',
    params: query
  })
}

// 获取任务执行详情
export function getExecutionDetail(executionId) {
  return request({
    url: '/datacollection/task/execution/' + executionId,
    method: 'get'
  })
}

// 验证任务配置
export function validateTask(taskId) {
  return request({
    url: '/datacollection/task/validate/' + taskId,
    method: 'post'
  })
}

// 预览任务执行结果
export function previewTask(taskId) {
  return request({
    url: '/datacollection/task/preview/' + taskId,
    method: 'post'
  })
}

// 导出任务配置
export function exportTask(taskId) {
  return request({
    url: '/datacollection/task/export/' + taskId,
    method: 'get',
    responseType: 'blob'
  })
}

// 导入任务配置
export function importTask(data) {
  return request({
    url: '/datacollection/task/import',
    method: 'post',
    data: data
  })
}

// 获取任务统计信息
export function getTaskStats(categoryId) {
  return request({
    url: '/datacollection/task/stats',
    method: 'get',
    params: {
      categoryId
    }
  })
}

// 检查任务编码是否唯一
export function checkTaskCode(taskCode, taskId) {
  return request({
    url: '/datacollection/task/checkCode',
    method: 'get',
    params: {
      taskCode,
      taskId
    }
  })
}

// 获取任务依赖关系
export function getTaskDependencies(taskId) {
  return request({
    url: '/datacollection/task/dependencies/' + taskId,
    method: 'get'
  })
}

// 保存流程定义
export function saveFlowDefinition(data) {
  return request({
    url: '/datacollection/task/flow',
    method: 'post',
    data: data
  })
}

// 获取流程定义
export function getFlowDefinition(taskId) {
  return request({
    url: '/datacollection/task/flow/' + taskId,
    method: 'get'
  })
}

// 验证流程定义
export function validateFlowDefinition(data) {
  return request({
    url: '/datacollection/task/flow/validate',
    method: 'post',
    data: data
  })
}

// 执行流程
export function executeFlow(data) {
  return request({
    url: '/datacollection/task/flow/execute',
    method: 'post',
    data: data
  })
}

// 更新任务状态
export function updateTaskStatus(taskId, status) {
  return request({
    url: '/datacollection/task/status',
    method: 'put',
    data: {
      taskId: taskId,
      status: status
    }
  })
}

// 执行数据采集任务
export function executeTask(taskId) {
  return request({
    url: '/datacollection/task/execute/' + taskId,
    method: 'post'
  })
}

// 停止数据采集任务
export function stopTask(taskId) {
  return request({
    url: '/datacollection/task/stop/' + taskId,
    method: 'post'
  })
}

// 验证流程定义
export function validateFlow(flowDefinition) {
  return request({
    url: '/datacollection/flow/validate',
    method: 'post',
    data: {
      flowDefinition: flowDefinition
    }
  })
}

// 预览流程执行
export function previewExecution(taskId) {
  return request({
    url: '/datacollection/flow/preview/' + taskId,
    method: 'post'
  })
}

// 获取执行历史
export function getExecutionHistory(taskId) {
  return request({
    url: '/datacollection/execution/history/' + taskId,
    method: 'get'
  })
}

// 获取执行状态
export function getExecutionStatus(executionId) {
  return request({
    url: '/datacollection/execution/status/' + executionId,
    method: 'get'
  })
}

// 获取执行日志
export function getExecutionLog(executionId) {
  return request({
    url: '/datacollection/execution/log/' + executionId,
    method: 'get'
  })
}

// 保存流程设计
export function saveFlowDesign(data) {
  return request({
    url: '/datacollection/flow/save',
    method: 'post',
    data: data
  })
}

// 获取流程设计
export function getFlowDesign(taskId) {
  return request({
    url: '/datacollection/flow/design/' + taskId,
    method: 'get'
  })
}

// 测试数据源连接
export function testDataSource(config) {
  return request({
    url: '/datacollection/datasource/test',
    method: 'post',
    data: config
  })
}

// 获取数据源表列表
export function getDataSourceTables(config) {
  return request({
    url: '/datacollection/datasource/tables',
    method: 'post',
    data: config
  })
}

// 获取表字段信息
export function getTableColumns(config) {
  return request({
    url: '/datacollection/datasource/columns',
    method: 'post',
    data: config
  })
}
