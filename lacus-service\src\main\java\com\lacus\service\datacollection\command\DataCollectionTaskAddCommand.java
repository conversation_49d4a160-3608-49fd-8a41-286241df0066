package com.lacus.service.datacollection.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 数据采集任务新增命令
 *
 * <AUTHOR>
 */
@Data
public class DataCollectionTaskAddCommand {

    /**
     * 任务ID（修改时使用）
     */
    private Long taskId;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 采集类型：API、SQL
     */
    @NotBlank(message = "采集类型不能为空")
    private String collectionType;

    /**
     * 数据源类型：MYSQL、SQLSERVER、API
     */
    @NotBlank(message = "数据源类型不能为空")
    private String sourceType;

    /**
     * 数据源配置JSON
     */
    private String sourceConfig;

    /**
     * 目标数据库
     */
    @NotBlank(message = "目标数据库不能为空")
    private String targetDatabase;

    /**
     * 目标表名
     */
    @NotBlank(message = "目标表名不能为空")
    private String targetTable;

    /**
     * 写入模式：OVERWRITE、APPEND、UPSERT、AUTO_CREATE
     */
    @NotBlank(message = "写入模式不能为空")
    private String writeMode;

    /**
     * 流程定义JSON
     */
    private String flowDefinition;

    /**
     * 调度类型：MANUAL、CRON、REALTIME
     */
    private String scheduleType;

    /**
     * Cron表达式
     */
    private String cronExpression;

    /**
     * 状态：0-禁用，1-启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 备注
     */
    private String remark;
}
