package com.lacus.service.datawarehouse.model;

import com.lacus.common.core.base.BaseEntity;
import com.lacus.service.datawarehouse.command.EtlTaskAddCommand;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ETL任务模型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EtlTaskModel extends BaseEntity {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 源数据层级 (ODS/DWD/DWS)
     */
    private String sourceLayer;

    /**
     * 目标数据层级 (DWD/DWS/ADS)
     */
    private String targetLayer;

    /**
     * 调度类型 (MANUAL/CRON/REALTIME)
     */
    private String scheduleType;

    /**
     * Cron表达式
     */
    private String cronExpression;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 目标表名
     */
    private String targetTable;

    /**
     * 写入模式 (OVERWRITE/APPEND/UPSERT)
     */
    private String writeMode;


    /**
     * 源表配置(JSON)
     */
    private String sourceTablesConfig;

    /**
     * 字段映射配置(JSON)
     */
    private String fieldMappingsConfig;

    /**
     * 数据质量规则配置(JSON)
     */
    private String qualityRulesConfig;

    /**
     * 任务状态 (0-禁用 1-启用)
     */
    private Integer status;

    /**
     * 最后执行时间
     */
    private LocalDateTime lastRunTime;

    /**
     * 最后执行状态
     */
    private String lastRunStatus;

    /**
     * 源表数量
     */
    private Integer sourceTableCount;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 源表列表(运行时使用)
     */
    private List<SourceTableConfig> sourceTables;

    /**
     * 字段映射列表(运行时使用)
     */
    private List<FieldMappingConfig> fieldMappings;

    /**
     * 质量规则列表(运行时使用)
     */
    private List<QualityRuleConfig> qualityRules;

    /**
     * 源表配置
     */
    @Data
    public static class SourceTableConfig {
        private String tableName;
        private String alias;
        private String whereCondition;
    }

    /**
     * 字段映射配置
     */
    @Data
    public static class FieldMappingConfig {
        private String targetField;
        private String fieldType;
        private String expression;
        private String defaultValue;
    }

    /**
     * 质量规则配置
     */
    @Data
    public static class QualityRuleConfig {
        private String ruleName;
        private String checkField;
        private String ruleType;
        private String ruleConfig;
        private String severity;
    }

    /**
     * 创建ETL任务模型
     */
    public static EtlTaskModel create(EtlTaskAddCommand command) {
        EtlTaskModel model = new EtlTaskModel();
        model.setTaskName(command.getTaskName());
        model.setDescription(command.getDescription());
        model.setSourceLayer(command.getSourceLayer());
        model.setTargetLayer(command.getTargetLayer());
        model.setScheduleType(command.getScheduleType());
        model.setCronExpression(command.getCronExpression());
        model.setTimezone(command.getTimezone());
        model.setTargetTable(command.getTargetTable());
        model.setWriteMode(command.getWriteMode());
        model.setSourceTablesConfig(command.getSourceTablesConfig());
        model.setFieldMappingsConfig(command.getFieldMappingsConfig());
        model.setQualityRulesConfig(command.getQualityRulesConfig());
        model.setStatus(1); // 默认启用
        return model;
    }

    /**
     * 更新状态
     */
    public void updateStatus(Integer status) {
        this.setStatus(status);
    }

    /**
     * 更新执行信息
     */
    public void updateExecutionInfo(LocalDateTime runTime, String runStatus) {
        this.setLastRunTime(runTime);
        this.setLastRunStatus(runStatus);
    }

    /**
     * 检查是否可以执行
     */
    public boolean canExecute() {
        return this.status != null && this.status == 1 && this.taskName != null && this.targetTable != null;
    }

    /**
     * 检查是否为实时任务
     */
    public boolean isRealtimeTask() {
        return "REALTIME".equals(this.scheduleType);
    }

    /**
     * 检查是否为定时任务
     */
    public boolean isCronTask() {
        return "CRON".equals(this.scheduleType);
    }

    /**
     * 检查是否为手动任务
     */
    public boolean isManualTask() {
        return "MANUAL".equals(this.scheduleType);
    }
}
