package com.lacus.service.datacollection.engine.executor;

import com.lacus.service.datacollection.engine.NodeExecutor;
import com.lacus.service.datacollection.model.FlowContext;
import com.lacus.service.datacollection.model.FlowDefinition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 循环节点执行器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LoopNodeExecutor implements NodeExecutor {

    @Override
    public String getSupportedNodeType() {
        return "LOOP";
    }

    @Override
    public NodeExecutionResult execute(FlowDefinition.FlowNode node, FlowContext context) {
        try {
            log.info("开始执行循环节点: {}", node.getName());
            context.addLog("开始执行循环节点: " + node.getName());

            Map<String, Object> config = node.getConfig();
            
            // 获取循环配置
            String loopType = (String) config.get("loopType"); // COUNT, CONDITION, DATA_LIST
            String loopVariable = (String) config.get("loopVariable");
            
            FlowContext.LoopContext loopContext = initializeLoopContext(node, config, context);
            context.getLoopContexts().put(node.getId(), loopContext);

            switch (loopType) {
                case "COUNT":
                    return executeCountLoop(node, config, context, loopContext);
                case "CONDITION":
                    return executeConditionLoop(node, config, context, loopContext);
                case "DATA_LIST":
                    return executeDataListLoop(node, config, context, loopContext);
                case "PAGE_QUERY":
                    return executePageQueryLoop(node, config, context, loopContext);
                default:
                    return new NodeExecutionResult("FAILED", "不支持的循环类型: " + loopType, null);
            }

        } catch (Exception e) {
            log.error("循环节点执行失败: {}", e.getMessage(), e);
            context.addLog("循环节点执行失败: " + e.getMessage());
            return new NodeExecutionResult("FAILED", "循环节点执行失败: " + e.getMessage(), null);
        }
    }

    /**
     * 初始化循环上下文
     */
    private FlowContext.LoopContext initializeLoopContext(FlowDefinition.FlowNode node, Map<String, Object> config, FlowContext context) {
        FlowContext.LoopContext loopContext = new FlowContext.LoopContext();
        loopContext.setLoopNodeId(node.getId());
        loopContext.setCurrentIndex(0);
        loopContext.setContinueLoop(true);
        
        // 设置循环变量初始值
        String loopVariable = (String) config.get("loopVariable");
        if (loopVariable != null) {
            loopContext.getLoopVariables().put(loopVariable, 0);
        }
        
        return loopContext;
    }

    /**
     * 执行计数循环
     */
    private NodeExecutionResult executeCountLoop(FlowDefinition.FlowNode node, Map<String, Object> config, 
                                               FlowContext context, FlowContext.LoopContext loopContext) {
        Integer count = (Integer) config.get("count");
        if (count == null || count <= 0) {
            return new NodeExecutionResult("FAILED", "循环次数配置错误", null);
        }

        loopContext.setTotalCount(count);
        context.addLog("开始计数循环，总次数: " + count);

        // 设置循环变量
        String loopVariable = (String) config.get("loopVariable");
        if (loopVariable != null) {
            context.setGlobalVariable(loopVariable, loopContext.getCurrentIndex());
            loopContext.getLoopVariables().put(loopVariable, loopContext.getCurrentIndex());
        }

        return new NodeExecutionResult("SUCCESS", "循环节点初始化成功", loopContext);
    }

    /**
     * 执行条件循环
     */
    private NodeExecutionResult executeConditionLoop(FlowDefinition.FlowNode node, Map<String, Object> config,
                                                   FlowContext context, FlowContext.LoopContext loopContext) {
        String condition = (String) config.get("condition");
        if (condition == null || condition.trim().isEmpty()) {
            return new NodeExecutionResult("FAILED", "循环条件不能为空", null);
        }

        context.addLog("开始条件循环，条件: " + condition);

        // 这里可以集成表达式引擎来评估条件
        // 暂时简化处理
        boolean conditionResult = evaluateCondition(condition, context);
        loopContext.setContinueLoop(conditionResult);

        return new NodeExecutionResult("SUCCESS", "条件循环初始化成功", loopContext);
    }

    /**
     * 执行数据列表循环
     */
    private NodeExecutionResult executeDataListLoop(FlowDefinition.FlowNode node, Map<String, Object> config,
                                                  FlowContext context, FlowContext.LoopContext loopContext) {
        String dataSource = (String) config.get("dataSource");
        if (dataSource == null) {
            return new NodeExecutionResult("FAILED", "数据源配置错误", null);
        }

        // 从上下文获取数据列表
        Object dataList = context.getGlobalVariable(dataSource);
        if (!(dataList instanceof List)) {
            return new NodeExecutionResult("FAILED", "数据源不是列表类型", null);
        }

        List<?> list = (List<?>) dataList;
        loopContext.setTotalCount(list.size());
        
        if (loopContext.getCurrentIndex() < list.size()) {
            Object currentItem = list.get(loopContext.getCurrentIndex());
            loopContext.setCurrentData(currentItem);
            
            // 设置当前项变量
            String itemVariable = (String) config.get("itemVariable");
            if (itemVariable != null) {
                context.setGlobalVariable(itemVariable, currentItem);
                loopContext.getLoopVariables().put(itemVariable, currentItem);
            }
        }

        context.addLog("开始数据列表循环，总数据量: " + list.size());
        return new NodeExecutionResult("SUCCESS", "数据列表循环初始化成功", loopContext);
    }

    /**
     * 执行分页查询循环
     */
    private NodeExecutionResult executePageQueryLoop(FlowDefinition.FlowNode node, Map<String, Object> config,
                                                   FlowContext context, FlowContext.LoopContext loopContext) {
        Integer pageSize = (Integer) config.get("pageSize");
        if (pageSize == null || pageSize <= 0) {
            pageSize = 100; // 默认页大小
        }

        // 设置分页参数
        int currentPage = loopContext.getCurrentIndex() + 1;
        context.setGlobalVariable("pageNum", currentPage);
        context.setGlobalVariable("pageSize", pageSize);
        
        loopContext.getLoopVariables().put("pageNum", currentPage);
        loopContext.getLoopVariables().put("pageSize", pageSize);

        context.addLog("开始分页查询循环，当前页: " + currentPage + "，页大小: " + pageSize);
        return new NodeExecutionResult("SUCCESS", "分页查询循环初始化成功", loopContext);
    }

    /**
     * 评估条件表达式
     */
    private boolean evaluateCondition(String condition, FlowContext context) {
        // 这里可以集成SpEL或其他表达式引擎
        // 暂时简化处理，支持基本的变量比较
        try {
            // 示例：支持 ${variable} > 100 这样的表达式
            return true; // 简化返回
        } catch (Exception e) {
            log.warn("条件表达式评估失败: {}", condition, e);
            return false;
        }
    }

    @Override
    public ValidationResult validate(FlowDefinition.FlowNode node) {
        Map<String, Object> config = node.getConfig();
        if (config == null) {
            return new ValidationResult(false, "循环节点配置不能为空");
        }

        String loopType = (String) config.get("loopType");
        if (loopType == null) {
            return new ValidationResult(false, "循环类型不能为空");
        }

        switch (loopType) {
            case "COUNT":
                Integer count = (Integer) config.get("count");
                if (count == null || count <= 0) {
                    return new ValidationResult(false, "计数循环的次数必须大于0");
                }
                break;
            case "CONDITION":
                String condition = (String) config.get("condition");
                if (condition == null || condition.trim().isEmpty()) {
                    return new ValidationResult(false, "条件循环的条件表达式不能为空");
                }
                break;
            case "DATA_LIST":
                String dataSource = (String) config.get("dataSource");
                if (dataSource == null || dataSource.trim().isEmpty()) {
                    return new ValidationResult(false, "数据列表循环的数据源不能为空");
                }
                break;
            case "PAGE_QUERY":
                // 分页查询循环的验证
                break;
            default:
                return new ValidationResult(false, "不支持的循环类型: " + loopType);
        }

        return new ValidationResult(true, "验证通过");
    }
}
