package com.lacus.service.datawarehouse.engine;

import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ETL定时任务Job
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class EtlQuartzJ<PERSON> implements Job {
    
    @Autowired
    private EtlExecutionEngine etlExecutionEngine;
    
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        Long taskId = context.getJobDetail().getJobDataMap().getLong("taskId");
        String taskName = context.getJobDetail().getJobDataMap().getString("taskName");
        
        log.info("定时ETL任务开始执行: taskId={}, taskName={}", taskId, taskName);
        
        try {
            // 检查任务是否已在运行
            if (etlExecutionEngine.isTaskRunning(taskId)) {
                log.warn("ETL任务正在运行中，跳过本次执行: taskId={}", taskId);
                return;
            }
            
            // 执行ETL任务
            String executionId = etlExecutionEngine.executeTask(taskId);
            log.info("定时ETL任务提交成功: taskId={}, executionId={}", taskId, executionId);
            
        } catch (Exception e) {
            log.error("定时ETL任务执行失败: taskId={}, taskName={}", taskId, taskName, e);
            throw new JobExecutionException("ETL任务执行失败: " + e.getMessage(), e);
        }
    }
}
