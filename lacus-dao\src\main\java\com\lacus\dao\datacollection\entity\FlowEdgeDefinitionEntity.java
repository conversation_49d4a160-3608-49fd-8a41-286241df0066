package com.lacus.dao.datacollection.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 流程连线定义实体
 *
 * <AUTHOR>
 */
@Data
@TableName("flow_edge_definition")
public class FlowEdgeDefinitionEntity {

    /**
     * 连线ID
     */
    @TableId(value = "edge_id", type = IdType.AUTO)
    private Long edgeId;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 连线唯一标识
     */
    @TableField("edge_key")
    private String edgeKey;

    /**
     * 源节点标识
     */
    @TableField("source_node_key")
    private String sourceNodeKey;

    /**
     * 目标节点标识
     */
    @TableField("target_node_key")
    private String targetNodeKey;

    /**
     * 条件表达式
     */
    @TableField("condition_expression")
    private String conditionExpression;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
}
