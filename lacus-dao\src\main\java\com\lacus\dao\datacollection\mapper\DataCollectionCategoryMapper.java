package com.lacus.dao.datacollection.mapper;

import com.lacus.datacollection.domain.DataCollectionCategory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据采集分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface DataCollectionCategoryMapper 
{
    /**
     * 查询数据采集分类
     * 
     * @param categoryId 数据采集分类主键
     * @return 数据采集分类
     */
    public DataCollectionCategory selectDataCollectionCategoryByCategoryId(Long categoryId);

    /**
     * 查询数据采集分类列表
     * 
     * @param dataCollectionCategory 数据采集分类
     * @return 数据采集分类集合
     */
    public List<DataCollectionCategory> selectDataCollectionCategoryList(DataCollectionCategory dataCollectionCategory);

    /**
     * 新增数据采集分类
     * 
     * @param dataCollectionCategory 数据采集分类
     * @return 结果
     */
    public int insertDataCollectionCategory(DataCollectionCategory dataCollectionCategory);

    /**
     * 修改数据采集分类
     * 
     * @param dataCollectionCategory 数据采集分类
     * @return 结果
     */
    public int updateDataCollectionCategory(DataCollectionCategory dataCollectionCategory);

    /**
     * 删除数据采集分类
     * 
     * @param categoryId 数据采集分类主键
     * @return 结果
     */
    public int deleteDataCollectionCategoryByCategoryId(Long categoryId);

    /**
     * 批量删除数据采集分类
     * 
     * @param categoryIds 需要删除的数据主键
     * @return 结果
     */
    public int deleteDataCollectionCategoryByCategoryIds(Long[] categoryIds);

    /**
     * 检查分类编码是否唯一
     * 
     * @param categoryCode 分类编码
     * @return 分类信息
     */
    public DataCollectionCategory checkCategoryCodeUnique(String categoryCode);

    /**
     * 检查分类名称是否唯一
     * 
     * @param categoryName 分类名称
     * @param parentId 父级ID
     * @return 分类信息
     */
    public DataCollectionCategory checkCategoryNameUnique(@Param("categoryName") String categoryName, @Param("parentId") Long parentId);

    /**
     * 检查是否存在子分类
     * 
     * @param categoryId 分类ID
     * @return 子分类数量
     */
    public int hasChildCategory(Long categoryId);

    /**
     * 根据分类路径查询分类
     * 
     * @param categoryPath 分类路径
     * @return 分类信息
     */
    public DataCollectionCategory selectCategoryByPath(String categoryPath);

    /**
     * 根据分类编码查询分类
     * 
     * @param categoryCode 分类编码
     * @return 分类信息
     */
    public DataCollectionCategory selectCategoryByCode(String categoryCode);

    /**
     * 更新分类路径
     * 
     * @param categoryId 分类ID
     * @param categoryPath 分类路径
     * @return 结果
     */
    public int updateCategoryPath(@Param("categoryId") Long categoryId, @Param("categoryPath") String categoryPath);

    /**
     * 更新分类任务数量
     * 
     * @param categoryId 分类ID
     * @param taskCount 任务数量
     * @return 结果
     */
    public int updateTaskCount(@Param("categoryId") Long categoryId, @Param("taskCount") Integer taskCount);

    /**
     * 根据父级ID查询子分类列表
     * 
     * @param parentId 父级ID
     * @return 子分类列表
     */
    public List<DataCollectionCategory> selectCategoryByParentId(Long parentId);

    /**
     * 获取分类的最大排序值
     * 
     * @param parentId 父级ID
     * @return 最大排序值
     */
    public Integer getMaxSortOrder(Long parentId);

    /**
     * 批量更新分类状态
     * 
     * @param categoryIds 分类ID数组
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateStatus(@Param("categoryIds") Long[] categoryIds, @Param("status") Integer status);

    /**
     * 根据分类名称模糊查询
     * 
     * @param categoryName 分类名称
     * @return 分类列表
     */
    public List<DataCollectionCategory> selectCategoryByNameLike(String categoryName);

    /**
     * 获取分类层级统计
     * 
     * @return 层级统计
     */
    public List<Object> selectCategoryLevelStats();

    /**
     * 获取分类状态统计
     * 
     * @return 状态统计
     */
    public List<Object> selectCategoryStatusStats();

    /**
     * 查询指定层级的分类
     * 
     * @param categoryLevel 分类层级
     * @return 分类列表
     */
    public List<DataCollectionCategory> selectCategoryByLevel(Integer categoryLevel);

    /**
     * 获取分类的完整路径
     * 
     * @param categoryId 分类ID
     * @return 完整路径
     */
    public String selectCategoryFullPath(Long categoryId);

    /**
     * 检查分类是否存在循环引用
     * 
     * @param categoryId 分类ID
     * @param parentId 父级ID
     * @return 是否存在循环引用
     */
    public boolean checkCircularReference(@Param("categoryId") Long categoryId, @Param("parentId") Long parentId);

    /**
     * 获取分类的所有祖先分类ID
     * 
     * @param categoryId 分类ID
     * @return 祖先分类ID列表
     */
    public List<Long> selectAncestorCategoryIds(Long categoryId);

    /**
     * 获取分类的所有后代分类ID
     * 
     * @param categoryId 分类ID
     * @return 后代分类ID列表
     */
    public List<Long> selectDescendantCategoryIds(Long categoryId);

    /**
     * 根据创建时间范围查询分类
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分类列表
     */
    public List<DataCollectionCategory> selectCategoryByCreateTime(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 获取分类创建统计（按日期分组）
     * 
     * @param days 统计天数
     * @return 创建统计
     */
    public List<Object> selectCategoryCreateStats(@Param("days") Integer days);

    /**
     * 查询热门分类（按任务数量排序）
     * 
     * @param limit 限制数量
     * @return 热门分类列表
     */
    public List<DataCollectionCategory> selectHotCategories(@Param("limit") Integer limit);

    /**
     * 查询空分类（没有任务的分类）
     * 
     * @return 空分类列表
     */
    public List<DataCollectionCategory> selectEmptyCategories();

    /**
     * 获取分类使用情况统计
     * 
     * @return 使用情况统计
     */
    public List<Object> selectCategoryUsageStats();
}
