-- 数据采集规则引擎 - 优化版数据库表结构
-- 版本: 2.0
-- 创建时间: 2024-12-19

-- 1. 任务分类目录表
DROP TABLE IF EXISTS `data_collection_category`;
CREATE TABLE `data_collection_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) NOT NULL COMMENT '分类编码',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父分类ID',
  `category_path` varchar(500) DEFAULT '' COMMENT '分类路径',
  `category_level` int(11) DEFAULT 1 COMMENT '分类层级',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `category_icon` varchar(100) DEFAULT '' COMMENT '分类图标',
  `category_desc` varchar(500) DEFAULT '' COMMENT '分类描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`category_id`),
  UNIQUE KEY `uk_category_code` (`category_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据采集任务分类表';

-- 2. 数据采集任务表 (优化版)
DROP TABLE IF EXISTS `data_collection_task`;
CREATE TABLE `data_collection_task` (
  `task_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_name` varchar(200) NOT NULL COMMENT '任务名称',
  `task_code` varchar(100) NOT NULL COMMENT '任务编码',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `task_desc` text COMMENT '任务描述',
  `collection_type` varchar(20) NOT NULL COMMENT '采集类型(API:接口采集 SQL:数据库采集 FILE:文件采集)',
  `source_type` varchar(20) NOT NULL COMMENT '数据源类型(MYSQL,SQLSERVER,ORACLE,API,FTP等)',
  `target_database` varchar(100) NOT NULL COMMENT '目标数据库',
  `target_table` varchar(100) NOT NULL COMMENT '目标表名',
  `write_mode` varchar(20) NOT NULL COMMENT '写入模式(OVERWRITE:覆盖 APPEND:追加 UPSERT:更新插入 AUTO_CREATE:自动建表)',
  `schedule_type` varchar(20) NOT NULL COMMENT '调度类型(MANUAL:手动 CRON:定时 REALTIME:实时)',
  `cron_expression` varchar(100) DEFAULT '' COMMENT 'Cron表达式',
  `flow_definition` longtext COMMENT '流程定义JSON',
  `task_config` longtext COMMENT '任务配置JSON',
  `priority` int(11) DEFAULT 5 COMMENT '优先级(1-10)',
  `timeout` int(11) DEFAULT 3600 COMMENT '超时时间(秒)',
  `retry_count` int(11) DEFAULT 3 COMMENT '重试次数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `version` int(11) DEFAULT 1 COMMENT '版本号',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`task_id`),
  UNIQUE KEY `uk_task_code` (`task_code`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_collection_type` (`collection_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据采集任务表';

-- 3. 流程节点定义表 (优化版)
DROP TABLE IF EXISTS `flow_node_definition`;
CREATE TABLE `flow_node_definition` (
  `node_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '节点ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `node_key` varchar(50) NOT NULL COMMENT '节点唯一标识',
  `node_name` varchar(200) NOT NULL COMMENT '节点名称',
  `node_type` varchar(50) NOT NULL COMMENT '节点类型',
  `node_config` longtext COMMENT '节点配置JSON',
  `position_x` int(11) DEFAULT 0 COMMENT 'X坐标',
  `position_y` int(11) DEFAULT 0 COMMENT 'Y坐标',
  `node_width` int(11) DEFAULT 200 COMMENT '节点宽度',
  `node_height` int(11) DEFAULT 80 COMMENT '节点高度',
  `node_style` text COMMENT '节点样式JSON',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`node_id`),
  UNIQUE KEY `uk_task_node` (`task_id`, `node_key`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_node_type` (`node_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程节点定义表';

-- 4. 流程连线定义表 (优化版)
DROP TABLE IF EXISTS `flow_edge_definition`;
CREATE TABLE `flow_edge_definition` (
  `edge_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '连线ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `edge_key` varchar(50) NOT NULL COMMENT '连线唯一标识',
  `edge_name` varchar(200) DEFAULT '' COMMENT '连线名称',
  `source_node_key` varchar(50) NOT NULL COMMENT '源节点标识',
  `target_node_key` varchar(50) NOT NULL COMMENT '目标节点标识',
  `source_anchor` varchar(20) DEFAULT 'right' COMMENT '源锚点',
  `target_anchor` varchar(20) DEFAULT 'left' COMMENT '目标锚点',
  `edge_condition` text COMMENT '连线条件',
  `edge_style` text COMMENT '连线样式JSON',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`edge_id`),
  UNIQUE KEY `uk_task_edge` (`task_id`, `edge_key`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_source_node` (`source_node_key`),
  KEY `idx_target_node` (`target_node_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程连线定义表';

-- 5. 数据源配置表 (优化版)
DROP TABLE IF EXISTS `data_source_config`;
CREATE TABLE `data_source_config` (
  `source_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据源ID',
  `source_name` varchar(200) NOT NULL COMMENT '数据源名称',
  `source_code` varchar(100) NOT NULL COMMENT '数据源编码',
  `source_type` varchar(50) NOT NULL COMMENT '数据源类型',
  `connection_config` longtext NOT NULL COMMENT '连接配置JSON',
  `connection_pool_config` text COMMENT '连接池配置JSON',
  `test_sql` varchar(500) DEFAULT 'SELECT 1' COMMENT '测试SQL',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`source_id`),
  UNIQUE KEY `uk_source_code` (`source_code`),
  KEY `idx_source_type` (`source_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据源配置表';

-- 6. 任务执行历史表 (优化版)
DROP TABLE IF EXISTS `collection_execution_history`;
CREATE TABLE `collection_execution_history` (
  `history_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '历史ID',
  `execution_id` varchar(64) NOT NULL COMMENT '执行ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `task_name` varchar(200) NOT NULL COMMENT '任务名称',
  `execution_type` varchar(20) NOT NULL COMMENT '执行类型(MANUAL:手动 SCHEDULE:调度 API:接口)',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` bigint(20) DEFAULT NULL COMMENT '执行时长(毫秒)',
  `status` varchar(20) NOT NULL COMMENT '执行状态(RUNNING:运行中 SUCCESS:成功 FAILED:失败 STOPPED:已停止)',
  `processed_records` bigint(20) DEFAULT 0 COMMENT '处理记录数',
  `error_records` bigint(20) DEFAULT 0 COMMENT '错误记录数',
  `error_message` text COMMENT '错误信息',
  `execution_log` longtext COMMENT '执行日志',
  `execution_result` longtext COMMENT '执行结果JSON',
  `server_ip` varchar(50) DEFAULT '' COMMENT '执行服务器IP',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`history_id`),
  UNIQUE KEY `uk_execution_id` (`execution_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务执行历史表';

-- 7. 节点执行历史表
DROP TABLE IF EXISTS `node_execution_history`;
CREATE TABLE `node_execution_history` (
  `node_history_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '节点历史ID',
  `execution_id` varchar(64) NOT NULL COMMENT '执行ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `node_key` varchar(50) NOT NULL COMMENT '节点标识',
  `node_name` varchar(200) NOT NULL COMMENT '节点名称',
  `node_type` varchar(50) NOT NULL COMMENT '节点类型',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` bigint(20) DEFAULT NULL COMMENT '执行时长(毫秒)',
  `status` varchar(20) NOT NULL COMMENT '执行状态',
  `input_data` longtext COMMENT '输入数据JSON',
  `output_data` longtext COMMENT '输出数据JSON',
  `error_message` text COMMENT '错误信息',
  `execution_log` text COMMENT '执行日志',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`node_history_id`),
  KEY `idx_execution_id` (`execution_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_node_key` (`node_key`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节点执行历史表';

-- 8. 流程变量定义表
DROP TABLE IF EXISTS `flow_variable_definition`;
CREATE TABLE `flow_variable_definition` (
  `variable_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '变量ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `variable_name` varchar(100) NOT NULL COMMENT '变量名称',
  `variable_type` varchar(50) NOT NULL COMMENT '变量类型(STRING,NUMBER,BOOLEAN,OBJECT,ARRAY)',
  `variable_value` text COMMENT '变量值',
  `variable_desc` varchar(500) DEFAULT '' COMMENT '变量描述',
  `is_global` tinyint(1) DEFAULT 0 COMMENT '是否全局变量',
  `is_input` tinyint(1) DEFAULT 0 COMMENT '是否输入变量',
  `is_output` tinyint(1) DEFAULT 0 COMMENT '是否输出变量',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`variable_id`),
  UNIQUE KEY `uk_task_variable` (`task_id`, `variable_name`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_variable_type` (`variable_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程变量定义表';

-- 初始化分类数据
INSERT INTO `data_collection_category` (`category_name`, `category_code`, `parent_id`, `category_path`, `category_level`, `sort_order`, `category_icon`, `category_desc`) VALUES
('数据采集任务目录', 'ROOT', 0, '/ROOT', 1, 0, 'folder', '根目录'),
('数据库采集任务', 'DATABASE_COLLECTION', 1, '/ROOT/DATABASE_COLLECTION', 2, 1, 'database', '数据库数据采集任务'),
('API接口采集任务', 'API_COLLECTION', 1, '/ROOT/API_COLLECTION', 2, 2, 'api', 'API接口数据采集任务'),
('文件采集任务', 'FILE_COLLECTION', 1, '/ROOT/FILE_COLLECTION', 2, 3, 'file', '文件数据采集任务'),
('实时同步任务', 'REALTIME_SYNC', 1, '/ROOT/REALTIME_SYNC', 2, 4, 'sync', '实时数据同步任务'),
('MySQL数据采集', 'MYSQL_COLLECTION', 2, '/ROOT/DATABASE_COLLECTION/MYSQL_COLLECTION', 3, 1, 'mysql', 'MySQL数据库采集'),
('SQL Server数据采集', 'SQLSERVER_COLLECTION', 2, '/ROOT/DATABASE_COLLECTION/SQLSERVER_COLLECTION', 3, 2, 'sqlserver', 'SQL Server数据库采集'),
('Oracle数据采集', 'ORACLE_COLLECTION', 2, '/ROOT/DATABASE_COLLECTION/ORACLE_COLLECTION', 3, 3, 'oracle', 'Oracle数据库采集'),
('水利工程数据', 'WATER_PROJECT', 2, '/ROOT/DATABASE_COLLECTION/WATER_PROJECT', 3, 4, 'water', '水利工程相关数据'),
('水文数据', 'HYDROLOGY_DATA', 2, '/ROOT/DATABASE_COLLECTION/HYDROLOGY_DATA', 3, 5, 'hydrology', '水文监测数据'),
('水质数据', 'WATER_QUALITY', 2, '/ROOT/DATABASE_COLLECTION/WATER_QUALITY', 3, 6, 'quality', '水质监测数据'),
('降雨数据', 'RAINFALL_DATA', 2, '/ROOT/DATABASE_COLLECTION/RAINFALL_DATA', 3, 7, 'rain', '降雨量数据'),
('水利设施数据', 'WATER_FACILITY', 2, '/ROOT/DATABASE_COLLECTION/WATER_FACILITY', 3, 8, 'facility', '水利设施数据');

-- 初始化数据源配置
INSERT INTO `data_source_config` (`source_name`, `source_code`, `source_type`, `connection_config`, `test_sql`, `create_by`) VALUES
('MySQL测试数据源', 'MYSQL_TEST', 'MYSQL', '{"host":"localhost","port":3306,"database":"test","username":"root","password":"","charset":"utf8mb4"}', 'SELECT 1', 'admin'),
('SQL Server测试数据源', 'SQLSERVER_TEST', 'SQLSERVER', '{"host":"localhost","port":1433,"database":"test","username":"sa","password":"","trustServerCertificate":true}', 'SELECT 1', 'admin'),
('Oracle测试数据源', 'ORACLE_TEST', 'ORACLE', '{"host":"localhost","port":1521,"serviceName":"XE","username":"system","password":""}', 'SELECT 1 FROM DUAL', 'admin');
