<template>
  <div class="data-transform-node-config">
    <el-form :model="config" label-width="100px" size="small">
      <!-- 转换类型 -->
      <el-form-item label="转换类型" required>
        <el-select v-model="config.transformType" placeholder="请选择转换类型" @change="handleTransformTypeChange">
          <el-option label="字段映射" value="MAPPING" />
          <el-option label="数据过滤" value="FILTER" />
          <el-option label="数据聚合" value="AGGREGATE" />
          <el-option label="格式转换" value="FORMAT" />
          <el-option label="脚本转换" value="SCRIPT" />
        </el-select>
      </el-form-item>

      <el-form-item label="输入变量" required>
        <el-input 
          v-model="config.inputVariable" 
          placeholder="输入数据的变量名，如：queryResult"
        />
      </el-form-item>

      <el-form-item label="输出变量" required>
        <el-input 
          v-model="config.outputVariable" 
          placeholder="转换后数据的变量名，如：transformedData"
        />
      </el-form-item>

      <!-- 字段映射配置 -->
      <template v-if="config.transformType === 'MAPPING'">
        <el-form-item label="映射规则">
          <div class="mapping-rules">
            <div 
              v-for="(rule, index) in config.mapping.rules" 
              :key="index"
              class="mapping-item"
            >
              <el-input 
                v-model="rule.sourceField" 
                placeholder="源字段"
                style="width: 120px; margin-right: 8px;"
              />
              <span style="margin-right: 8px;">→</span>
              <el-input 
                v-model="rule.targetField" 
                placeholder="目标字段"
                style="width: 120px; margin-right: 8px;"
              />
              <el-select 
                v-model="rule.transform" 
                placeholder="转换函数"
                style="width: 120px; margin-right: 8px;"
              >
                <el-option label="无转换" value="NONE" />
                <el-option label="大写" value="UPPER" />
                <el-option label="小写" value="LOWER" />
                <el-option label="去空格" value="TRIM" />
                <el-option label="日期格式化" value="DATE_FORMAT" />
                <el-option label="数字格式化" value="NUMBER_FORMAT" />
                <el-option label="字符串拼接" value="CONCAT" />
                <el-option label="字符串截取" value="SUBSTRING" />
                <el-option label="正则替换" value="REGEX_REPLACE" />
              </el-select>
              <el-input 
                v-model="rule.params" 
                placeholder="转换参数"
                style="width: 100px; margin-right: 8px;"
              />
              <el-button 
                type="danger" 
                size="small" 
                icon="Delete"
                @click="removeMappingRule(index)"
              />
            </div>
            <el-button 
              type="primary" 
              size="small" 
              icon="Plus"
              @click="addMappingRule"
            >
              添加映射
            </el-button>
          </div>
        </el-form-item>
      </template>

      <!-- 数据过滤配置 -->
      <template v-if="config.transformType === 'FILTER'">
        <el-form-item label="过滤条件">
          <div class="filter-conditions">
            <div 
              v-for="(condition, index) in config.filter.conditions" 
              :key="index"
              class="filter-item"
            >
              <el-input 
                v-model="condition.field" 
                placeholder="字段名"
                style="width: 120px; margin-right: 8px;"
              />
              <el-select 
                v-model="condition.operator" 
                placeholder="操作符"
                style="width: 100px; margin-right: 8px;"
              >
                <el-option label="等于" value="EQUALS" />
                <el-option label="不等于" value="NOT_EQUALS" />
                <el-option label="大于" value="GREATER_THAN" />
                <el-option label="小于" value="LESS_THAN" />
                <el-option label="包含" value="CONTAINS" />
                <el-option label="不包含" value="NOT_CONTAINS" />
                <el-option label="为空" value="IS_NULL" />
                <el-option label="不为空" value="IS_NOT_NULL" />
              </el-select>
              <el-input 
                v-model="condition.value" 
                placeholder="比较值"
                style="width: 120px; margin-right: 8px;"
              />
              <el-select 
                v-model="condition.logicOperator" 
                placeholder="逻辑"
                style="width: 80px; margin-right: 8px;"
                v-if="index < config.filter.conditions.length - 1"
              >
                <el-option label="AND" value="AND" />
                <el-option label="OR" value="OR" />
              </el-select>
              <el-button 
                type="danger" 
                size="small" 
                icon="Delete"
                @click="removeFilterCondition(index)"
              />
            </div>
            <el-button 
              type="primary" 
              size="small" 
              icon="Plus"
              @click="addFilterCondition"
            >
              添加条件
            </el-button>
          </div>
        </el-form-item>
      </template>

      <!-- 数据聚合配置 -->
      <template v-if="config.transformType === 'AGGREGATE'">
        <el-form-item label="分组字段">
          <el-select 
            v-model="config.aggregate.groupBy" 
            multiple
            placeholder="请选择分组字段"
            style="width: 100%;"
          >
            <el-option 
              v-for="field in availableFields"
              :key="field"
              :label="field"
              :value="field"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="聚合函数">
          <div class="aggregate-functions">
            <div 
              v-for="(func, index) in config.aggregate.functions" 
              :key="index"
              class="aggregate-item"
            >
              <el-input 
                v-model="func.field" 
                placeholder="字段名"
                style="width: 120px; margin-right: 8px;"
              />
              <el-select 
                v-model="func.function" 
                placeholder="聚合函数"
                style="width: 100px; margin-right: 8px;"
              >
                <el-option label="COUNT" value="COUNT" />
                <el-option label="SUM" value="SUM" />
                <el-option label="AVG" value="AVG" />
                <el-option label="MAX" value="MAX" />
                <el-option label="MIN" value="MIN" />
              </el-select>
              <el-input 
                v-model="func.alias" 
                placeholder="别名"
                style="width: 120px; margin-right: 8px;"
              />
              <el-button 
                type="danger" 
                size="small" 
                icon="Delete"
                @click="removeAggregateFunction(index)"
              />
            </div>
            <el-button 
              type="primary" 
              size="small" 
              icon="Plus"
              @click="addAggregateFunction"
            >
              添加函数
            </el-button>
          </div>
        </el-form-item>
      </template>

      <!-- 格式转换配置 -->
      <template v-if="config.transformType === 'FORMAT'">
        <el-form-item label="输入格式">
          <el-select v-model="config.format.inputFormat" placeholder="请选择输入格式">
            <el-option label="JSON" value="JSON" />
            <el-option label="CSV" value="CSV" />
            <el-option label="XML" value="XML" />
            <el-option label="Excel" value="EXCEL" />
          </el-select>
        </el-form-item>
        <el-form-item label="输出格式">
          <el-select v-model="config.format.outputFormat" placeholder="请选择输出格式">
            <el-option label="JSON" value="JSON" />
            <el-option label="CSV" value="CSV" />
            <el-option label="XML" value="XML" />
            <el-option label="Excel" value="EXCEL" />
          </el-select>
        </el-form-item>
        <el-form-item label="格式选项">
          <el-input 
            v-model="config.format.options" 
            type="textarea"
            :rows="3"
            placeholder="格式化选项（JSON格式），如：{&quot;delimiter&quot;: &quot;,&quot;, &quot;header&quot;: true}"
          />
        </el-form-item>
      </template>

      <!-- 脚本转换配置 -->
      <template v-if="config.transformType === 'SCRIPT'">
        <el-form-item label="脚本语言">
          <el-select v-model="config.script.language" placeholder="请选择脚本语言">
            <el-option label="JavaScript" value="JAVASCRIPT" />
            <el-option label="Python" value="PYTHON" />
            <el-option label="Groovy" value="GROOVY" />
          </el-select>
        </el-form-item>
        <el-form-item label="转换脚本">
          <el-input 
            v-model="config.script.code" 
            type="textarea"
            :rows="8"
            placeholder="请输入转换脚本，输入数据通过 input 变量访问，返回转换后的数据"
          />
          <div class="form-tip">
            示例：<br>
            // JavaScript<br>
            return input.map(item => ({<br>
            &nbsp;&nbsp;id: item.id,<br>
            &nbsp;&nbsp;name: item.name.toUpperCase()<br>
            }));
          </div>
        </el-form-item>
      </template>

      <!-- 高级配置 -->
      <el-divider content-position="left">高级配置</el-divider>

      <el-form-item label="批处理">
        <el-switch 
          v-model="config.batch.enabled" 
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>

      <el-form-item label="批大小" v-if="config.batch.enabled">
        <el-input-number 
          v-model="config.batch.size" 
          :min="100"
          :max="10000"
          :step="100"
          placeholder="每批处理记录数"
        />
      </el-form-item>

      <el-form-item label="并行处理">
        <el-switch 
          v-model="config.parallel.enabled" 
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>

      <el-form-item label="线程数" v-if="config.parallel.enabled">
        <el-input-number 
          v-model="config.parallel.threads" 
          :min="1"
          :max="10"
          placeholder="并行线程数"
        />
      </el-form-item>

      <el-form-item label="错误处理">
        <el-select v-model="config.onError" placeholder="转换错误时的处理方式">
          <el-option label="停止处理" value="STOP" />
          <el-option label="跳过错误记录" value="SKIP" />
          <el-option label="使用默认值" value="DEFAULT" />
          <el-option label="记录错误继续" value="LOG_CONTINUE" />
        </el-select>
      </el-form-item>

      <el-form-item label="性能监控">
        <el-checkbox-group v-model="config.monitoring">
          <el-checkbox label="recordCount">记录处理数量</el-checkbox>
          <el-checkbox label="processingTime">处理耗时</el-checkbox>
          <el-checkbox label="memoryUsage">内存使用</el-checkbox>
          <el-checkbox label="errorCount">错误数量</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <!-- 配置预览 -->
    <el-divider content-position="left">配置预览</el-divider>
    <div class="config-preview">
      <el-alert 
        :title="getConfigSummary()" 
        type="info" 
        :closable="false"
        show-icon
      />
    </div>

    <!-- 测试按钮 -->
    <div class="test-section" v-if="detailed">
      <el-button type="success" @click="testTransform" :loading="testing">
        <el-icon><Refresh /></el-icon>
        测试转换
      </el-button>
      <el-button type="info" @click="previewResult" :loading="previewing">
        <el-icon><View /></el-icon>
        预览结果
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { Refresh, View } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  node: {
    type: Object,
    default: () => ({})
  },
  detailed: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

// 配置数据
const config = ref({
  transformType: 'MAPPING',
  inputVariable: '',
  outputVariable: 'transformedData',
  mapping: {
    rules: []
  },
  filter: {
    conditions: []
  },
  aggregate: {
    groupBy: [],
    functions: []
  },
  format: {
    inputFormat: 'JSON',
    outputFormat: 'JSON',
    options: ''
  },
  script: {
    language: 'JAVASCRIPT',
    code: ''
  },
  batch: {
    enabled: false,
    size: 1000
  },
  parallel: {
    enabled: false,
    threads: 2
  },
  onError: 'STOP',
  monitoring: ['recordCount', 'processingTime'],
  ...props.modelValue
})

const testing = ref(false)
const previewing = ref(false)
const availableFields = ref(['id', 'name', 'email', 'createTime', 'status'])

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', newConfig)
}, { deep: true })

// 转换类型变化处理
function handleTransformTypeChange() {
  // 根据转换类型初始化默认配置
  switch (config.value.transformType) {
    case 'MAPPING':
      if (config.value.mapping.rules.length === 0) {
        addMappingRule()
      }
      break
    case 'FILTER':
      if (config.value.filter.conditions.length === 0) {
        addFilterCondition()
      }
      break
    case 'AGGREGATE':
      if (config.value.aggregate.functions.length === 0) {
        addAggregateFunction()
      }
      break
  }
}

// 映射规则操作
function addMappingRule() {
  config.value.mapping.rules.push({
    sourceField: '',
    targetField: '',
    transform: 'NONE',
    params: ''
  })
}

function removeMappingRule(index) {
  config.value.mapping.rules.splice(index, 1)
}

// 过滤条件操作
function addFilterCondition() {
  config.value.filter.conditions.push({
    field: '',
    operator: 'EQUALS',
    value: '',
    logicOperator: 'AND'
  })
}

function removeFilterCondition(index) {
  config.value.filter.conditions.splice(index, 1)
}

// 聚合函数操作
function addAggregateFunction() {
  config.value.aggregate.functions.push({
    field: '',
    function: 'COUNT',
    alias: ''
  })
}

function removeAggregateFunction(index) {
  config.value.aggregate.functions.splice(index, 1)
}

// 测试转换
function testTransform() {
  testing.value = true
  setTimeout(() => {
    testing.value = false
  }, 2000)
}

// 预览结果
function previewResult() {
  previewing.value = true
  setTimeout(() => {
    previewing.value = false
  }, 2000)
}

// 配置摘要
const getConfigSummary = computed(() => {
  const typeMap = {
    'MAPPING': '字段映射',
    'FILTER': '数据过滤',
    'AGGREGATE': '数据聚合',
    'FORMAT': '格式转换',
    'SCRIPT': '脚本转换'
  }
  
  const type = typeMap[config.value.transformType] || '未知'
  const input = config.value.inputVariable || '未设置'
  const output = config.value.outputVariable || '未设置'
  const batch = config.value.batch.enabled ? '，批处理' : ''
  const parallel = config.value.parallel.enabled ? '，并行处理' : ''
  
  return `数据转换：${type}，${input} → ${output}${batch}${parallel}`
})
</script>

<style scoped>
.data-transform-node-config {
  max-height: 600px;
  overflow-y: auto;
}

.mapping-rules,
.filter-conditions,
.aggregate-functions {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}

.mapping-item,
.filter-item,
.aggregate-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.mapping-item:last-child,
.filter-item:last-child,
.aggregate-item:last-child {
  margin-bottom: 12px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.config-preview {
  margin-top: 16px;
}

.test-section {
  margin-top: 16px;
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.el-divider {
  margin: 20px 0 16px 0;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
