package com.lacus.admin.controller.metadata;

import com.lacus.common.core.dto.ResponseDTO;
import com.lacus.common.core.page.PageDTO;
import com.lacus.domain.metadata.table.TableBusiness;
import com.lacus.domain.metadata.table.dto.TableDTO;
import com.lacus.domain.metadata.table.query.TableDetailQuery;
import com.lacus.domain.metadata.table.query.TableQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


@Api(value = "数据表管理", tags = {"数据表管理"})
@RestController
@RequestMapping("/metadata/table")
public class TableController {

    @Autowired
    private TableBusiness tableBusiness;

    @ApiOperation("数据表分页列表")
    @GetMapping("/pageList")
    @PreAuthorize("@permission.has('metadata:table:list')")
    public ResponseDTO<PageDTO> pageList(TableQuery query) {
        PageDTO page = tableBusiness.pageList(query);
        return ResponseDTO.ok(page);
    }

    @ApiOperation("根据数据源ID和数据库名称查询数据表")
    @GetMapping("/listTable")
    @PreAuthorize("@permission.has('metadata:table:list')")
    public ResponseDTO listTable(TableQuery query) {
        return ResponseDTO.ok(tableBusiness.listTable(query));
    }

    @ApiOperation("查询表详情")
    @GetMapping("/detail/{tableId}")
    @PreAuthorize("@permission.has('metadata:table:query')")
    public ResponseDTO<TableDTO> detail(@PathVariable("tableId") Long tableId) {
        return ResponseDTO.ok(tableBusiness.getTableDetailById(tableId));
    }

    @ApiOperation("查询表详情")
    @GetMapping("/info")
    @PreAuthorize("@permission.has('metadata:table:query')")
    public ResponseDTO<TableDTO> info(TableDetailQuery query) {
        return ResponseDTO.ok(tableBusiness.getTableDetail(query));
    }

    /**
     * 根据数据层级获取表列表
     */
    @ApiOperation("根据数据层级获取表列表")
    @GetMapping("/by-layer")
//    @PreAuthorize("@permission.has('metadata:table:list')")
    public ResponseDTO<Object> getTablesByLayer(@RequestParam String layer) {
        return ResponseDTO.ok(tableBusiness.getTablesByLayer(layer));
    }
}
