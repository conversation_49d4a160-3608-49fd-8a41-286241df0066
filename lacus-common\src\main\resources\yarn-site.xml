<?xml version="1.0"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->
<configuration>

<!-- Site specific YARN configuration properties -->
<property>
    <name>yarn.nodemanager.aux-services</name>
    <value>mapreduce_shuffle</value>
</property>
    <!-- 指定YARN的ResourceManager的地址 -->
    <property>
        <name>yarn.resourcemanager.hostname</name>
        <value>shouhou</value>
    </property>
    <!-- 日志聚集功能使能 -->
    <property>
        <name>yarn.log-aggregation-enable</name>
        <value>true</value>
    </property>

    <!-- 日志保留时间设置7天 -->
    <property>
        <name>yarn.log-aggregation.retain-seconds</name>
        <value>604800</value>
    </property>
    <property>

        <name>yarn.application.classpath</name>

        <value>
            /home/<USER>/hadoop-3.3.6/etc/hadoop:
            /home/<USER>/hadoop-3.3.6/share/hadoop/common/lib/*:
            /home/<USER>/hadoop-3.3.6/share/hadoop/common/*:
            /home/<USER>/hadoop-3.3.6/share/hadoop/hdfs:
            /home/<USER>/hadoop-3.3.6/share/hadoop/hdfs/lib/*:
            /home/<USER>/hadoop-3.3.6/share/hadoop/hdfs/*:
            /home/<USER>/hadoop-3.3.6/share/hadoop/mapreduce/*:
            /home/<USER>/hadoop-3.3.6/share/hadoop/yarn:
            /home/<USER>/hadoop-3.3.6/share/hadoop/yarn/lib/*:
            /home/<USER>/hadoop-3.3.6/share/hadoop/yarn/*
        </value>

    </property>
</configuration>
