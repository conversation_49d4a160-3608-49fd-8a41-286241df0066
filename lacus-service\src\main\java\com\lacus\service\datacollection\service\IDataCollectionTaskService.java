package com.lacus.service.datacollection.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lacus.dao.datacollection.vo.DataCollectionTaskExecutionVO;
import com.lacus.dao.datacollection.vo.DataCollectionTaskPageQuery;
import com.lacus.dao.datacollection.vo.DataCollectionTaskVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 数据采集任务Service接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IDataCollectionTaskService {

    /**
     * 分页查询数据采集任务列表
     *
     * @param pageQuery 分页查询参数
     * @return 任务列表
     */
    IPage<DataCollectionTaskVO> selectTaskPage(DataCollectionTaskPageQuery pageQuery);

    /**
     * 查询数据采集任务
     *
     * @param taskId 数据采集任务主键
     * @return 数据采集任务
     */
    DataCollectionTaskVO selectTaskById(Long taskId);

    /**
     * 新增数据采集任务
     *
     * @param taskVO 数据采集任务
     * @return 结果
     */
    boolean insertTask(DataCollectionTaskVO taskVO);

    /**
     * 修改数据采集任务
     *
     * @param taskVO 数据采集任务
     * @return 结果
     */
    boolean updateTask(DataCollectionTaskVO taskVO);

    /**
     * 批量删除数据采集任务
     *
     * @param taskIds 需要删除的数据采集任务主键集合
     * @return 结果
     */
    boolean deleteTaskByIds(List<Long> taskIds);

    /**
     * 删除数据采集任务信息
     *
     * @param taskId 数据采集任务主键
     * @return 结果
     */
    boolean deleteTaskById(Long taskId);

    /**
     * 检查任务编码是否唯一
     *
     * @param taskCode 任务编码
     * @param taskId 任务ID（排除自己）
     * @return 结果
     */
    boolean checkTaskCodeUnique(String taskCode, Long taskId);

    /**
     * 检查任务名称是否唯一
     *
     * @param taskName 任务名称
     * @param categoryId 分类ID
     * @param taskId 任务ID（排除自己）
     * @return 结果
     */
    boolean checkTaskNameUnique(String taskName, Long categoryId, Long taskId);

    /**
     * 改变任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @return 结果
     */
    boolean changeTaskStatus(Long taskId, Integer status);

    /**
     * 批量更新任务状态
     *
     * @param taskIds 任务ID列表
     * @param status 状态
     * @return 结果
     */
    boolean batchUpdateTaskStatus(List<Long> taskIds, Integer status);

    /**
     * 执行任务
     *
     * @param taskId 任务ID
     */
    String executeTask(Long taskId);

    /**
     * 停止任务
     *
     * @param taskId 任务ID
     * @return 结果
     */
    boolean stopTask(Long taskId);

    /**
     * 复制任务
     *
     * @param taskId 任务ID
     * @param newTaskName 新任务名称
     * @return 新任务ID
     */
    Long copyTask(Long taskId, String newTaskName);

    /**
     * 移动任务到其他分类
     *
     * @param taskId 任务ID
     * @param targetCategoryId 目标分类ID
     * @param moveReason 移动原因
     * @return 结果
     */
    boolean moveTask(Long taskId, Long targetCategoryId, String moveReason);

    /**
     * 获取任务执行历史
     *
     * @param taskId 任务ID
     * @param status 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 执行历史列表
     */
    List<DataCollectionTaskExecutionVO> getTaskHistory(Long taskId, String status, String startTime, String endTime);

    /**
     * 获取任务执行详情
     *
     * @param executionId 执行ID
     * @return 执行详情
     */
    DataCollectionTaskExecutionVO getExecutionDetail(String executionId);

    /**
     * 验证任务配置
     *
     * @param taskId 任务ID
     * @return 验证结果
     */
    Map<String, Object> validateTask(Long taskId);

    /**
     * 预览任务执行结果
     *
     * @param taskId 任务ID
     * @return 预览结果
     */
    Map<String, Object> previewTask(Long taskId);

    /**
     * 导出任务配置
     *
     * @param response 响应对象
     * @param taskId 任务ID
     */
    void exportTask(HttpServletResponse response, Long taskId);

    /**
     * 导入任务配置
     *
     * @param data 任务数据
     * @return 导入结果
     */
    Map<String, Object> importTask(Map<String, Object> data);

    /**
     * 获取任务统计信息
     *
     * @param categoryId 分类ID
     * @return 统计信息
     */
    Map<String, Object> getTaskStats(Long categoryId);

    /**
     * 获取任务依赖关系
     *
     * @param taskId 任务ID
     * @return 依赖关系
     */
    Map<String, Object> getTaskDependencies(Long taskId);

    /**
     * 保存流程定义
     *
     * @param data 流程数据
     * @return 保存结果
     */
    Map<String, Object> saveFlowDefinition(Map<String, Object> data);

    /**
     * 获取流程定义
     *
     * @param taskId 任务ID
     * @return 流程定义
     */
    Map<String, Object> getFlowDefinition(Long taskId);

    /**
     * 验证流程定义
     *
     * @param data 流程数据
     * @return 验证结果
     */
    Map<String, Object> validateFlowDefinition(Map<String, Object> data);

    /**
     * 执行流程
     *
     * @param data 流程数据
     * @return 执行结果
     */
    Map<String, Object> executeFlow(Map<String, Object> data);

    /**
     * 根据任务编码查询任务
     *
     * @param taskCode 任务编码
     * @return 任务信息
     */
    DataCollectionTaskVO selectTaskByCode(String taskCode);

    /**
     * 根据任务名称查询任务
     *
     * @param taskName 任务名称
     * @return 任务信息
     */
    DataCollectionTaskVO selectTaskByName(String taskName);

    /**
     * 获取所有启用的任务
     *
     * @return 任务列表
     */
    List<DataCollectionTaskVO> selectEnabledTasks();

    /**
     * 根据调度类型查询任务
     *
     * @param scheduleType 调度类型
     * @return 任务列表
     */
    List<DataCollectionTaskVO> selectTasksByScheduleType(String scheduleType);

    /**
     * 根据分类ID查询任务
     *
     * @param categoryId 分类ID
     * @param onlyEnabled 是否只查询启用状态
     * @return 任务列表
     */
    List<DataCollectionTaskVO> selectTasksByCategoryId(Long categoryId, Boolean onlyEnabled);

    /**
     * 查询最近执行的任务
     *
     * @param limit 限制数量
     * @return 任务列表
     */
    List<DataCollectionTaskVO> selectRecentExecutedTasks(Integer limit);

    /**
     * 查询执行失败的任务
     *
     * @return 任务列表
     */
    List<DataCollectionTaskVO> selectFailedTasks();

    /**
     * 查询正在运行的任务
     *
     * @return 任务列表
     */
    List<DataCollectionTaskVO> selectRunningTasks();

    /**
     * 查询热门任务
     *
     * @param limit 限制数量
     * @return 热门任务列表
     */
    List<DataCollectionTaskVO> selectHotTasks(Integer limit);

    /**
     * 根据采集类型查询任务
     *
     * @param collectionType 采集类型
     * @param onlyEnabled 是否只查询启用状态
     * @return 任务列表
     */
    List<DataCollectionTaskVO> selectTasksByCollectionType(String collectionType, Boolean onlyEnabled);

    /**
     * 根据数据源类型查询任务
     *
     * @param sourceType 数据源类型
     * @param onlyEnabled 是否只查询启用状态
     * @return 任务列表
     */
    List<DataCollectionTaskVO> selectTasksBySourceType(String sourceType, Boolean onlyEnabled);

    /**
     * 获取任务创建统计
     *
     * @param days 统计天数
     * @return 创建统计
     */
    List<Map<String, Object>> getTaskCreateStats(Integer days);

    /**
     * 更新任务版本号
     *
     * @param taskId 任务ID
     * @return 结果
     */
    boolean updateTaskVersion(Long taskId);
}
