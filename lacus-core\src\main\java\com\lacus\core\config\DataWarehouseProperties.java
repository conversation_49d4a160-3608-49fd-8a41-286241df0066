package com.lacus.core.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 数据仓库配置属性
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "datawarehouse")
public class DataWarehouseProperties {

    private EtlConfig etl = new EtlConfig();
    private QualityConfig quality = new QualityConfig();
    private ScheduleConfig schedule = new ScheduleConfig();
    private DorisConfig doris = new DorisConfig();
    private ThreadPoolConfig threadPool = new ThreadPoolConfig();

    // Getters and Setters
    public EtlConfig getEtl() { return etl; }
    public void setEtl(EtlConfig etl) { this.etl = etl; }

    public QualityConfig getQuality() { return quality; }
    public void setQuality(QualityConfig quality) { this.quality = quality; }

    public ScheduleConfig getSchedule() { return schedule; }
    public void setSchedule(ScheduleConfig schedule) { this.schedule = schedule; }

    public DorisConfig getDoris() { return doris; }
    public void setDoris(DorisConfig doris) { this.doris = doris; }

    public ThreadPoolConfig getThreadPool() { return threadPool; }
    public void setThreadPool(ThreadPoolConfig threadPool) { this.threadPool = threadPool; }

    /**
     * ETL配置
     */
    public static class EtlConfig {
        private int maxConcurrentTasks = 10;
        private int taskTimeoutMinutes = 60;
        private int maxRetryCount = 3;
        private int previewMaxRows = 100;
        private List<String> supportedLayers;

        // Getters and Setters
        public int getMaxConcurrentTasks() { return maxConcurrentTasks; }
        public void setMaxConcurrentTasks(int maxConcurrentTasks) { this.maxConcurrentTasks = maxConcurrentTasks; }

        public int getTaskTimeoutMinutes() { return taskTimeoutMinutes; }
        public void setTaskTimeoutMinutes(int taskTimeoutMinutes) { this.taskTimeoutMinutes = taskTimeoutMinutes; }

        public int getMaxRetryCount() { return maxRetryCount; }
        public void setMaxRetryCount(int maxRetryCount) { this.maxRetryCount = maxRetryCount; }

        public int getPreviewMaxRows() { return previewMaxRows; }
        public void setPreviewMaxRows(int previewMaxRows) { this.previewMaxRows = previewMaxRows; }

        public List<String> getSupportedLayers() { return supportedLayers; }
        public void setSupportedLayers(List<String> supportedLayers) { this.supportedLayers = supportedLayers; }
    }

    /**
     * 质量监控配置
     */
    public static class QualityConfig {
        private String defaultCheckFrequency = "DAILY";
        private int alertThreshold = 80;

        // Getters and Setters
        public String getDefaultCheckFrequency() { return defaultCheckFrequency; }
        public void setDefaultCheckFrequency(String defaultCheckFrequency) { this.defaultCheckFrequency = defaultCheckFrequency; }

        public int getAlertThreshold() { return alertThreshold; }
        public void setAlertThreshold(int alertThreshold) { this.alertThreshold = alertThreshold; }
    }

    /**
     * 调度配置
     */
    public static class ScheduleConfig {
        private int maxConcurrentJobs = 5;
        private int schedulerPoolSize = 10;
        private int jobTimeoutMinutes = 120;

        // Getters and Setters
        public int getMaxConcurrentJobs() { return maxConcurrentJobs; }
        public void setMaxConcurrentJobs(int maxConcurrentJobs) { this.maxConcurrentJobs = maxConcurrentJobs; }

        public int getSchedulerPoolSize() { return schedulerPoolSize; }
        public void setSchedulerPoolSize(int schedulerPoolSize) { this.schedulerPoolSize = schedulerPoolSize; }

        public int getJobTimeoutMinutes() { return jobTimeoutMinutes; }
        public void setJobTimeoutMinutes(int jobTimeoutMinutes) { this.jobTimeoutMinutes = jobTimeoutMinutes; }
    }

    /**
     * Doris配置
     */
    public static class DorisConfig {
        private int connectionTimeout = 30;
        private int queryTimeout = 300;
        private Map<String, String> databases;

        // Getters and Setters
        public int getConnectionTimeout() { return connectionTimeout; }
        public void setConnectionTimeout(int connectionTimeout) { this.connectionTimeout = connectionTimeout; }

        public int getQueryTimeout() { return queryTimeout; }
        public void setQueryTimeout(int queryTimeout) { this.queryTimeout = queryTimeout; }

        public Map<String, String> getDatabases() { return databases; }
        public void setDatabases(Map<String, String> databases) { this.databases = databases; }
    }

    /**
     * 线程池配置
     */
    public static class ThreadPoolConfig {
        private ExecutorConfig etlExecutor = new ExecutorConfig();
        private ExecutorConfig qualityExecutor = new ExecutorConfig();
        private ExecutorConfig scheduleExecutor = new ExecutorConfig();

        // Getters and Setters
        public ExecutorConfig getEtlExecutor() { return etlExecutor; }
        public void setEtlExecutor(ExecutorConfig etlExecutor) { this.etlExecutor = etlExecutor; }

        public ExecutorConfig getQualityExecutor() { return qualityExecutor; }
        public void setQualityExecutor(ExecutorConfig qualityExecutor) { this.qualityExecutor = qualityExecutor; }

        public ExecutorConfig getScheduleExecutor() { return scheduleExecutor; }
        public void setScheduleExecutor(ExecutorConfig scheduleExecutor) { this.scheduleExecutor = scheduleExecutor; }
    }

    /**
     * 执行器配置
     */
    public static class ExecutorConfig {
        private int corePoolSize = 5;
        private int maxPoolSize = 20;
        private int queueCapacity = 100;
        private String threadNamePrefix = "executor-";
        private int keepAliveSeconds = 60;

        // Getters and Setters
        public int getCorePoolSize() { return corePoolSize; }
        public void setCorePoolSize(int corePoolSize) { this.corePoolSize = corePoolSize; }

        public int getMaxPoolSize() { return maxPoolSize; }
        public void setMaxPoolSize(int maxPoolSize) { this.maxPoolSize = maxPoolSize; }

        public int getQueueCapacity() { return queueCapacity; }
        public void setQueueCapacity(int queueCapacity) { this.queueCapacity = queueCapacity; }

        public String getThreadNamePrefix() { return threadNamePrefix; }
        public void setThreadNamePrefix(String threadNamePrefix) { this.threadNamePrefix = threadNamePrefix; }

        public int getKeepAliveSeconds() { return keepAliveSeconds; }
        public void setKeepAliveSeconds(int keepAliveSeconds) { this.keepAliveSeconds = keepAliveSeconds; }
    }
}
