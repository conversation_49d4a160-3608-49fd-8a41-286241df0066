package com.lacus.dao.datacollection.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lacus.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据采集任务实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("data_collection_task")
public class DataCollectionTaskEntity extends BaseEntity {

    /**
     * 任务ID
     */
    @TableId(value = "task_id", type = IdType.AUTO)
    private Long taskId;

    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 任务描述
     */
    @TableField("task_desc")
    private String taskDesc;

    /**
     * 采集类型：API、SQL
     */
    @TableField("collection_type")
    private String collectionType;

    /**
     * 数据源类型：MYSQL、SQLSERVER、API
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 数据源配置JSON
     */
    @TableField("source_config")
    private String sourceConfig;

    /**
     * 目标数据库
     */
    @TableField("target_database")
    private String targetDatabase;

    /**
     * 目标表名
     */
    @TableField("target_table")
    private String targetTable;

    /**
     * 写入模式：OVERWRITE、APPEND、UPSERT、AUTO_CREATE
     */
    @TableField("write_mode")
    private String writeMode;

    /**
     * 流程定义JSON
     */
    @TableField("flow_definition")
    private String flowDefinition;

    /**
     * 调度类型：MANUAL、CRON、REALTIME
     */
    @TableField("schedule_type")
    private String scheduleType;

    /**
     * Cron表达式
     */
    @TableField("cron_expression")
    private String cronExpression;

    /**
     * 状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;
}
