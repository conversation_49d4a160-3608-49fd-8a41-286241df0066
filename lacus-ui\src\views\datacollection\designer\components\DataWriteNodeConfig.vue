<template>
  <div class="data-write-node-config">
    <el-form :model="config" label-width="100px" size="small">
      <!-- 基本配置 -->
      <el-form-item label="输入变量" required>
        <el-input 
          v-model="config.inputVariable" 
          placeholder="要写入的数据变量名，如：transformedData"
        />
      </el-form-item>

      <el-form-item label="目标数据库" required>
        <el-input 
          v-model="config.targetDatabase" 
          placeholder="目标数据库名称"
        />
      </el-form-item>

      <el-form-item label="目标表名" required>
        <el-input 
          v-model="config.targetTable" 
          placeholder="目标表名称"
        />
      </el-form-item>

      <el-form-item label="写入模式" required>
        <el-select v-model="config.writeMode" placeholder="请选择写入模式" @change="handleWriteModeChange">
          <el-option label="覆盖写入" value="OVERWRITE">
            <div class="write-mode-option">
              <span class="mode-name">覆盖写入</span>
              <span class="mode-desc">清空表后写入新数据</span>
            </div>
          </el-option>
          <el-option label="追加写入" value="APPEND">
            <div class="write-mode-option">
              <span class="mode-name">追加写入</span>
              <span class="mode-desc">在表末尾添加新数据</span>
            </div>
          </el-option>
          <el-option label="更新插入" value="UPSERT">
            <div class="write-mode-option">
              <span class="mode-name">更新插入</span>
              <span class="mode-desc">存在则更新，不存在则插入</span>
            </div>
          </el-option>
          <el-option label="自动建表" value="AUTO_CREATE">
            <div class="write-mode-option">
              <span class="mode-name">自动建表</span>
              <span class="mode-desc">表不存在时自动创建</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <!-- UPSERT模式配置 -->
      <template v-if="config.writeMode === 'UPSERT'">
        <el-form-item label="主键字段" required>
          <el-select 
            v-model="config.upsert.keyFields" 
            multiple
            placeholder="请选择主键字段"
            style="width: 100%;"
          >
            <el-option 
              v-for="field in availableFields"
              :key="field"
              :label="field"
              :value="field"
            />
          </el-select>
          <div class="form-tip">用于判断记录是否存在的字段</div>
        </el-form-item>
        <el-form-item label="更新字段">
          <el-select 
            v-model="config.upsert.updateFields" 
            multiple
            placeholder="请选择要更新的字段，留空则更新所有字段"
            style="width: 100%;"
          >
            <el-option 
              v-for="field in availableFields"
              :key="field"
              :label="field"
              :value="field"
            />
          </el-select>
        </el-form-item>
      </template>

      <!-- AUTO_CREATE模式配置 -->
      <template v-if="config.writeMode === 'AUTO_CREATE'">
        <el-form-item label="表结构推断">
          <el-switch 
            v-model="config.autoCreate.inferSchema" 
            active-text="自动推断"
            inactive-text="手动定义"
          />
        </el-form-item>
        <template v-if="!config.autoCreate.inferSchema">
          <el-form-item label="表结构定义">
            <div class="schema-definition">
              <div 
                v-for="(column, index) in config.autoCreate.schema" 
                :key="index"
                class="column-item"
              >
                <el-input 
                  v-model="column.name" 
                  placeholder="字段名"
                  style="width: 120px; margin-right: 8px;"
                />
                <el-select 
                  v-model="column.type" 
                  placeholder="数据类型"
                  style="width: 100px; margin-right: 8px;"
                >
                  <el-option label="VARCHAR" value="VARCHAR" />
                  <el-option label="INT" value="INT" />
                  <el-option label="BIGINT" value="BIGINT" />
                  <el-option label="DECIMAL" value="DECIMAL" />
                  <el-option label="DATE" value="DATE" />
                  <el-option label="DATETIME" value="DATETIME" />
                  <el-option label="TEXT" value="TEXT" />
                </el-select>
                <el-input 
                  v-model="column.length" 
                  placeholder="长度"
                  style="width: 80px; margin-right: 8px;"
                />
                <el-checkbox v-model="column.nullable" style="margin-right: 8px;">可空</el-checkbox>
                <el-button 
                  type="danger" 
                  size="small" 
                  icon="Delete"
                  @click="removeColumn(index)"
                />
              </div>
              <el-button 
                type="primary" 
                size="small" 
                icon="Plus"
                @click="addColumn"
              >
                添加字段
              </el-button>
            </div>
          </el-form-item>
        </template>
      </template>

      <!-- 性能配置 -->
      <el-divider content-position="left">性能配置</el-divider>

      <el-form-item label="批处理大小">
        <el-input-number 
          v-model="config.batchSize" 
          :min="100"
          :max="10000"
          :step="100"
          placeholder="每批写入记录数"
        />
        <span class="form-tip">批量写入可以提高性能</span>
      </el-form-item>

      <el-form-item label="并行写入">
        <el-switch 
          v-model="config.parallel.enabled" 
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>

      <el-form-item label="并行度" v-if="config.parallel.enabled">
        <el-input-number 
          v-model="config.parallel.degree" 
          :min="1"
          :max="10"
          placeholder="并行写入线程数"
        />
      </el-form-item>

      <el-form-item label="写入超时">
        <el-input-number 
          v-model="config.timeout" 
          :min="30"
          :max="3600"
          placeholder="秒"
        />
        <span class="form-tip">单次写入操作超时时间</span>
      </el-form-item>

      <!-- 数据质量 -->
      <el-divider content-position="left">数据质量</el-divider>

      <el-form-item label="数据验证">
        <el-switch 
          v-model="config.validation.enabled" 
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>

      <template v-if="config.validation.enabled">
        <el-form-item label="验证规则">
          <div class="validation-rules">
            <div 
              v-for="(rule, index) in config.validation.rules" 
              :key="index"
              class="rule-item"
            >
              <el-input 
                v-model="rule.field" 
                placeholder="字段名"
                style="width: 120px; margin-right: 8px;"
              />
              <el-select 
                v-model="rule.type" 
                placeholder="验证类型"
                style="width: 120px; margin-right: 8px;"
              >
                <el-option label="非空检查" value="NOT_NULL" />
                <el-option label="长度检查" value="LENGTH" />
                <el-option label="格式检查" value="FORMAT" />
                <el-option label="范围检查" value="RANGE" />
                <el-option label="唯一性检查" value="UNIQUE" />
              </el-select>
              <el-input 
                v-model="rule.params" 
                placeholder="验证参数"
                style="width: 120px; margin-right: 8px;"
              />
              <el-button 
                type="danger" 
                size="small" 
                icon="Delete"
                @click="removeValidationRule(index)"
              />
            </div>
            <el-button 
              type="primary" 
              size="small" 
              icon="Plus"
              @click="addValidationRule"
            >
              添加规则
            </el-button>
          </div>
        </el-form-item>
      </template>

      <el-form-item label="重复数据处理">
        <el-select v-model="config.duplicateHandling" placeholder="重复数据处理方式">
          <el-option label="忽略重复" value="IGNORE" />
          <el-option label="覆盖重复" value="REPLACE" />
          <el-option label="报错停止" value="ERROR" />
          <el-option label="记录日志" value="LOG" />
        </el-select>
      </el-form-item>

      <!-- 错误处理 -->
      <el-divider content-position="left">错误处理</el-divider>

      <el-form-item label="错误处理策略">
        <el-select v-model="config.onError" placeholder="写入错误时的处理方式">
          <el-option label="停止写入" value="STOP" />
          <el-option label="跳过错误记录" value="SKIP" />
          <el-option label="重试写入" value="RETRY" />
          <el-option label="写入错误表" value="ERROR_TABLE" />
        </el-select>
      </el-form-item>

      <el-form-item label="重试次数" v-if="config.onError === 'RETRY'">
        <el-input-number 
          v-model="config.retryCount" 
          :min="1"
          :max="10"
          placeholder="重试次数"
        />
      </el-form-item>

      <el-form-item label="错误表名" v-if="config.onError === 'ERROR_TABLE'">
        <el-input 
          v-model="config.errorTable" 
          placeholder="错误记录存储表名"
        />
      </el-form-item>

      <el-form-item label="最大错误数">
        <el-input-number 
          v-model="config.maxErrors" 
          :min="0"
          placeholder="允许的最大错误数，0表示不限制"
        />
      </el-form-item>

      <!-- 监控统计 -->
      <el-divider content-position="left">监控统计</el-divider>

      <el-form-item label="统计信息">
        <el-checkbox-group v-model="config.statistics">
          <el-checkbox label="writeCount">写入记录数</el-checkbox>
          <el-checkbox label="writeTime">写入耗时</el-checkbox>
          <el-checkbox label="errorCount">错误记录数</el-checkbox>
          <el-checkbox label="throughput">写入吞吐量</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="结果变量">
        <el-input 
          v-model="config.resultVariable" 
          placeholder="写入结果保存的变量名，如：writeResult"
        />
      </el-form-item>
    </el-form>

    <!-- 配置预览 -->
    <el-divider content-position="left">配置预览</el-divider>
    <div class="config-preview">
      <el-alert 
        :title="getConfigSummary()" 
        type="info" 
        :closable="false"
        show-icon
      />
    </div>

    <!-- 测试按钮 -->
    <div class="test-section" v-if="detailed">
      <el-button type="success" @click="testWrite" :loading="testing">
        <el-icon><Upload /></el-icon>
        测试写入
      </el-button>
      <el-button type="info" @click="validateConfig" :loading="validating">
        <el-icon><CircleCheck /></el-icon>
        验证配置
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { Upload, CircleCheck } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  node: {
    type: Object,
    default: () => ({})
  },
  detailed: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

// 配置数据
const config = ref({
  inputVariable: '',
  targetDatabase: '',
  targetTable: '',
  writeMode: 'APPEND',
  upsert: {
    keyFields: [],
    updateFields: []
  },
  autoCreate: {
    inferSchema: true,
    schema: []
  },
  batchSize: 1000,
  parallel: {
    enabled: false,
    degree: 2
  },
  timeout: 300,
  validation: {
    enabled: false,
    rules: []
  },
  duplicateHandling: 'IGNORE',
  onError: 'STOP',
  retryCount: 3,
  errorTable: '',
  maxErrors: 0,
  statistics: ['writeCount', 'writeTime'],
  resultVariable: 'writeResult',
  ...props.modelValue
})

const testing = ref(false)
const validating = ref(false)
const availableFields = ref(['id', 'name', 'email', 'createTime', 'updateTime', 'status'])

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', newConfig)
}, { deep: true })

// 写入模式变化处理
function handleWriteModeChange() {
  // 根据写入模式调整相关配置
  if (config.value.writeMode === 'AUTO_CREATE' && config.value.autoCreate.schema.length === 0) {
    addColumn()
  }
}

// 表结构操作
function addColumn() {
  config.value.autoCreate.schema.push({
    name: '',
    type: 'VARCHAR',
    length: '255',
    nullable: true
  })
}

function removeColumn(index) {
  config.value.autoCreate.schema.splice(index, 1)
}

// 验证规则操作
function addValidationRule() {
  config.value.validation.rules.push({
    field: '',
    type: 'NOT_NULL',
    params: ''
  })
}

function removeValidationRule(index) {
  config.value.validation.rules.splice(index, 1)
}

// 测试写入
function testWrite() {
  testing.value = true
  setTimeout(() => {
    testing.value = false
  }, 2000)
}

// 验证配置
function validateConfig() {
  validating.value = true
  setTimeout(() => {
    validating.value = false
  }, 1000)
}

// 配置摘要
const getConfigSummary = computed(() => {
  const modeMap = {
    'OVERWRITE': '覆盖写入',
    'APPEND': '追加写入',
    'UPSERT': '更新插入',
    'AUTO_CREATE': '自动建表'
  }
  
  const mode = modeMap[config.value.writeMode] || '未知'
  const database = config.value.targetDatabase || '未设置'
  const table = config.value.targetTable || '未设置'
  const batch = config.value.batchSize || 1000
  const parallel = config.value.parallel.enabled ? '，并行写入' : ''
  const validation = config.value.validation.enabled ? '，数据验证' : ''
  
  return `数据写入：${mode} → ${database}.${table}，批大小${batch}${parallel}${validation}`
})
</script>

<style scoped>
.data-write-node-config {
  max-height: 600px;
  overflow-y: auto;
}

.write-mode-option {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.mode-name {
  font-weight: 500;
}

.mode-desc {
  font-size: 12px;
  color: #909399;
}

.schema-definition,
.validation-rules {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}

.column-item,
.rule-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.column-item:last-child,
.rule-item:last-child {
  margin-bottom: 12px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
  line-height: 1.4;
}

.config-preview {
  margin-top: 16px;
}

.test-section {
  margin-top: 16px;
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.el-divider {
  margin: 20px 0 16px 0;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
