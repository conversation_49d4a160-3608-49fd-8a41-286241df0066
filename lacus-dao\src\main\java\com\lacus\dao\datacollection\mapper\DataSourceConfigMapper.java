package com.lacus.dao.datacollection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lacus.dao.datacollection.entity.DataSourceConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据源配置Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DataSourceConfigMapper extends BaseMapper<DataSourceConfigEntity> {

    /**
     * 根据数据源名称查询
     *
     * @param sourceName 数据源名称
     * @return 数据源配置
     */
    DataSourceConfigEntity selectBySourceName(@Param("sourceName") String sourceName);

    /**
     * 根据数据源类型查询列表
     *
     * @param sourceType 数据源类型
     * @return 数据源列表
     */
    List<DataSourceConfigEntity> selectBySourceType(@Param("sourceType") String sourceType);

    /**
     * 查询启用状态的数据源列表
     *
     * @return 数据源列表
     */
    List<DataSourceConfigEntity> selectEnabledSources();
}
