package com.lacus.dao.datacollection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lacus.dao.datacollection.entity.DataCollectionTaskEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据采集任务Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DataCollectionTaskMapper extends BaseMapper<DataCollectionTaskEntity> {

    /**
     * 根据任务名称查询任务
     *
     * @param taskName 任务名称
     * @return 任务实体
     */
    DataCollectionTaskEntity selectByTaskName(@Param("taskName") String taskName);

    /**
     * 查询启用状态的任务列表
     *
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectEnabledTasks();

    /**
     * 根据调度类型查询任务列表
     *
     * @param scheduleType 调度类型
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectByScheduleType(@Param("scheduleType") String scheduleType);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @return 更新行数
     */
    int updateTaskStatus(@Param("taskId") Long taskId, @Param("status") Integer status);

    /**
     * 批量删除任务
     *
     * @param taskIds 任务ID列表
     * @return 删除行数
     */
    int deleteBatchByIds(@Param("taskIds") List<Long> taskIds);
}
