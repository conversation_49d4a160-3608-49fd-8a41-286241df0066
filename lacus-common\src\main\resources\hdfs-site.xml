<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->

<!-- Put site-specific property overrides in this file. -->

<configuration>
<property>
        <name>dfs.replication</name>      
        <value>1</value>
    </property>  

    <property>
        <name>dfs.namenode.name.dir</name>  
        <value>/home/<USER>/hadoop-3.3.6/hdfs/name</value>  
        <final>true</final>
    </property>  

    <property>
        <name>dfs.datanode.data.dir</name>  
        <value>/home/<USER>/hadoop-3.3.6/hdfs/data</value>  
        <final>true</final>
    </property>  

    <property>
        <name>dfs.http.address</name>
        <value>0.0.0.0:50070</value>
    </property>
<property>
    <name>dfs.namenode.rpc-bind-host</name>
    <value>0.0.0.0</value>
</property>
<property>
    <name>dfs.namenode.servicerpc-bind-host</name>
    <value>0.0.0.0</value>
</property>
<property>
    <name>dfs.namenode.http-bind-host</name>
    <value>0.0.0.0</value>
</property>
<property>
    <name>dfs.namenode.https-bind-host</name>
    <value>0.0.0.0</value>
</property>
    <property>
        <name>dfs.permissions</name>  
        <value>false</value>
    </property>

</configuration>
