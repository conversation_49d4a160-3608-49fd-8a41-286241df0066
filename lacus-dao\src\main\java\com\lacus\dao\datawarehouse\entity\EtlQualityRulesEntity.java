package com.lacus.dao.datawarehouse.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lacus.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * ETL数据源配置表
 */
@Getter
@Setter
@TableName("etl_quality_rules")
@ApiModel(value = "EtlQualityRulesEntity对象", description = "ETL数据质量配置")
public class EtlQualityRulesEntity extends BaseEntity<EtlQualityRulesEntity> {

    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("规则名称")
    @TableField("rule_name")
    private String ruleName;

    @ApiModelProperty("检查字段")
    @TableField("check_field")
    private String checkField;

    @ApiModelProperty("规则类型")
    @TableField("rule_type")
    private String ruleType;

    @ApiModelProperty("规则配置")
    @TableField("rule_config")
    private String ruleConfig;

    @ApiModelProperty("严重级别")
    @TableField("severity")
    private String severity;

    @ApiModelProperty("关联的任务ID")
    @TableField("etl_task_id")
    private Long etlTaskId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
