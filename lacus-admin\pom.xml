<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>lacus</artifactId>
        <groupId>com.lacus</groupId>
        <version>${revision}</version>
    </parent>
    <packaging>jar</packaging>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>lacus-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>
        <dependency>
            <groupId>com.lacus</groupId>
            <artifactId>lacus-datasource-plugin</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-interpolation</artifactId>
            <version>1.26</version>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.lacus</groupId>
            <artifactId>lacus-core</artifactId>
        </dependency>

        <!-- 业务领域 -->
        <dependency>
            <groupId>com.lacus</groupId>
            <artifactId>lacus-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lacus</groupId>
            <artifactId>lacus-dao</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.1.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
</project>
