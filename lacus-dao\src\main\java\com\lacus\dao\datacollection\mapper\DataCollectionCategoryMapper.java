package com.lacus.dao.datacollection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lacus.dao.datacollection.entity.DataCollectionCategoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 数据采集分类Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Mapper
public interface DataCollectionCategoryMapper extends BaseMapper<DataCollectionCategoryEntity>
{

    /**
     * 分页查询数据采集分类列表
     *
     * @param page 分页参数
     * @param categoryName 分类名称
     * @param categoryCode 分类编码
     * @param parentId 父分类ID
     * @param status 状态
     * @param createTimeStart 创建时间开始
     * @param createTimeEnd 创建时间结束
     * @return 分类列表
     */
    IPage<DataCollectionCategoryEntity> selectCategoryPage(
            Page<DataCollectionCategoryEntity> page,
            @Param("categoryName") String categoryName,
            @Param("categoryCode") String categoryCode,
            @Param("parentId") Long parentId,
            @Param("status") Integer status,
            @Param("createTimeStart") String createTimeStart,
            @Param("createTimeEnd") String createTimeEnd
    );

    /**
     * 查询分类树形结构
     *
     * @param status 状态过滤
     * @return 分类树
     */
    List<DataCollectionCategoryEntity> selectCategoryTree(@Param("status") Integer status);

    /**
     * 根据父级ID查询子分类列表
     *
     * @param parentId 父级ID
     * @param status 状态过滤
     * @return 子分类列表
     */
    List<DataCollectionCategoryEntity> selectCategoryByParentId(
            @Param("parentId") Long parentId,
            @Param("status") Integer status
    );

    /**
     * 检查分类编码是否唯一
     *
     * @param categoryCode 分类编码
     * @param categoryId 分类ID（排除自己）
     * @return 分类信息
     */
    DataCollectionCategoryEntity checkCategoryCodeUnique(
            @Param("categoryCode") String categoryCode,
            @Param("categoryId") Long categoryId
    );

    /**
     * 检查分类名称是否唯一
     *
     * @param categoryName 分类名称
     * @param parentId 父级ID
     * @param categoryId 分类ID（排除自己）
     * @return 分类信息
     */
    DataCollectionCategoryEntity checkCategoryNameUnique(
            @Param("categoryName") String categoryName,
            @Param("parentId") Long parentId,
            @Param("categoryId") Long categoryId
    );

    /**
     * 检查是否存在子分类
     *
     * @param categoryId 分类ID
     * @return 子分类数量
     */
    int hasChildCategory(@Param("categoryId") Long categoryId);

    /**
     * 根据分类路径查询分类
     *
     * @param categoryPath 分类路径
     * @return 分类信息
     */
    DataCollectionCategoryEntity selectCategoryByPath(@Param("categoryPath") String categoryPath);

    /**
     * 根据分类编码查询分类
     *
     * @param categoryCode 分类编码
     * @return 分类信息
     */
    DataCollectionCategoryEntity selectCategoryByCode(@Param("categoryCode") String categoryCode);

    /**
     * 更新分类路径
     *
     * @param categoryId 分类ID
     * @param categoryPath 分类路径
     * @return 结果
     */
    int updateCategoryPath(@Param("categoryId") Long categoryId, @Param("categoryPath") String categoryPath);

    /**
     * 获取分类的最大排序值
     *
     * @param parentId 父级ID
     * @return 最大排序值
     */
    Integer getMaxSortOrder(@Param("parentId") Long parentId);

    /**
     * 批量更新分类状态
     *
     * @param categoryIds 分类ID数组
     * @param status 状态
     * @return 结果
     */
    int batchUpdateStatus(@Param("categoryIds") List<Long> categoryIds, @Param("status") Integer status);

    /**
     * 根据分类名称模糊查询
     *
     * @param categoryName 分类名称
     * @param status 状态过滤
     * @return 分类列表
     */
    List<DataCollectionCategoryEntity> selectCategoryByNameLike(
            @Param("categoryName") String categoryName,
            @Param("status") Integer status
    );

    /**
     * 获取分类层级统计
     *
     * @return 层级统计
     */
    List<Map<String, Object>> selectCategoryLevelStats();

    /**
     * 获取分类状态统计
     *
     * @return 状态统计
     */
    List<Map<String, Object>> selectCategoryStatusStats();

    /**
     * 查询指定层级的分类
     *
     * @param categoryLevel 分类层级
     * @param status 状态过滤
     * @return 分类列表
     */
    List<DataCollectionCategoryEntity> selectCategoryByLevel(
            @Param("categoryLevel") Integer categoryLevel,
            @Param("status") Integer status
    );

    /**
     * 检查分类是否存在循环引用
     *
     * @param categoryId 分类ID
     * @param parentId 父级ID
     * @return 是否存在循环引用
     */
    boolean checkCircularReference(@Param("categoryId") Long categoryId, @Param("parentId") Long parentId);

    /**
     * 获取分类的所有祖先分类ID
     *
     * @param categoryId 分类ID
     * @return 祖先分类ID列表
     */
    List<Long> selectAncestorCategoryIds(@Param("categoryId") Long categoryId);

    /**
     * 获取分类的所有后代分类ID
     *
     * @param categoryId 分类ID
     * @return 后代分类ID列表
     */
    List<Long> selectDescendantCategoryIds(@Param("categoryId") Long categoryId);

    /**
     * 根据创建时间范围查询分类
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分类列表
     */
    List<DataCollectionCategoryEntity> selectCategoryByCreateTime(
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );

    /**
     * 获取分类创建统计（按日期分组）
     *
     * @param days 统计天数
     * @return 创建统计
     */
    List<Map<String, Object>> selectCategoryCreateStats(@Param("days") Integer days);

    /**
     * 查询热门分类（按任务数量排序）
     *
     * @param limit 限制数量
     * @return 热门分类列表
     */
    List<DataCollectionCategoryEntity> selectHotCategories(@Param("limit") Integer limit);

    /**
     * 查询空分类（没有任务的分类）
     *
     * @return 空分类列表
     */
    List<DataCollectionCategoryEntity> selectEmptyCategories();

    /**
     * 获取分类使用情况统计
     *
     * @return 使用情况统计
     */
    List<Map<String, Object>> selectCategoryUsageStats();

    /**
     * 获取分类的完整路径名称
     *
     * @param categoryId 分类ID
     * @return 完整路径名称
     */
    String selectCategoryFullName(@Param("categoryId") Long categoryId);

    /**
     * 批量删除分类
     *
     * @param categoryIds 分类ID列表
     * @return 删除数量
     */
    int batchDeleteCategory(@Param("categoryIds") List<Long> categoryIds);
}
