package com.lacus.service.datawarehouse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lacus.common.core.page.PageDTO;
import com.lacus.dao.datawarehouse.entity.EtlExecutionHistoryEntity;
import com.lacus.dao.datawarehouse.entity.EtlTaskEntity;
import com.lacus.service.datawarehouse.command.EtlTaskAddCommand;
import com.lacus.service.datawarehouse.dto.EtlTaskDTO;
import com.lacus.service.datawarehouse.dto.EtlTaskStatsDTO;
import com.lacus.service.datawarehouse.model.EtlTaskModel;
import com.lacus.service.datawarehouse.query.EtlExecutionHistoryQuery;
import com.lacus.service.datawarehouse.query.EtlTaskQuery;

import java.util.List;

/**
 * ETL任务服务接口
 */
public interface EtlTaskService extends IService<EtlTaskEntity> {
    PageDTO queryEtlTaskDtoList(EtlTaskQuery query);

    EtlTaskDTO queryEtlTaskDtoById(Long taskId);

    /**
     * 根据ID查询ETL任务
     */
    EtlTaskDTO queryEtlTaskById(Long taskId);

    /**
     * 保存ETL任务
     */
    Long saveEtlTask(EtlTaskAddCommand command);

    /**
     * 更新ETL任务
     */
    void updateEtlTask(EtlTaskAddCommand model);

    /**
     * 批量删除ETL任务
     */
    void batchDeleteEtlTask(List<Long> taskIds);

    /**
     * 检查任务名称是否存在
     */
    boolean checkTaskNameExists(String taskName, Long excludeTaskId);

    /**
     * 更新任务状态
     */
    void updateEtlTaskStatus(Long taskId, Integer status);

    /**
     * 执行ETL任务
     */
    String runEtlTask(Long taskId);

    /**
     * 停止ETL任务
     */
    void stopEtlTask(Long taskId);

    /**
     * 获取执行历史
     */
//    PageDTO getExecutionHistory(EtlExecutionHistoryQuery query);

    /**
     * 预览ETL结果
     */
    Object previewEtlResult(Object command);

    /**
     * 获取字段映射建议
     */
    Object getFieldMappingSuggestions(String sourceLayer, String targetLayer, List<String> sourceTables);

    /**
     * 获取ETL任务统计信息
     */
    EtlTaskStatsDTO getEtlTaskStats();

    /**
     * 获取ETL任务统计信息
     */
    List<EtlExecutionHistoryEntity> getExecutionHistory(String taskId);

    PageDTO getHistoryList(EtlExecutionHistoryQuery query);

    /**
     * 根据状态查询任务列表
     */
    List<EtlTaskModel> queryTasksByStatus(Integer status);

    /**
     * 根据调度类型查询任务列表
     */
    List<EtlTaskModel> queryTasksByScheduleType(String scheduleType);
}
