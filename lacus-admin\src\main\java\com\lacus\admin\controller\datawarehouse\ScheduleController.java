package com.lacus.admin.controller.datawarehouse;

import com.lacus.common.core.base.BaseController;
import com.lacus.common.core.dto.ResponseDTO;
import com.lacus.common.core.page.PageDTO;
import com.lacus.domain.datawarehouse.schedule.ScheduleBusiness;
import com.lacus.service.datawarehouse.command.ScheduleJobAddCommand;
import com.lacus.service.datawarehouse.command.ScheduleJobUpdateCommand;
import com.lacus.service.datawarehouse.dto.ScheduleJobDTO;
import com.lacus.service.datawarehouse.dto.ScheduleStatsDTO;
import com.lacus.service.datawarehouse.query.ScheduleJobQuery;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.lacus.core.security.AuthenticationUtils.getUsername;

/**
 * 调度管理控制器
 */
@Api(value = "调度管理控制器", tags = {"调度管理控制器"})
@RestController
@RequestMapping("/datawarehouse/schedule")
public class ScheduleController extends BaseController {

    @Autowired
    private  ScheduleBusiness scheduleBusiness;

    /**
     * 查询调度任务列表
     */
    @GetMapping("/list")
    @PreAuthorize("@permission.has('datawarehouse:schedule:list')")
    public ResponseDTO<?> list(ScheduleJobQuery query) {
        PageDTO page = scheduleBusiness.queryScheduleJobList(query);
        return ResponseDTO.ok(page);
    }

    /**
     * 获取调度任务详细信息
     */
    @GetMapping("/{scheduleId}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:query')")
    public ResponseDTO<ScheduleJobDTO> getInfo(@PathVariable Long scheduleId) {
        return ResponseDTO.ok(scheduleBusiness.queryScheduleJobById(scheduleId));
    }

    /**
     * 新增调度任务
     */
    @PostMapping
    @PreAuthorize("@permission.has('datawarehouse:schedule:add')")
    public ResponseDTO<Long> add(@Validated @RequestBody ScheduleJobAddCommand command) {
        command.setCreateBy(getUsername());
        return scheduleBusiness.addScheduleJob(command);
    }

    /**
     * 修改调度任务
     */
    @PutMapping
    @PreAuthorize("@permission.has('datawarehouse:schedule:edit')")
    public ResponseDTO<Void> edit(@Validated @RequestBody ScheduleJobUpdateCommand command) {
        command.setUpdateBy(getUsername());
        scheduleBusiness.updateScheduleJob(command);
        return ResponseDTO.ok();
    }

    /**
     * 删除调度任务
     */
    @DeleteMapping("/{scheduleIds}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:remove')")
    public ResponseDTO<String> remove(@PathVariable Long[] scheduleIds) {
        return scheduleBusiness.batchDeleteScheduleJob(Arrays.asList(scheduleIds));
    }

    /**
     * 触发调度任务
     */
    @PostMapping("/trigger/{scheduleId}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:trigger')")
    public ResponseDTO<String> trigger(@PathVariable Long scheduleId) {
        return scheduleBusiness.triggerScheduleJob(scheduleId);
    }

    /**
     * 暂停调度任务
     */
    @PostMapping("/pause/{scheduleId}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:pause')")
    public ResponseDTO<String> pause(@PathVariable Long scheduleId) {
        return scheduleBusiness.pauseScheduleJob(scheduleId);
    }

    /**
     * 恢复调度任务
     */
    @PostMapping("/resume/{scheduleId}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:resume')")
    public ResponseDTO<String> resume(@PathVariable Long scheduleId) {
        return scheduleBusiness.resumeScheduleJob(scheduleId);
    }

    /**
     * 获取调度统计信息
     */
    @GetMapping("/stats")
    @PreAuthorize("@permission.has('datawarehouse:schedule:list')")
    public ResponseDTO<ScheduleStatsDTO> getStats() {
        return scheduleBusiness.getScheduleStats();
    }

    /**
     * 获取今日调度任务
     */
    @GetMapping("/today")
    @PreAuthorize("@permission.has('datawarehouse:schedule:list')")
    public ResponseDTO<Object> getTodaySchedules() {
        return ResponseDTO.ok(scheduleBusiness.getTodaySchedules());
    }

    /**
     * 获取日历调度数据
     */
    @GetMapping("/calendar")
    @PreAuthorize("@permission.has('datawarehouse:schedule:list')")
    public ResponseDTO<Object> getCalendarSchedules(@RequestParam Integer year, @RequestParam Integer month) {
        return ResponseDTO.ok(scheduleBusiness.getCalendarSchedules(year, month));
    }

    /**
     * 获取调度执行历史
     */
    @GetMapping("/history/{scheduleId}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:query')")
    public ResponseDTO<PageDTO> getHistory(@PathVariable Long scheduleId) {
        return scheduleBusiness.getScheduleHistory(scheduleId);
    }

    /**
     * 获取调度执行日志
     */
    @GetMapping("/log/{executionId}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:query')")
    public ResponseDTO<String> getLog(@PathVariable String executionId) {
        return scheduleBusiness.getScheduleLog(executionId);
    }

    /**
     * 验证Cron表达式
     */
    @PostMapping("/validate-cron")
    @PreAuthorize("@permission.has('datawarehouse:schedule:add')")
    public ResponseDTO<Boolean> validateCron(@RequestBody Map<String, String> params) {
        String expression = params.get("expression");
        return scheduleBusiness.validateCronExpression(expression);
    }

    /**
     * 获取Cron表达式下次执行时间
     */
    @PostMapping("/next-fire-times")
    @PreAuthorize("@permission.has('datawarehouse:schedule:add')")
    public ResponseDTO<Object> getNextFireTimes(@RequestBody Map<String, Object> params) {
        String expression = (String) params.get("expression");
        Integer count = (Integer) params.get("count");
        return ResponseDTO.ok(scheduleBusiness.getNextFireTimes(expression, count));
    }

    /**
     * 获取调度依赖关系
     */
    @GetMapping("/dependencies/{scheduleId}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:query')")
    public ResponseDTO<Object> getDependencies(@PathVariable Long scheduleId) {
        return ResponseDTO.ok(scheduleBusiness.getScheduleDependencies(scheduleId));
    }

    /**
     * 设置调度依赖
     */
    @PostMapping("/dependencies/{scheduleId}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:edit')")
    public ResponseDTO<Void> setDependencies(@PathVariable Long scheduleId, @RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Object> dependencies = (List<Object>) params.get("dependencies");
        return scheduleBusiness.setScheduleDependencies(scheduleId, dependencies);
    }

    /**
     * 获取调度任务监控数据
     */
    @GetMapping("/monitor/{scheduleId}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:query')")
    public ResponseDTO<Object> getMonitor(@PathVariable Long scheduleId) {
        return scheduleBusiness.getScheduleMonitorData(scheduleId);
    }

    /**
     * 获取调度性能指标
     */
    @GetMapping("/metrics/{scheduleId}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:query')")
    public ResponseDTO<Object> getMetrics(@PathVariable Long scheduleId, @RequestParam String timeRange) {
        return scheduleBusiness.getScheduleMetrics(scheduleId, timeRange);
    }

    /**
     * 批量操作调度任务
     */
    @PostMapping("/batch-operate")
    @PreAuthorize("@permission.has('datawarehouse:schedule:edit')")
    public ResponseDTO<String> batchOperate(@RequestBody Map<String, Object> params) {
        String operation = (String) params.get("operation");
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) params.get("ids");
        return scheduleBusiness.batchOperateSchedule(operation, ids);
    }

    /**
     * 获取调度告警规则
     */
    @GetMapping("/alert-rules/{scheduleId}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:query')")
    public ResponseDTO<Object> getAlertRules(@PathVariable Long scheduleId) {
        return scheduleBusiness.getScheduleAlertRules(scheduleId);
    }

    /**
     * 设置调度告警规则
     */
    @PostMapping("/alert-rules/{scheduleId}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:edit')")
    public ResponseDTO<Void> setAlertRules(@PathVariable Long scheduleId, @RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Object> rules = (List<Object>) params.get("rules");
        return scheduleBusiness.setScheduleAlertRules(scheduleId, rules);
    }

    /**
     * 获取调度任务模板
     */
    @GetMapping("/templates")
    @PreAuthorize("@permission.has('datawarehouse:schedule:query')")
    public ResponseDTO<Object> getTemplates() {
        return scheduleBusiness.getScheduleTemplates();
    }

    /**
     * 从模板创建调度任务
     */
    @PostMapping("/create-from-template/{templateId}")
    @PreAuthorize("@permission.has('datawarehouse:schedule:add')")
    public ResponseDTO<Long> createFromTemplate(@PathVariable Long templateId, @RequestBody ScheduleJobAddCommand data) {
        data.setCreateBy(getUsername());
        return scheduleBusiness.createFromTemplate(templateId, data);
    }
}
