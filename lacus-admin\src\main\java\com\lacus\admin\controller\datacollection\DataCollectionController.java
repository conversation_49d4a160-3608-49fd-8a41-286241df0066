package com.lacus.admin.controller.datacollection;

import com.lacus.common.core.base.BaseController;
import com.lacus.common.core.dto.ResponseDTO;
import com.lacus.service.datacollection.DataCollectionService;
import com.lacus.service.datacollection.command.DataCollectionTaskAddCommand;
import com.lacus.service.datacollection.dto.DataCollectionTaskDTO;
import com.lacus.service.datacollection.query.DataCollectionTaskQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 数据采集控制器
 *
 * <AUTHOR>
 */
@Api(value = "数据采集控制器", tags = {"数据采集控制器"})
@RestController
@RequestMapping("/datacollection")
public class DataCollectionController extends BaseController {

    @Autowired
    private DataCollectionService dataCollectionService;

    /**
     * 查询数据采集任务列表
     */
    @ApiOperation("查询数据采集任务列表")
    @GetMapping("/task/list")
    public ResponseDTO<?> list(DataCollectionTaskQuery query) {
        return ResponseDTO.ok(dataCollectionService.queryTaskList(query));
    }

    /**
     * 获取数据采集任务详细信息
     */
    @ApiOperation("获取数据采集任务详细信息")
    @GetMapping("/task/{taskId}")
    @PreAuthorize("@permission.has('datacollection:task:query')")
    public ResponseDTO<DataCollectionTaskDTO> getInfo(@PathVariable Long taskId) {
        return ResponseDTO.ok(dataCollectionService.queryTaskById(taskId));
    }

    /**
     * 新增数据采集任务
     */
    @ApiOperation("新增数据采集任务")
    @PostMapping("/task")
    @PreAuthorize("@permission.has('datacollection:task:add')")
    public ResponseDTO<Long> add(@Validated @RequestBody DataCollectionTaskAddCommand command) {
        Long taskId = dataCollectionService.saveTask(command);
        return ResponseDTO.ok(taskId);
    }

    /**
     * 修改数据采集任务
     */
    @ApiOperation("修改数据采集任务")
    @PutMapping("/task")
    @PreAuthorize("@permission.has('datacollection:task:edit')")
    public ResponseDTO<Void> edit(@Validated @RequestBody DataCollectionTaskAddCommand command) {
        dataCollectionService.updateTask(command);
        return ResponseDTO.ok();
    }

    /**
     * 删除数据采集任务
     */
    @ApiOperation("删除数据采集任务")
    @PostMapping("/task/delete")
    @PreAuthorize("@permission.has('datacollection:task:remove')")
    public ResponseDTO<Void> remove(@RequestBody List<Long> taskIds) {
        dataCollectionService.deleteTask(taskIds);
        return ResponseDTO.ok();
    }

    /**
     * 更新数据采集任务状态
     */
    @ApiOperation("更新数据采集任务状态")
    @PutMapping("/task/status")
    @PreAuthorize("@permission.has('datacollection:task:edit')")
    public ResponseDTO<Void> updateStatus(@RequestBody Map<String, Object> params) {
        Long taskId = Long.valueOf(params.get("taskId").toString());
        Integer status = Integer.valueOf(params.get("status").toString());
        dataCollectionService.updateTaskStatus(taskId, status);
        return ResponseDTO.ok();
    }

    /**
     * 执行数据采集任务
     */
    @ApiOperation("执行数据采集任务")
    @PostMapping("/task/execute/{taskId}")
    @PreAuthorize("@permission.has('datacollection:task:execute')")
    public ResponseDTO<String> execute(@PathVariable Long taskId) {
        String executionId = dataCollectionService.executeTask(taskId);
        return ResponseDTO.ok(executionId);
    }

    /**
     * 停止数据采集任务
     */
    @ApiOperation("停止数据采集任务")
    @PostMapping("/task/stop/{taskId}")
    @PreAuthorize("@permission.has('datacollection:task:execute')")
    public ResponseDTO<Void> stop(@PathVariable Long taskId) {
        dataCollectionService.stopTask(taskId);
        return ResponseDTO.ok();
    }

    /**
     * 验证流程定义
     */
    @ApiOperation("验证流程定义")
    @PostMapping("/flow/validate")
    @PreAuthorize("@permission.has('datacollection:task:add')")
    public ResponseDTO<DataCollectionService.ValidationResult> validateFlow(@RequestBody Map<String, Object> params) {
        String flowDefinition = (String) params.get("flowDefinition");
        DataCollectionService.ValidationResult result = dataCollectionService.validateFlowDefinition(flowDefinition);
        return ResponseDTO.ok(result);
    }

    /**
     * 预览流程执行
     */
    @ApiOperation("预览流程执行")
    @PostMapping("/flow/preview/{taskId}")
    @PreAuthorize("@permission.has('datacollection:task:query')")
    public ResponseDTO<Object> previewExecution(@PathVariable Long taskId) {
        Object result = dataCollectionService.previewExecution(taskId);
        return ResponseDTO.ok(result);
    }

    /**
     * 获取执行历史
     */
    @ApiOperation("获取执行历史")
    @GetMapping("/execution/history/{taskId}")
    @PreAuthorize("@permission.has('datacollection:task:query')")
    public ResponseDTO<List<Object>> getExecutionHistory(@PathVariable Long taskId) {
        List<Object> history = dataCollectionService.getExecutionHistory(taskId);
        return ResponseDTO.ok(history);
    }

    /**
     * 获取执行状态
     */
    @ApiOperation("获取执行状态")
    @GetMapping("/execution/status/{executionId}")
    @PreAuthorize("@permission.has('datacollection:task:query')")
    public ResponseDTO<Object> getExecutionStatus(@PathVariable String executionId) {
        Object status = dataCollectionService.getExecutionStatus(executionId);
        return ResponseDTO.ok(status);
    }

    /**
     * 获取执行日志
     */
    @ApiOperation("获取执行日志")
    @GetMapping("/execution/log/{executionId}")
    @PreAuthorize("@permission.has('datacollection:task:query')")
    public ResponseDTO<String> getExecutionLog(@PathVariable String executionId) {
        String log = dataCollectionService.getExecutionLog(executionId);
        return ResponseDTO.ok(log);
    }

    /**
     * 保存流程设计
     */
    @ApiOperation("保存流程设计")
    @PostMapping("/flow/save")
    @PreAuthorize("@permission.has('datacollection:task:add')")
    public ResponseDTO<Void> saveFlowDesign(@RequestBody Map<String, Object> params) {
        // 这里处理流程设计的保存
        // 包括节点、连线、配置等信息
        return ResponseDTO.ok();
    }

    /**
     * 获取流程设计
     */
    @ApiOperation("获取流程设计")
    @GetMapping("/flow/design/{taskId}")
    @PreAuthorize("@permission.has('datacollection:task:query')")
    public ResponseDTO<Object> getFlowDesign(@PathVariable Long taskId) {
        // 返回流程设计的完整信息
        DataCollectionTaskDTO task = dataCollectionService.queryTaskById(taskId);
        return ResponseDTO.ok(task.getFlowDefinition());
    }

    /**
     * 测试数据源连接
     */
    @ApiOperation("测试数据源连接")
    @PostMapping("/datasource/test")
    @PreAuthorize("@permission.has('datacollection:task:add')")
    public ResponseDTO<Boolean> testDataSource(@RequestBody Map<String, Object> params) {
        // 测试数据源连接
        return ResponseDTO.ok(true);
    }

    /**
     * 获取数据源表列表
     */
    @ApiOperation("获取数据源表列表")
    @PostMapping("/datasource/tables")
    @PreAuthorize("@permission.has('datacollection:task:add')")
    public ResponseDTO<List<String>> getDataSourceTables(@RequestBody Map<String, Object> params) {
        // 获取数据源的表列表
        return ResponseDTO.ok(List.of("table1", "table2", "table3"));
    }

    /**
     * 获取表字段信息
     */
    @ApiOperation("获取表字段信息")
    @PostMapping("/datasource/columns")
    @PreAuthorize("@permission.has('datacollection:task:add')")
    public ResponseDTO<List<Object>> getTableColumns(@RequestBody Map<String, Object> params) {
        // 获取表的字段信息
        return ResponseDTO.ok(List.of());
    }
}
