package com.lacus.dao.datacollection.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 流程节点定义实体
 *
 * <AUTHOR>
 */
@Data
@TableName("flow_node_definition")
public class FlowNodeDefinitionEntity {

    /**
     * 节点ID
     */
    @TableId(value = "node_id", type = IdType.AUTO)
    private Long nodeId;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 节点唯一标识
     */
    @TableField("node_key")
    private String nodeKey;

    /**
     * 节点名称
     */
    @TableField("node_name")
    private String nodeName;

    /**
     * 节点类型：START、END、API_CALL、SQL_QUERY、LOOP、CONDITION、DATA_TRANSFORM、DATA_WRITE
     */
    @TableField("node_type")
    private String nodeType;

    /**
     * 节点配置JSON
     */
    @TableField("node_config")
    private String nodeConfig;

    /**
     * X坐标
     */
    @TableField("position_x")
    private Integer positionX;

    /**
     * Y坐标
     */
    @TableField("position_y")
    private Integer positionY;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
}
