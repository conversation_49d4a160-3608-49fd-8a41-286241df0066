package com.lacus.service.datawarehouse.engine;

import com.lacus.dao.datawarehouse.entity.EtlTaskEntity;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * ETL调度管理器
 * 负责管理ETL任务的三种调度类型：MANUAL、CRON、REALTIME
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class EtlScheduleManager {
    
    private static final String JOB_GROUP = "ETL_JOB_GROUP";
    private static final String TRIGGER_GROUP = "ETL_TRIGGER_GROUP";
    
    @Autowired
    private Scheduler scheduler;
    
    @Autowired
    private EtlExecutionEngine etlExecutionEngine;
    
    @Autowired
    private RealtimeProcessor realtimeProcessor;
    
    // 存储正在运行的实时任务
    private final ConcurrentMap<Long, Object> realtimeTasks = new ConcurrentHashMap<>();
    
    // 存储定时任务
    private final ConcurrentMap<Long, JobKey> cronJobs = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        log.info("ETL调度管理器初始化完成");
    }
    
    @PreDestroy
    public void destroy() {
        log.info("ETL调度管理器正在关闭...");
        // 停止所有实时任务
        realtimeTasks.keySet().forEach(this::stopRealtimeTask);
        // 停止所有定时任务
        cronJobs.keySet().forEach(this::stopCronTask);
        log.info("ETL调度管理器已关闭");
    }
    
    /**
     * 启动调度
     */
    public void startSchedule(EtlTaskEntity task) {
        Long taskId = task.getTaskId();
        String scheduleType = task.getScheduleType();
        
        log.info("启动ETL任务调度: taskId={}, scheduleType={}", taskId, scheduleType);
        
        try {
            switch (scheduleType) {
                case "MANUAL":
                    // 手动任务不需要启动调度，只在手动触发时执行
                    log.info("手动任务无需启动调度: taskId={}", taskId);
                    break;
                    
                case "CRON":
                    startCronSchedule(task);
                    break;
                    
                case "REALTIME":
                    startRealtimeSchedule(task);
                    break;
                    
                default:
                    log.warn("未知的调度类型: taskId={}, scheduleType={}", taskId, scheduleType);
            }
        } catch (Exception e) {
            log.error("启动ETL任务调度失败: taskId={}, scheduleType={}", taskId, scheduleType, e);
            throw new RuntimeException("启动调度失败: " + e.getMessage());
        }
    }
    
    /**
     * 停止调度
     */
    public void stopSchedule(Long taskId) {
        log.info("停止ETL任务调度: taskId={}", taskId);
        
        try {
            // 停止定时任务
            stopCronTask(taskId);
            
            // 停止实时任务
            stopRealtimeTask(taskId);
            
            log.info("ETL任务调度已停止: taskId={}", taskId);
        } catch (Exception e) {
            log.error("停止ETL任务调度失败: taskId={}", taskId, e);
        }
    }
    
    /**
     * 启动定时调度
     */
    private void startCronSchedule(EtlTaskEntity task) throws SchedulerException {
        Long taskId = task.getTaskId();
        String cronExpression = task.getCronExpression();
        
        if (cronExpression == null || cronExpression.trim().isEmpty()) {
            throw new IllegalArgumentException("定时任务的Cron表达式不能为空");
        }
        
        // 先停止已存在的任务
        stopCronTask(taskId);
        
        // 创建JobDetail
        JobDetail jobDetail = JobBuilder.newJob(EtlQuartzJob.class)
                .withIdentity(getJobKey(taskId))
                .usingJobData("taskId", taskId)
                .usingJobData("taskName", task.getTaskName())
                .build();
        
        // 创建Trigger
        CronTrigger trigger = TriggerBuilder.newTrigger()
                .withIdentity(getTriggerKey(taskId))
                .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression)
                        .withMisfireHandlingInstructionDoNothing()) // 错过的任务不执行
                .build();
        
        // 调度任务
        scheduler.scheduleJob(jobDetail, trigger);
        cronJobs.put(taskId, getJobKey(taskId));
        
        log.info("定时ETL任务启动成功: taskId={}, cron={}", taskId, cronExpression);
    }
    
    /**
     * 启动实时调度
     */
    private void startRealtimeSchedule(EtlTaskEntity task) {
        Long taskId = task.getTaskId();
        
        // 先停止已存在的实时任务
        stopRealtimeTask(taskId);
        
        // 启动实时处理器
        realtimeProcessor.startRealtimeTask(task);
        realtimeTasks.put(taskId, new Object());
        
        log.info("实时ETL任务启动成功: taskId={}", taskId);
    }
    
    /**
     * 停止定时任务
     */
    private void stopCronTask(Long taskId) {
        try {
            JobKey jobKey = cronJobs.remove(taskId);
            if (jobKey != null) {
                scheduler.deleteJob(jobKey);
                log.info("定时ETL任务已停止: taskId={}", taskId);
            }
        } catch (SchedulerException e) {
            log.error("停止定时ETL任务失败: taskId={}", taskId, e);
        }
    }
    
    /**
     * 停止实时任务
     */
    private void stopRealtimeTask(Long taskId) {
        if (realtimeTasks.remove(taskId) != null) {
            realtimeProcessor.stopRealtimeTask(taskId);
            log.info("实时ETL任务已停止: taskId={}", taskId);
        }
    }
    
    /**
     * 手动执行任务
     */
    public String executeManualTask(Long taskId) {
        log.info("手动执行ETL任务: taskId={}", taskId);
        return etlExecutionEngine.executeTask(taskId);
    }
    
    /**
     * 检查任务是否正在运行
     */
    public boolean isTaskRunning(Long taskId) {
        return etlExecutionEngine.isTaskRunning(taskId);
    }
    
    /**
     * 停止正在运行的任务
     */
    public void stopRunningTask(Long taskId) {
        etlExecutionEngine.stopTask(taskId);
    }
    
    private JobKey getJobKey(Long taskId) {
        return JobKey.jobKey("etl_job_" + taskId, JOB_GROUP);
    }
    
    private TriggerKey getTriggerKey(Long taskId) {
        return TriggerKey.triggerKey("etl_trigger_" + taskId, TRIGGER_GROUP);
    }
}
