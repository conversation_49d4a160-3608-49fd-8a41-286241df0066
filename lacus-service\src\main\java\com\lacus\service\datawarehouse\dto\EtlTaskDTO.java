package com.lacus.service.datawarehouse.dto;

import com.lacus.dao.datawarehouse.entity.EtlFieldMappingEntity;
import com.lacus.dao.datawarehouse.entity.EtlQualityRulesEntity;
import com.lacus.dao.datawarehouse.entity.EtlSourceTableEntity;
import com.lacus.service.datawarehouse.model.EtlTaskModel;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * ETL任务DTO
 */
@Data
public class EtlTaskDTO {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 源数据层级
     */
    private String sourceLayer;

    /**
     * 目标数据层级
     */
    private String targetLayer;

    /**
     * 调度类型
     */
    private String scheduleType;

    /**
     * Cron表达式
     */
    private String cronExpression;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 目标表名
     */
    private String targetTable;

    /**
     * 写入模式
     */
    private String writeMode;

    /**
     * 主键字段
     */
    private String primaryKeys;

    /**
     * 源表配置
     */
    private String sourceTablesConfig;

    /**
     * 字段映射配置
     */
    private String fieldMappingsConfig;

    /**
     * 数据质量规则配置
     */
    private String qualityRulesConfig;

    /**
     * 任务状态
     */
    private Integer status;

    /**
     * 最后执行时间
     */
    private LocalDateTime lastRunTime;

    /**
     * 最后执行状态
     */
    private String lastRunStatus;

    /**
     * 源表数量
     */
    private Integer sourceTableCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 源表列表(运行时解析)
     */
    private List<EtlSourceTableEntity> sourceTables;

    /**
     * 字段映射列表(运行时解析)
     */
    private List<EtlFieldMappingEntity> fieldMappings;

    /**
     * 质量规则列表(运行时解析)
     */
    private List<EtlQualityRulesEntity> qualityRules;

    /**
     * 从模型转换为DTO
     */
    public static EtlTaskDTO fromModel(EtlTaskModel model) {
        EtlTaskDTO dto = new EtlTaskDTO();
        dto.setTaskId(model.getTaskId());
        dto.setTaskName(model.getTaskName());
        dto.setDescription(model.getDescription());
        dto.setSourceLayer(model.getSourceLayer());
        dto.setTargetLayer(model.getTargetLayer());
        dto.setScheduleType(model.getScheduleType());
        dto.setCronExpression(model.getCronExpression());
        dto.setTimezone(model.getTimezone());
        dto.setTargetTable(model.getTargetTable());
        dto.setWriteMode(model.getWriteMode());
        dto.setSourceTablesConfig(model.getSourceTablesConfig());
        dto.setFieldMappingsConfig(model.getFieldMappingsConfig());
        dto.setQualityRulesConfig(model.getQualityRulesConfig());
        dto.setStatus(model.getStatus());
        dto.setLastRunTime(model.getLastRunTime());
        dto.setLastRunStatus(model.getLastRunStatus());
        dto.setSourceTableCount(model.getSourceTableCount());
        dto.setCreateTime(model.getCreateTime());
        dto.setUpdateTime(model.getUpdateTime());
        dto.setCreateBy(model.getCreateBy());
        dto.setUpdateBy(model.getUpdateBy());
        return dto;
    }
}
