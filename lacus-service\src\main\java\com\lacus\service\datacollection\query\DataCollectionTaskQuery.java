package com.lacus.service.datacollection.query;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lacus.dao.system.query.AbstractPageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 数据采集任务查询条件
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataCollectionTaskQuery extends AbstractPageQuery {

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 采集类型：API、SQL
     */
    private String collectionType;

    /**
     * 数据源类型：MYSQL、SQLSERVER、API
     */
    private String sourceType;

    /**
     * 目标数据库
     */
    private String targetDatabase;

    /**
     * 目标表名
     */
    private String targetTable;

    /**
     * 写入模式：OVERWRITE、APPEND、UPSERT、AUTO_CREATE
     */
    private String writeMode;

    /**
     * 调度类型：MANUAL、CRON、REALTIME
     */
    private String scheduleType;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    private Date endTime;

    @Override
    public QueryWrapper toQueryWrapper() {
        return null;
    }
}
