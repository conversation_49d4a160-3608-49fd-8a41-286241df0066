<template>
  <div class="start-node-config">
    <el-form :model="config" label-width="100px" size="small">
      <el-form-item label="节点描述">
        <el-input 
          v-model="config.description" 
          type="textarea"
          :rows="3"
          placeholder="请输入开始节点的描述信息"
        />
      </el-form-item>

      <el-form-item label="初始变量">
        <div class="variable-list">
          <div 
            v-for="(variable, index) in config.initialVariables" 
            :key="index"
            class="variable-item"
          >
            <el-input 
              v-model="variable.name" 
              placeholder="变量名"
              style="width: 120px; margin-right: 8px;"
            />
            <el-select 
              v-model="variable.type" 
              placeholder="类型"
              style="width: 100px; margin-right: 8px;"
            >
              <el-option label="字符串" value="STRING" />
              <el-option label="数字" value="NUMBER" />
              <el-option label="布尔" value="BOOLEAN" />
              <el-option label="对象" value="OBJECT" />
            </el-select>
            <el-input 
              v-model="variable.value" 
              placeholder="初始值"
              style="width: 120px; margin-right: 8px;"
            />
            <el-button 
              type="danger" 
              size="small" 
              icon="Delete"
              @click="removeVariable(index)"
            />
          </div>
          <el-button 
            type="primary" 
            size="small" 
            icon="Plus"
            @click="addVariable"
          >
            添加变量
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="执行前检查">
        <el-checkbox-group v-model="config.preChecks">
          <el-checkbox label="checkDataSource">检查数据源连接</el-checkbox>
          <el-checkbox label="checkTargetTable">检查目标表存在</el-checkbox>
          <el-checkbox label="checkPermissions">检查执行权限</el-checkbox>
          <el-checkbox label="validateConfig">验证配置完整性</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="超时设置">
        <el-input-number 
          v-model="config.timeout" 
          :min="1"
          :max="3600"
          placeholder="秒"
        />
        <span class="form-tip">流程总超时时间（秒）</span>
      </el-form-item>

      <el-form-item label="并发控制">
        <el-switch 
          v-model="config.concurrent" 
          active-text="允许并发"
          inactive-text="串行执行"
        />
        <div class="form-tip">是否允许多个实例同时执行</div>
      </el-form-item>
    </el-form>

    <!-- 配置预览 -->
    <el-divider content-position="left">配置预览</el-divider>
    <div class="config-preview">
      <el-alert 
        title="开始节点：流程执行的起点，负责初始化变量和执行前检查" 
        type="success" 
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  node: {
    type: Object,
    default: () => ({})
  },
  detailed: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

// 配置数据
const config = ref({
  description: '',
  initialVariables: [],
  preChecks: ['validateConfig'],
  timeout: 300,
  concurrent: false,
  ...props.modelValue
})

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', newConfig)
}, { deep: true })

// 添加变量
function addVariable() {
  config.value.initialVariables.push({
    name: '',
    type: 'STRING',
    value: ''
  })
}

// 删除变量
function removeVariable(index) {
  config.value.initialVariables.splice(index, 1)
}
</script>

<style scoped>
.start-node-config {
  max-height: 600px;
  overflow-y: auto;
}

.variable-list {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}

.variable-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.variable-item:last-child {
  margin-bottom: 12px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
  line-height: 1.4;
}

.config-preview {
  margin-top: 16px;
}

.el-divider {
  margin: 20px 0 16px 0;
}
</style>
