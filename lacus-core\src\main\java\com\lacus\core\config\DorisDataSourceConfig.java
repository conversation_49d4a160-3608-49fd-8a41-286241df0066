package com.lacus.core.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * Doris数据源配置
 * 
 * <AUTHOR>
 */
@Configuration
public class DorisDataSourceConfig {

    /**
     * 配置Doris数据源
     */
    @Bean(name = "dorisDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.doris")
    public DataSource dorisDataSource() {
        return new DruidDataSource();
    }

    /**
     * 配置Doris JdbcTemplate
     */
    @Bean(name = "dorisJdbcTemplate")
    public JdbcTemplate dorisJdbcTemplate() {
        return new JdbcTemplate(dorisDataSource());
    }
}
