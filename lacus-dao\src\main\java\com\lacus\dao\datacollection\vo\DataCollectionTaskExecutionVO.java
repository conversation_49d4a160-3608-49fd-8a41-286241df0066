package com.lacus.dao.datacollection.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据采集任务执行记录VO
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class DataCollectionTaskExecutionVO {

    /**
     * 执行ID
     */
    private String executionId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务编码
     */
    private String taskCode;

    /**
     * 执行状态(RUNNING/SUCCESS/FAILED/CANCELLED)
     */
    private String status;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 执行时长(毫秒)
     */
    private Long duration;

    /**
     * 处理记录数
     */
    private Long processedRecords;

    /**
     * 成功记录数
     */
    private Long successRecords;

    /**
     * 失败记录数
     */
    private Long failedRecords;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行日志
     */
    private String executionLog;

    /**
     * 触发方式(MANUAL/SCHEDULE/API)
     */
    private String triggerType;

    /**
     * 触发用户
     */
    private String triggerUser;

    /**
     * 执行参数
     */
    private String executionParams;

    /**
     * 执行结果
     */
    private String executionResult;

    /**
     * 数据源信息
     */
    private String sourceInfo;

    /**
     * 目标信息
     */
    private String targetInfo;

    /**
     * 执行进度(0-100)
     */
    private Integer progress;

    /**
     * 当前步骤
     */
    private String currentStep;

    /**
     * 总步骤数
     */
    private Integer totalSteps;

    /**
     * 已完成步骤数
     */
    private Integer completedSteps;

    /**
     * 状态文本
     */
    private String statusText;

    /**
     * 触发方式文本
     */
    private String triggerTypeText;

    /**
     * 执行时长文本
     */
    private String durationText;

    /**
     * 处理速度(记录/秒)
     */
    private Double processSpeed;

    /**
     * 数据大小
     */
    private String dataSize;

    /**
     * 是否可以重试
     */
    private Boolean canRetry;

    /**
     * 是否可以取消
     */
    private Boolean canCancel;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 执行节点
     */
    private String executionNode;

    /**
     * 资源使用情况
     */
    private String resourceUsage;
}
