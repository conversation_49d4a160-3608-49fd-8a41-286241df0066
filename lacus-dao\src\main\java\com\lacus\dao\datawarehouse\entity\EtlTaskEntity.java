package com.lacus.dao.datawarehouse.entity;

import com.lacus.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * ETL任务表
 */
@Getter
@Setter
@TableName("etl_task")
@ApiModel(value = "EtlTaskEntity对象", description = "ETL任务表")
public class EtlTaskEntity extends BaseEntity<EtlTaskEntity> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("任务ID")
    @TableId(value = "task_id", type = IdType.AUTO)
    private Long taskId;

    @ApiModelProperty("任务名称")
    @TableField("task_name")
    private String taskName;

    @ApiModelProperty("任务描述")
    @TableField("description")
    private String description;

    @ApiModelProperty("源数据层级")
    @TableField("source_layer")
    private String sourceLayer;

    @ApiModelProperty("目标数据层级")
    @TableField("target_layer")
    private String targetLayer;

    @ApiModelProperty("调度类型")
    @TableField("schedule_type")
    private String scheduleType;

    @ApiModelProperty("Cron表达式")
    @TableField("cron_expression")
    private String cronExpression;

    @ApiModelProperty("时区")
    @TableField("timezone")
    private String timezone;

    @ApiModelProperty("目标表名")
    @TableField("target_table")
    private String targetTable;

    @ApiModelProperty("写入模式")
    @TableField("write_mode")
    private String writeMode;

    @ApiModelProperty("主键字段")
    @TableField("primary_keys")
    private String primaryKeys;

    @ApiModelProperty("源表配置")
    @TableField("source_tables_config")
    private String sourceTablesConfig;

    @ApiModelProperty("字段映射配置")
    @TableField("field_mappings_config")
    private String fieldMappingsConfig;

    @ApiModelProperty("数据质量规则配置")
    @TableField("quality_rules_config")
    private String qualityRulesConfig;

    @ApiModelProperty("任务状态")
    @TableField("status")
    private Integer status;

    @ApiModelProperty("最后执行时间")
    @TableField("last_run_time")
    private Date lastRunTime;

    @ApiModelProperty("最后执行状态")
    @TableField("last_run_status")
    private String lastRunStatus;

    @ApiModelProperty("源表数量")
    @TableField("source_table_count")
    private Integer sourceTableCount;

    @Override
    public Serializable pkVal() {
        return this.taskId;
    }
}
