<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lacus.datacollection.mapper.DataCollectionCategoryMapper">
    
    <resultMap type="DataCollectionCategory" id="DataCollectionCategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="categoryCode"    column="category_code"    />
        <result property="parentId"    column="parent_id"    />
        <result property="categoryPath"    column="category_path"    />
        <result property="categoryLevel"    column="category_level"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="categoryIcon"    column="category_icon"    />
        <result property="categoryDesc"    column="category_desc"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDataCollectionCategoryVo">
        select category_id, category_name, category_code, parent_id, category_path, category_level, sort_order, category_icon, category_desc, status, create_by, create_time, update_by, update_time, remark from data_collection_category
    </sql>

    <select id="selectDataCollectionCategoryList" parameterType="DataCollectionCategory" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        <where>  
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="categoryCode != null  and categoryCode != ''"> and category_code = #{categoryCode}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="categoryPath != null  and categoryPath != ''"> and category_path = #{categoryPath}</if>
            <if test="categoryLevel != null "> and category_level = #{categoryLevel}</if>
            <if test="categoryIcon != null  and categoryIcon != ''"> and category_icon = #{categoryIcon}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by parent_id, sort_order, category_id
    </select>
    
    <select id="selectDataCollectionCategoryByCategoryId" parameterType="Long" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where category_id = #{categoryId}
    </select>
        
    <insert id="insertDataCollectionCategory" parameterType="DataCollectionCategory" useGeneratedKeys="true" keyProperty="categoryId">
        insert into data_collection_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="categoryCode != null and categoryCode != ''">category_code,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="categoryPath != null">category_path,</if>
            <if test="categoryLevel != null">category_level,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="categoryIcon != null">category_icon,</if>
            <if test="categoryDesc != null">category_desc,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="categoryCode != null and categoryCode != ''">#{categoryCode},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="categoryPath != null">#{categoryPath},</if>
            <if test="categoryLevel != null">#{categoryLevel},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="categoryIcon != null">#{categoryIcon},</if>
            <if test="categoryDesc != null">#{categoryDesc},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDataCollectionCategory" parameterType="DataCollectionCategory">
        update data_collection_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="categoryCode != null and categoryCode != ''">category_code = #{categoryCode},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="categoryPath != null">category_path = #{categoryPath},</if>
            <if test="categoryLevel != null">category_level = #{categoryLevel},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="categoryIcon != null">category_icon = #{categoryIcon},</if>
            <if test="categoryDesc != null">category_desc = #{categoryDesc},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteDataCollectionCategoryByCategoryId" parameterType="Long">
        delete from data_collection_category where category_id = #{categoryId}
    </delete>

    <delete id="deleteDataCollectionCategoryByCategoryIds" parameterType="String">
        delete from data_collection_category where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

    <select id="checkCategoryCodeUnique" parameterType="String" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where category_code = #{categoryCode} limit 1
    </select>

    <select id="checkCategoryNameUnique" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where category_name = #{categoryName} and parent_id = #{parentId} limit 1
    </select>

    <select id="hasChildCategory" parameterType="Long" resultType="int">
        select count(1) from data_collection_category where parent_id = #{categoryId}
    </select>

    <select id="selectCategoryByPath" parameterType="String" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where category_path = #{categoryPath} limit 1
    </select>

    <select id="selectCategoryByCode" parameterType="String" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where category_code = #{categoryCode} limit 1
    </select>

    <update id="updateCategoryPath">
        update data_collection_category set category_path = #{categoryPath} where category_id = #{categoryId}
    </update>

    <update id="updateTaskCount">
        update data_collection_category set task_count = #{taskCount} where category_id = #{categoryId}
    </update>

    <select id="selectCategoryByParentId" parameterType="Long" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where parent_id = #{parentId}
        order by sort_order, category_id
    </select>

    <select id="getMaxSortOrder" parameterType="Long" resultType="Integer">
        select max(sort_order) from data_collection_category where parent_id = #{parentId}
    </select>

    <update id="batchUpdateStatus">
        update data_collection_category set status = #{status} where category_id in
        <foreach item="categoryId" collection="categoryIds" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </update>

    <select id="selectCategoryByNameLike" parameterType="String" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where category_name like concat('%', #{categoryName}, '%')
        order by category_level, sort_order
    </select>

    <select id="selectCategoryLevelStats" resultType="java.util.Map">
        select category_level as level, count(*) as count
        from data_collection_category
        where status = 1
        group by category_level
        order by category_level
    </select>

    <select id="selectCategoryStatusStats" resultType="java.util.Map">
        select status, count(*) as count
        from data_collection_category
        group by status
    </select>

    <select id="selectCategoryByLevel" parameterType="Integer" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where category_level = #{categoryLevel}
        order by sort_order, category_id
    </select>

    <select id="selectCategoryFullPath" parameterType="Long" resultType="String">
        WITH RECURSIVE category_path AS (
            SELECT category_id, category_name, parent_id, category_name as full_path
            FROM data_collection_category
            WHERE category_id = #{categoryId}
            
            UNION ALL
            
            SELECT c.category_id, c.category_name, c.parent_id, 
                   CONCAT(c.category_name, ' > ', cp.full_path) as full_path
            FROM data_collection_category c
            INNER JOIN category_path cp ON c.category_id = cp.parent_id
        )
        SELECT full_path FROM category_path WHERE parent_id = 0 OR parent_id IS NULL
    </select>

    <select id="checkCircularReference" resultType="boolean">
        WITH RECURSIVE category_hierarchy AS (
            SELECT category_id, parent_id, 1 as level
            FROM data_collection_category
            WHERE category_id = #{parentId}
            
            UNION ALL
            
            SELECT c.category_id, c.parent_id, ch.level + 1
            FROM data_collection_category c
            INNER JOIN category_hierarchy ch ON c.parent_id = ch.category_id
            WHERE ch.level &lt; 10
        )
        SELECT COUNT(*) > 0 FROM category_hierarchy WHERE category_id = #{categoryId}
    </select>

    <select id="selectAncestorCategoryIds" parameterType="Long" resultType="Long">
        WITH RECURSIVE category_ancestors AS (
            SELECT category_id, parent_id
            FROM data_collection_category
            WHERE category_id = #{categoryId}
            
            UNION ALL
            
            SELECT c.category_id, c.parent_id
            FROM data_collection_category c
            INNER JOIN category_ancestors ca ON c.category_id = ca.parent_id
        )
        SELECT category_id FROM category_ancestors WHERE category_id != #{categoryId}
    </select>

    <select id="selectDescendantCategoryIds" parameterType="Long" resultType="Long">
        WITH RECURSIVE category_descendants AS (
            SELECT category_id, parent_id
            FROM data_collection_category
            WHERE parent_id = #{categoryId}
            
            UNION ALL
            
            SELECT c.category_id, c.parent_id
            FROM data_collection_category c
            INNER JOIN category_descendants cd ON c.parent_id = cd.category_id
        )
        SELECT category_id FROM category_descendants
    </select>

    <select id="selectCategoryByCreateTime" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where create_time between #{startTime} and #{endTime}
        order by create_time desc
    </select>

    <select id="selectCategoryCreateStats" resultType="java.util.Map">
        select DATE(create_time) as date, count(*) as count
        from data_collection_category
        where create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        group by DATE(create_time)
        order by date
    </select>

    <select id="selectHotCategories" resultMap="DataCollectionCategoryResult">
        select c.*, COALESCE(t.task_count, 0) as task_count
        from data_collection_category c
        left join (
            select category_id, count(*) as task_count
            from data_collection_task
            where status = 1
            group by category_id
        ) t on c.category_id = t.category_id
        where c.status = 1
        order by task_count desc, c.category_name
        limit #{limit}
    </select>

    <select id="selectEmptyCategories" resultMap="DataCollectionCategoryResult">
        select c.*
        from data_collection_category c
        left join data_collection_task t on c.category_id = t.category_id
        where c.status = 1 and t.category_id is null
        order by c.category_level, c.sort_order
    </select>

    <select id="selectCategoryUsageStats" resultType="java.util.Map">
        select 
            c.category_id,
            c.category_name,
            c.category_level,
            COALESCE(t.task_count, 0) as task_count,
            COALESCE(t.enabled_task_count, 0) as enabled_task_count,
            COALESCE(t.disabled_task_count, 0) as disabled_task_count
        from data_collection_category c
        left join (
            select 
                category_id,
                count(*) as task_count,
                sum(case when status = 1 then 1 else 0 end) as enabled_task_count,
                sum(case when status = 0 then 1 else 0 end) as disabled_task_count
            from data_collection_task
            group by category_id
        ) t on c.category_id = t.category_id
        where c.status = 1
        order by c.category_level, c.sort_order
    </select>

</mapper>
