package com.lacus.dao.datacollection.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 数据采集流程VO
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class DataCollectionFlowVO {

    /**
     * 流程ID
     */
    private String flowId;

    /**
     * 流程名称
     */
    private String flowName;

    /**
     * 流程描述
     */
    private String flowDesc;

    /**
     * 流程版本
     */
    private String version;

    /**
     * 流程状态
     */
    private String status;

    /**
     * 流程定义JSON
     */
    private String flowDefinition;

    /**
     * 节点列表
     */
    private List<FlowNodeVO> nodes;

    /**
     * 连线列表
     */
    private List<FlowEdgeVO> edges;

    /**
     * 流程配置
     */
    private Map<String, Object> config;

    /**
     * 全局参数
     */
    private Map<String, Object> globalParams;

    /**
     * 流程变量
     */
    private Map<String, Object> variables;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 流程节点VO
     */
    @Data
    public static class FlowNodeVO {
        /**
         * 节点ID
         */
        private String nodeId;

        /**
         * 节点名称
         */
        private String nodeName;

        /**
         * 节点类型
         */
        private String nodeType;

        /**
         * 节点配置
         */
        private Map<String, Object> nodeConfig;

        /**
         * 位置信息
         */
        private Map<String, Object> position;

        /**
         * 样式信息
         */
        private Map<String, Object> style;

        /**
         * 是否启用
         */
        private Boolean enabled;

        /**
         * 描述
         */
        private String description;
    }

    /**
     * 流程连线VO
     */
    @Data
    public static class FlowEdgeVO {
        /**
         * 连线ID
         */
        private String edgeId;

        /**
         * 源节点ID
         */
        private String sourceNodeId;

        /**
         * 目标节点ID
         */
        private String targetNodeId;

        /**
         * 连线类型
         */
        private String edgeType;

        /**
         * 连线配置
         */
        private Map<String, Object> edgeConfig;

        /**
         * 条件表达式
         */
        private String condition;

        /**
         * 样式信息
         */
        private Map<String, Object> style;

        /**
         * 描述
         */
        private String description;
    }
}
