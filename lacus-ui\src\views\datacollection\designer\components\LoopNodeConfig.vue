<template>
  <div class="loop-node-config">
    <el-form :model="config" label-width="100px" size="small">
      <!-- 循环类型 -->
      <el-form-item label="循环类型" required>
        <el-select v-model="config.loopType" placeholder="请选择循环类型" @change="handleLoopTypeChange">
          <el-option label="计数循环" value="COUNT" />
          <el-option label="条件循环" value="CONDITION" />
          <el-option label="数据列表循环" value="DATA_LIST" />
          <el-option label="分页查询循环" value="PAGE_QUERY" />
        </el-select>
      </el-form-item>

      <!-- 计数循环配置 -->
      <template v-if="config.loopType === 'COUNT'">
        <el-form-item label="循环次数" required>
          <el-input-number 
            v-model="config.count" 
            :min="1" 
            :max="10000"
            placeholder="请输入循环次数"
          />
        </el-form-item>
        <el-form-item label="循环变量">
          <el-input 
            v-model="config.loopVariable" 
            placeholder="循环计数器变量名，如：i"
          />
          <div class="form-tip">循环变量会自动递增，可在后续节点中使用 ${i} 引用</div>
        </el-form-item>
      </template>

      <!-- 条件循环配置 -->
      <template v-if="config.loopType === 'CONDITION'">
        <el-form-item label="循环条件" required>
          <el-input 
            v-model="config.condition" 
            type="textarea"
            :rows="3"
            placeholder="请输入循环条件表达式，如：${count} < 100"
          />
          <div class="form-tip">支持变量引用，如 ${variableName}，条件为true时继续循环</div>
        </el-form-item>
        <el-form-item label="最大循环次数">
          <el-input-number 
            v-model="config.maxIterations" 
            :min="1" 
            :max="10000"
            placeholder="防止无限循环"
          />
        </el-form-item>
      </template>

      <!-- 数据列表循环配置 -->
      <template v-if="config.loopType === 'DATA_LIST'">
        <el-form-item label="数据源变量" required>
          <el-input 
            v-model="config.dataSource" 
            placeholder="数据列表变量名，如：userList"
          />
          <div class="form-tip">引用上游节点输出的数组数据</div>
        </el-form-item>
        <el-form-item label="当前项变量">
          <el-input 
            v-model="config.itemVariable" 
            placeholder="当前循环项变量名，如：currentUser"
          />
          <div class="form-tip">每次循环时，当前数据项会赋值给此变量</div>
        </el-form-item>
        <el-form-item label="索引变量">
          <el-input 
            v-model="config.indexVariable" 
            placeholder="当前索引变量名，如：index"
          />
          <div class="form-tip">当前循环的索引位置（从0开始）</div>
        </el-form-item>
      </template>

      <!-- 分页查询循环配置 -->
      <template v-if="config.loopType === 'PAGE_QUERY'">
        <el-form-item label="页大小" required>
          <el-input-number 
            v-model="config.pageSize" 
            :min="1" 
            :max="10000"
            placeholder="每页数据量"
          />
        </el-form-item>
        <el-form-item label="起始页码">
          <el-input-number 
            v-model="config.startPage" 
            :min="1"
            placeholder="起始页码，默认为1"
          />
        </el-form-item>
        <el-form-item label="最大页数">
          <el-input-number 
            v-model="config.maxPages" 
            :min="1"
            placeholder="最大查询页数，防止无限循环"
          />
        </el-form-item>
        <el-form-item label="页码变量">
          <el-input 
            v-model="config.pageVariable" 
            placeholder="页码变量名，如：pageNum"
          />
          <div class="form-tip">当前页码会赋值给此变量，供查询节点使用</div>
        </el-form-item>
        <el-form-item label="页大小变量">
          <el-input 
            v-model="config.pageSizeVariable" 
            placeholder="页大小变量名，如：pageSize"
          />
          <div class="form-tip">页大小会赋值给此变量，供查询节点使用</div>
        </el-form-item>
        <el-form-item label="结束条件">
          <el-input 
            v-model="config.endCondition" 
            type="textarea"
            :rows="2"
            placeholder="循环结束条件，如：${resultCount} < ${pageSize}"
          />
          <div class="form-tip">当满足此条件时提前结束循环，通常用于检查返回数据量</div>
        </el-form-item>
      </template>

      <!-- 高级配置 -->
      <el-divider content-position="left">高级配置</el-divider>
      
      <el-form-item label="并发执行">
        <el-switch 
          v-model="config.concurrent" 
          active-text="启用"
          inactive-text="禁用"
        />
        <div class="form-tip">是否并发执行循环体内的节点（谨慎使用）</div>
      </el-form-item>

      <el-form-item label="错误处理">
        <el-select v-model="config.errorHandling" placeholder="请选择错误处理策略">
          <el-option label="停止循环" value="STOP" />
          <el-option label="跳过当前项" value="SKIP" />
          <el-option label="重试当前项" value="RETRY" />
        </el-select>
      </el-form-item>

      <el-form-item label="重试次数" v-if="config.errorHandling === 'RETRY'">
        <el-input-number 
          v-model="config.retryCount" 
          :min="1" 
          :max="10"
          placeholder="单项重试次数"
        />
      </el-form-item>

      <el-form-item label="循环延迟">
        <el-input-number 
          v-model="config.delayMs" 
          :min="0"
          placeholder="每次循环间隔毫秒数"
        />
        <div class="form-tip">避免过于频繁的请求，单位：毫秒</div>
      </el-form-item>

      <!-- 变量输出配置 -->
      <el-divider content-position="left">变量输出</el-divider>
      
      <el-form-item label="输出变量">
        <el-input 
          v-model="config.outputVariable" 
          placeholder="循环结果输出变量名，如：loopResult"
        />
        <div class="form-tip">循环执行结果会保存到此变量中</div>
      </el-form-item>

      <el-form-item label="统计信息">
        <el-checkbox-group v-model="config.statistics">
          <el-checkbox label="totalCount">总循环次数</el-checkbox>
          <el-checkbox label="successCount">成功次数</el-checkbox>
          <el-checkbox label="errorCount">错误次数</el-checkbox>
          <el-checkbox label="duration">执行时长</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <!-- 配置预览 -->
    <el-divider content-position="left">配置预览</el-divider>
    <div class="config-preview">
      <el-alert 
        :title="getConfigSummary()" 
        type="info" 
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  node: {
    type: Object,
    default: () => ({})
  },
  detailed: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

// 配置数据
const config = ref({
  loopType: 'COUNT',
  count: 10,
  condition: '',
  maxIterations: 1000,
  dataSource: '',
  itemVariable: 'item',
  indexVariable: 'index',
  pageSize: 100,
  startPage: 1,
  maxPages: 100,
  pageVariable: 'pageNum',
  pageSizeVariable: 'pageSize',
  endCondition: '',
  loopVariable: 'i',
  concurrent: false,
  errorHandling: 'STOP',
  retryCount: 3,
  delayMs: 0,
  outputVariable: 'loopResult',
  statistics: ['totalCount', 'successCount'],
  ...props.modelValue
})

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', newConfig)
}, { deep: true })

// 循环类型变化处理
function handleLoopTypeChange() {
  // 重置相关配置
  switch (config.value.loopType) {
    case 'COUNT':
      config.value.count = config.value.count || 10
      config.value.loopVariable = config.value.loopVariable || 'i'
      break
    case 'CONDITION':
      config.value.maxIterations = config.value.maxIterations || 1000
      break
    case 'DATA_LIST':
      config.value.itemVariable = config.value.itemVariable || 'item'
      config.value.indexVariable = config.value.indexVariable || 'index'
      break
    case 'PAGE_QUERY':
      config.value.pageSize = config.value.pageSize || 100
      config.value.startPage = config.value.startPage || 1
      config.value.pageVariable = config.value.pageVariable || 'pageNum'
      config.value.pageSizeVariable = config.value.pageSizeVariable || 'pageSize'
      break
  }
}

// 配置摘要
const getConfigSummary = computed(() => {
  switch (config.value.loopType) {
    case 'COUNT':
      return `计数循环：执行 ${config.value.count || 0} 次，变量：${config.value.loopVariable || 'i'}`
    case 'CONDITION':
      return `条件循环：${config.value.condition || '未设置条件'}，最大 ${config.value.maxIterations || 1000} 次`
    case 'DATA_LIST':
      return `数据循环：遍历 ${config.value.dataSource || '未设置数据源'}，当前项：${config.value.itemVariable || 'item'}`
    case 'PAGE_QUERY':
      return `分页循环：每页 ${config.value.pageSize || 100} 条，最大 ${config.value.maxPages || 100} 页`
    default:
      return '未配置循环类型'
  }
})
</script>

<style scoped>
.loop-node-config {
  max-height: 600px;
  overflow-y: auto;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.config-preview {
  margin-top: 16px;
}

.el-divider {
  margin: 20px 0 16px 0;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.el-alert {
  margin-bottom: 0;
}
</style>
