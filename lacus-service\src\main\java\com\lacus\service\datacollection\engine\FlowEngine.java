package com.lacus.service.datacollection.engine;

import com.lacus.service.datacollection.model.FlowContext;
import com.lacus.service.datacollection.model.FlowDefinition;

/**
 * 流程引擎接口
 *
 * <AUTHOR>
 */
public interface FlowEngine {

    /**
     * 执行流程
     *
     * @param flowDefinition 流程定义
     * @param context 执行上下文
     * @return 执行结果
     */
    FlowExecutionResult execute(FlowDefinition flowDefinition, FlowContext context);

    /**
     * 停止流程执行
     *
     * @param executionId 执行ID
     */
    void stopExecution(String executionId);

    /**
     * 暂停流程执行
     *
     * @param executionId 执行ID
     */
    void pauseExecution(String executionId);

    /**
     * 恢复流程执行
     *
     * @param executionId 执行ID
     */
    void resumeExecution(String executionId);

    /**
     * 获取执行状态
     *
     * @param executionId 执行ID
     * @return 执行状态
     */
    FlowExecutionStatus getExecutionStatus(String executionId);

    /**
     * 流程执行结果
     */
    class FlowExecutionResult {
        private String executionId;
        private String status;
        private String message;
        private Object result;
        private FlowContext context;

        // 构造函数
        public FlowExecutionResult(String executionId, String status, String message, Object result, FlowContext context) {
            this.executionId = executionId;
            this.status = status;
            this.message = message;
            this.result = result;
            this.context = context;
        }

        // Getters and Setters
        public String getExecutionId() { return executionId; }
        public void setExecutionId(String executionId) { this.executionId = executionId; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Object getResult() { return result; }
        public void setResult(Object result) { this.result = result; }
        public FlowContext getContext() { return context; }
        public void setContext(FlowContext context) { this.context = context; }
    }

    /**
     * 流程执行状态
     */
    class FlowExecutionStatus {
        private String executionId;
        private String status;
        private String currentNodeId;
        private Integer progress;
        private String message;

        // 构造函数
        public FlowExecutionStatus(String executionId, String status, String currentNodeId, Integer progress, String message) {
            this.executionId = executionId;
            this.status = status;
            this.currentNodeId = currentNodeId;
            this.progress = progress;
            this.message = message;
        }

        // Getters and Setters
        public String getExecutionId() { return executionId; }
        public void setExecutionId(String executionId) { this.executionId = executionId; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getCurrentNodeId() { return currentNodeId; }
        public void setCurrentNodeId(String currentNodeId) { this.currentNodeId = currentNodeId; }
        public Integer getProgress() { return progress; }
        public void setProgress(Integer progress) { this.progress = progress; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
