package com.lacus.service.datawarehouse.engine;

import lombok.Builder;
import lombok.Data;

/**
 * ETL执行结果
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class EtlExecutionResult {
    
    /**
     * 执行ID
     */
    private String executionId;

    /**
     * 执行状态
     */
    private String status;
    
    /**
     * 处理的行数
     */
    private long processedRows;
    
    /**
     * 错误行数
     */
    private long errorRows;
    
    /**
     * 执行时长（毫秒）
     */
    private long duration;
    
    /**
     * 是否成功
     */
    private boolean resultSuccess;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 开始时间
     */
    private long startTime;
    
    /**
     * 结束时间
     */
    private long endTime;
    
    /**
     * 执行的SQL数量
     */
    private int sqlCount;
    
    /**
     * 额外信息
     */
    private String additionalInfo;
}
