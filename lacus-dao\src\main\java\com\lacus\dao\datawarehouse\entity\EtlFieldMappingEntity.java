package com.lacus.dao.datawarehouse.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lacus.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * ETL数据源配置表
 */
@Getter
@Setter
@TableName("etl_field_mapping")
@ApiModel(value = "EtlQualityRulesEntity对象", description = "ETL字段映射配置")
public class EtlFieldMappingEntity extends BaseEntity<EtlFieldMappingEntity> {

    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("目标字段")
    @TableField("target_field")
    private String targetField;

    @ApiModelProperty("字段类型")
    @TableField("field_type")
    private String fieldType;

    @ApiModelProperty("转换表达式")
    @TableField("expression")
    private String expression;

    @ApiModelProperty("默认值")
    @TableField("default_value")
    private String defaultValue;

    @ApiModelProperty("目标源表表格名称")
    @TableField("source_table")
    private String sourceTable;

    @ApiModelProperty("目标源表字段")
    @TableField("source_field")
    private String sourceField;

    @ApiModelProperty("匹配分数")
    @TableField("match_score")
    private Integer matchScore;

    @ApiModelProperty("匹配理由")
    @TableField("match_reason")
    private String matchReason;

    @ApiModelProperty("关联的任务ID")
    @TableField("etl_task_id")
    private Long etlTaskId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
