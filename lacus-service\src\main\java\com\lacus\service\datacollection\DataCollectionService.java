package com.lacus.service.datacollection;

import com.lacus.service.datacollection.dto.DataCollectionTaskDTO;
import com.lacus.service.datacollection.command.DataCollectionTaskAddCommand;
import com.lacus.service.datacollection.query.DataCollectionTaskQuery;
import com.lacus.common.core.page.PageDTO;

import java.util.List;

/**
 * 数据采集服务接口
 *
 * <AUTHOR>
 */
public interface DataCollectionService {

    /**
     * 查询数据采集任务列表
     *
     * @param query 查询条件
     * @return 任务列表
     */
    PageDTO<DataCollectionTaskDTO> queryTaskList(DataCollectionTaskQuery query);

    /**
     * 根据ID查询数据采集任务
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    DataCollectionTaskDTO queryTaskById(Long taskId);

    /**
     * 新增数据采集任务
     *
     * @param command 新增命令
     * @return 任务ID
     */
    Long saveTask(DataCollectionTaskAddCommand command);

    /**
     * 修改数据采集任务
     *
     * @param command 修改命令
     */
    void updateTask(DataCollectionTaskAddCommand command);

    /**
     * 删除数据采集任务
     *
     * @param taskIds 任务ID列表
     */
    void deleteTask(List<Long> taskIds);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     */
    void updateTaskStatus(Long taskId, Integer status);

    /**
     * 执行数据采集任务
     *
     * @param taskId 任务ID
     * @return 执行ID
     */
    String executeTask(Long taskId);

    /**
     * 停止数据采集任务
     *
     * @param taskId 任务ID
     */
    void stopTask(Long taskId);

    /**
     * 验证流程定义
     *
     * @param flowDefinition 流程定义JSON
     * @return 验证结果
     */
    ValidationResult validateFlowDefinition(String flowDefinition);

    /**
     * 预览流程执行
     *
     * @param taskId 任务ID
     * @return 预览结果
     */
    Object previewExecution(Long taskId);

    /**
     * 获取执行历史
     *
     * @param taskId 任务ID
     * @return 执行历史
     */
    List<Object> getExecutionHistory(Long taskId);

    /**
     * 获取执行状态
     *
     * @param executionId 执行ID
     * @return 执行状态
     */
    Object getExecutionStatus(String executionId);

    /**
     * 获取执行日志
     *
     * @param executionId 执行ID
     * @return 执行日志
     */
    String getExecutionLog(String executionId);

    /**
     * 验证结果
     */
    class ValidationResult {
        private boolean valid;
        private String message;
        private List<String> errors;

        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public ValidationResult(boolean valid, String message, List<String> errors) {
            this.valid = valid;
            this.message = message;
            this.errors = errors;
        }

        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
    }
}
