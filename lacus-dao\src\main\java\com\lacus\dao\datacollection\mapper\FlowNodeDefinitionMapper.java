package com.lacus.dao.datacollection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lacus.dao.datacollection.entity.FlowNodeDefinitionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程节点定义Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FlowNodeDefinitionMapper extends BaseMapper<FlowNodeDefinitionEntity> {

    /**
     * 根据任务ID查询节点列表
     *
     * @param taskId 任务ID
     * @return 节点列表
     */
    List<FlowNodeDefinitionEntity> selectByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据任务ID删除节点
     *
     * @param taskId 任务ID
     * @return 删除行数
     */
    int deleteByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据节点Key查询节点
     *
     * @param taskId 任务ID
     * @param nodeKey 节点Key
     * @return 节点实体
     */
    FlowNodeDefinitionEntity selectByNodeKey(@Param("taskId") Long taskId, @Param("nodeKey") String nodeKey);

    /**
     * 批量插入节点
     *
     * @param nodes 节点列表
     * @return 插入行数
     */
    int insertBatch(@Param("nodes") List<FlowNodeDefinitionEntity> nodes);
}
