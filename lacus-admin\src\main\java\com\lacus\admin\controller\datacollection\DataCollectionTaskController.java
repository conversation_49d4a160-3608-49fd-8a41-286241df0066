package com.lacus.admin.controller.datacollection;

import com.lacus.common.annotation.Log;
import com.lacus.common.core.controller.BaseController;
import com.lacus.common.core.domain.AjaxResult;
import com.lacus.common.core.page.TableDataInfo;
import com.lacus.common.enums.BusinessType;
import com.lacus.common.utils.poi.ExcelUtil;
import com.lacus.datacollection.domain.DataCollectionTask;
import com.lacus.datacollection.service.IDataCollectionTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 数据采集任务Controller
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/datacollection/task")
public class DataCollectionTaskController extends BaseController
{
    @Autowired
    private IDataCollectionTaskService dataCollectionTaskService;

    /**
     * 查询数据采集任务列表
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataCollectionTask dataCollectionTask)
    {
        startPage();
        List<DataCollectionTask> list = dataCollectionTaskService.selectDataCollectionTaskList(dataCollectionTask);
        return getDataTable(list);
    }

    /**
     * 导出数据采集任务列表
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:export')")
    @Log(title = "数据采集任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataCollectionTask dataCollectionTask)
    {
        List<DataCollectionTask> list = dataCollectionTaskService.selectDataCollectionTaskList(dataCollectionTask);
        ExcelUtil<DataCollectionTask> util = new ExcelUtil<DataCollectionTask>(DataCollectionTask.class);
        util.exportExcel(response, list, "数据采集任务数据");
    }

    /**
     * 获取数据采集任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@PathVariable("taskId") Long taskId)
    {
        return success(dataCollectionTaskService.selectDataCollectionTaskByTaskId(taskId));
    }

    /**
     * 新增数据采集任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:add')")
    @Log(title = "数据采集任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody DataCollectionTask dataCollectionTask)
    {
        if (!dataCollectionTaskService.checkTaskCodeUnique(dataCollectionTask))
        {
            return error("新增任务'" + dataCollectionTask.getTaskName() + "'失败，任务编码已存在");
        }
        dataCollectionTask.setCreateBy(getUsername());
        return toAjax(dataCollectionTaskService.insertDataCollectionTask(dataCollectionTask));
    }

    /**
     * 修改数据采集任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:edit')")
    @Log(title = "数据采集任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody DataCollectionTask dataCollectionTask)
    {
        if (!dataCollectionTaskService.checkTaskCodeUnique(dataCollectionTask))
        {
            return error("修改任务'" + dataCollectionTask.getTaskName() + "'失败，任务编码已存在");
        }
        dataCollectionTask.setUpdateBy(getUsername());
        return toAjax(dataCollectionTaskService.updateDataCollectionTask(dataCollectionTask));
    }

    /**
     * 删除数据采集任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:remove')")
    @Log(title = "数据采集任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    public AjaxResult remove(@PathVariable Long[] taskIds)
    {
        return toAjax(dataCollectionTaskService.deleteDataCollectionTaskByTaskIds(taskIds));
    }

    /**
     * 批量删除任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:remove')")
    @Log(title = "批量删除任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch")
    public AjaxResult batchRemove(@RequestBody Long[] taskIds)
    {
        return toAjax(dataCollectionTaskService.batchDeleteTask(taskIds));
    }

    /**
     * 改变任务状态
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:edit')")
    @Log(title = "改变任务状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody Map<String, Object> params)
    {
        Long taskId = Long.valueOf(params.get("taskId").toString());
        Integer status = Integer.valueOf(params.get("status").toString());
        return toAjax(dataCollectionTaskService.changeTaskStatus(taskId, status, getUsername()));
    }

    /**
     * 执行任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:execute')")
    @Log(title = "执行任务", businessType = BusinessType.OTHER)
    @PostMapping("/execute/{taskId}")
    public AjaxResult execute(@PathVariable Long taskId)
    {
        return success(dataCollectionTaskService.executeTask(taskId, getUsername()));
    }

    /**
     * 停止任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:execute')")
    @Log(title = "停止任务", businessType = BusinessType.OTHER)
    @PostMapping("/stop/{taskId}")
    public AjaxResult stop(@PathVariable Long taskId)
    {
        return toAjax(dataCollectionTaskService.stopTask(taskId, getUsername()));
    }

    /**
     * 复制任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:add')")
    @Log(title = "复制任务", businessType = BusinessType.INSERT)
    @PostMapping("/copy")
    public AjaxResult copy(@RequestBody Map<String, Object> params)
    {
        Long taskId = Long.valueOf(params.get("taskId").toString());
        String newTaskName = params.get("newTaskName").toString();
        return success(dataCollectionTaskService.copyTask(taskId, newTaskName, getUsername()));
    }

    /**
     * 移动任务到其他分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:edit')")
    @Log(title = "移动任务", businessType = BusinessType.UPDATE)
    @PutMapping("/move")
    public AjaxResult move(@RequestBody Map<String, Object> params)
    {
        Long taskId = Long.valueOf(params.get("taskId").toString());
        Long targetCategoryId = Long.valueOf(params.get("targetCategoryId").toString());
        String moveReason = params.get("moveReason") != null ? params.get("moveReason").toString() : "";
        return toAjax(dataCollectionTaskService.moveTask(taskId, targetCategoryId, moveReason, getUsername()));
    }

    /**
     * 获取任务执行历史
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping("/history")
    public TableDataInfo getHistory(@RequestParam(required = false) Long taskId,
                                   @RequestParam(required = false) String status,
                                   @RequestParam(required = false) String startTime,
                                   @RequestParam(required = false) String endTime)
    {
        startPage();
        List<Map<String, Object>> list = dataCollectionTaskService.getTaskHistory(taskId, status, startTime, endTime);
        return getDataTable(list);
    }

    /**
     * 获取任务执行详情
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping("/execution/{executionId}")
    public AjaxResult getExecutionDetail(@PathVariable String executionId)
    {
        return success(dataCollectionTaskService.getExecutionDetail(executionId));
    }

    /**
     * 验证任务配置
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @PostMapping("/validate/{taskId}")
    public AjaxResult validate(@PathVariable Long taskId)
    {
        return success(dataCollectionTaskService.validateTask(taskId));
    }

    /**
     * 预览任务执行结果
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @PostMapping("/preview/{taskId}")
    public AjaxResult preview(@PathVariable Long taskId)
    {
        return success(dataCollectionTaskService.previewTask(taskId));
    }

    /**
     * 导出任务配置
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:export')")
    @GetMapping("/export/{taskId}")
    public void exportTask(HttpServletResponse response, @PathVariable Long taskId)
    {
        dataCollectionTaskService.exportTask(response, taskId);
    }

    /**
     * 导入任务配置
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:import')")
    @Log(title = "导入任务", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importTask(@RequestBody Map<String, Object> data)
    {
        return success(dataCollectionTaskService.importTask(data, getUsername()));
    }

    /**
     * 获取任务统计信息
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping("/stats")
    public AjaxResult getStats(@RequestParam(required = false) Long categoryId)
    {
        return success(dataCollectionTaskService.getTaskStats(categoryId));
    }

    /**
     * 检查任务编码是否唯一
     */
    @GetMapping("/checkCode")
    public AjaxResult checkTaskCode(@RequestParam String taskCode, @RequestParam(required = false) Long taskId)
    {
        DataCollectionTask task = new DataCollectionTask();
        task.setTaskCode(taskCode);
        task.setTaskId(taskId);
        boolean unique = dataCollectionTaskService.checkTaskCodeUnique(task);
        return success(unique);
    }

    /**
     * 获取任务依赖关系
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping("/dependencies/{taskId}")
    public AjaxResult getDependencies(@PathVariable Long taskId)
    {
        return success(dataCollectionTaskService.getTaskDependencies(taskId));
    }

    /**
     * 保存流程定义
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:edit')")
    @Log(title = "保存流程定义", businessType = BusinessType.UPDATE)
    @PostMapping("/flow")
    public AjaxResult saveFlow(@RequestBody Map<String, Object> data)
    {
        return success(dataCollectionTaskService.saveFlowDefinition(data, getUsername()));
    }

    /**
     * 获取流程定义
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping("/flow/{taskId}")
    public AjaxResult getFlow(@PathVariable Long taskId)
    {
        return success(dataCollectionTaskService.getFlowDefinition(taskId));
    }

    /**
     * 验证流程定义
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @PostMapping("/flow/validate")
    public AjaxResult validateFlow(@RequestBody Map<String, Object> data)
    {
        return success(dataCollectionTaskService.validateFlowDefinition(data));
    }

    /**
     * 执行流程
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:execute')")
    @Log(title = "执行流程", businessType = BusinessType.OTHER)
    @PostMapping("/flow/execute")
    public AjaxResult executeFlow(@RequestBody Map<String, Object> data)
    {
        return success(dataCollectionTaskService.executeFlow(data, getUsername()));
    }
}
