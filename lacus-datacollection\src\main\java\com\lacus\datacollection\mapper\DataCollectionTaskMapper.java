package com.lacus.datacollection.mapper;

import com.lacus.datacollection.domain.DataCollectionTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 数据采集任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface DataCollectionTaskMapper 
{
    /**
     * 查询数据采集任务
     * 
     * @param taskId 数据采集任务主键
     * @return 数据采集任务
     */
    public DataCollectionTask selectDataCollectionTaskByTaskId(Long taskId);

    /**
     * 查询数据采集任务列表
     * 
     * @param dataCollectionTask 数据采集任务
     * @return 数据采集任务集合
     */
    public List<DataCollectionTask> selectDataCollectionTaskList(DataCollectionTask dataCollectionTask);

    /**
     * 新增数据采集任务
     * 
     * @param dataCollectionTask 数据采集任务
     * @return 结果
     */
    public int insertDataCollectionTask(DataCollectionTask dataCollectionTask);

    /**
     * 修改数据采集任务
     * 
     * @param dataCollectionTask 数据采集任务
     * @return 结果
     */
    public int updateDataCollectionTask(DataCollectionTask dataCollectionTask);

    /**
     * 删除数据采集任务
     * 
     * @param taskId 数据采集任务主键
     * @return 结果
     */
    public int deleteDataCollectionTaskByTaskId(Long taskId);

    /**
     * 批量删除数据采集任务
     * 
     * @param taskIds 需要删除的数据主键
     * @return 结果
     */
    public int deleteDataCollectionTaskByTaskIds(Long[] taskIds);

    /**
     * 检查任务编码是否唯一
     * 
     * @param taskCode 任务编码
     * @return 任务信息
     */
    public DataCollectionTask checkTaskCodeUnique(String taskCode);

    /**
     * 检查任务名称是否唯一
     * 
     * @param taskName 任务名称
     * @param categoryId 分类ID
     * @return 任务信息
     */
    public DataCollectionTask checkTaskNameUnique(@Param("taskName") String taskName, @Param("categoryId") Long categoryId);

    /**
     * 根据分类ID查询任务数量
     * 
     * @param categoryId 分类ID
     * @return 任务数量
     */
    public int selectTaskCountByCategory(Long categoryId);

    /**
     * 根据分类ID查询任务状态统计
     * 
     * @param categoryId 分类ID
     * @return 状态统计
     */
    public Map<String, Integer> selectTaskStatusStatsByCategory(Long categoryId);

    /**
     * 更新任务状态
     * 
     * @param taskId 任务ID
     * @param status 状态
     * @return 结果
     */
    public int updateTaskStatus(@Param("taskId") Long taskId, @Param("status") Integer status);

    /**
     * 根据任务编码查询任务
     * 
     * @param taskCode 任务编码
     * @return 任务信息
     */
    public DataCollectionTask selectTaskByCode(String taskCode);

    /**
     * 根据任务名称查询任务
     * 
     * @param taskName 任务名称
     * @return 任务信息
     */
    public DataCollectionTask selectTaskByName(String taskName);

    /**
     * 查询启用状态的任务列表
     * 
     * @return 任务列表
     */
    public List<DataCollectionTask> selectEnabledTasks();

    /**
     * 根据调度类型查询任务列表
     * 
     * @param scheduleType 调度类型
     * @return 任务列表
     */
    public List<DataCollectionTask> selectTasksByScheduleType(String scheduleType);

    /**
     * 根据采集类型查询任务列表
     * 
     * @param collectionType 采集类型
     * @return 任务列表
     */
    public List<DataCollectionTask> selectTasksByCollectionType(String collectionType);

    /**
     * 根据数据源类型查询任务列表
     * 
     * @param sourceType 数据源类型
     * @return 任务列表
     */
    public List<DataCollectionTask> selectTasksBySourceType(String sourceType);

    /**
     * 批量更新任务状态
     * 
     * @param taskIds 任务ID数组
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateTaskStatus(@Param("taskIds") Long[] taskIds, @Param("status") Integer status);

    /**
     * 批量移动任务到指定分类
     * 
     * @param taskIds 任务ID数组
     * @param categoryId 目标分类ID
     * @return 结果
     */
    public int batchMoveTasksToCategory(@Param("taskIds") Long[] taskIds, @Param("categoryId") Long categoryId);

    /**
     * 获取任务统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> selectTaskStats();

    /**
     * 根据创建时间范围查询任务
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 任务列表
     */
    public List<DataCollectionTask> selectTasksByCreateTime(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 获取任务创建统计（按日期分组）
     * 
     * @param days 统计天数
     * @return 创建统计
     */
    public List<Object> selectTaskCreateStats(@Param("days") Integer days);

    /**
     * 查询最近执行的任务
     * 
     * @param limit 限制数量
     * @return 任务列表
     */
    public List<DataCollectionTask> selectRecentExecutedTasks(@Param("limit") Integer limit);

    /**
     * 查询执行失败的任务
     * 
     * @return 任务列表
     */
    public List<DataCollectionTask> selectFailedTasks();

    /**
     * 查询长时间未执行的任务
     * 
     * @param hours 小时数
     * @return 任务列表
     */
    public List<DataCollectionTask> selectLongTimeNoExecuteTasks(@Param("hours") Integer hours);

    /**
     * 获取任务执行统计
     * 
     * @param taskId 任务ID
     * @return 执行统计
     */
    public Map<String, Object> selectTaskExecutionStats(Long taskId);

    /**
     * 查询热门任务（按执行次数排序）
     * 
     * @param limit 限制数量
     * @return 热门任务列表
     */
    public List<DataCollectionTask> selectHotTasks(@Param("limit") Integer limit);

    /**
     * 根据目标数据库查询任务
     * 
     * @param targetDatabase 目标数据库
     * @return 任务列表
     */
    public List<DataCollectionTask> selectTasksByTargetDatabase(String targetDatabase);

    /**
     * 根据目标表查询任务
     * 
     * @param targetTable 目标表
     * @return 任务列表
     */
    public List<DataCollectionTask> selectTasksByTargetTable(String targetTable);

    /**
     * 获取任务依赖关系
     * 
     * @param taskId 任务ID
     * @return 依赖关系
     */
    public List<Map<String, Object>> selectTaskDependencies(Long taskId);

    /**
     * 查询任务的前置依赖
     * 
     * @param taskId 任务ID
     * @return 前置依赖任务列表
     */
    public List<DataCollectionTask> selectTaskPreDependencies(Long taskId);

    /**
     * 查询任务的后置依赖
     * 
     * @param taskId 任务ID
     * @return 后置依赖任务列表
     */
    public List<DataCollectionTask> selectTaskPostDependencies(Long taskId);

    /**
     * 更新任务版本号
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    public int updateTaskVersion(Long taskId);

    /**
     * 获取任务的历史版本
     * 
     * @param taskId 任务ID
     * @return 历史版本列表
     */
    public List<Map<String, Object>> selectTaskVersionHistory(Long taskId);
}
