<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采集类型" prop="collectionType">
        <el-select v-model="queryParams.collectionType" placeholder="请选择采集类型" clearable>
          <el-option label="API接口" value="API" />
          <el-option label="SQL查询" value="SQL" />
        </el-select>
      </el-form-item>
      <el-form-item label="数据源类型" prop="sourceType">
        <el-select v-model="queryParams.sourceType" placeholder="请选择数据源类型" clearable>
          <el-option label="MySQL" value="MYSQL" />
          <el-option label="SQL Server" value="SQLSERVER" />
          <el-option label="API接口" value="API" />
          <el-option label="Oracle" value="ORACLE" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['datacollection:task:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['datacollection:task:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['datacollection:task:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['datacollection:task:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务ID" align="center" prop="taskId" width="80" />
      <el-table-column label="任务名称" align="center" prop="taskName" :show-overflow-tooltip="true" />
      <el-table-column label="采集类型" align="center" prop="collectionType" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.collectionType === 'API' ? 'primary' : 'success'">
            {{ scope.row.collectionTypeDesc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="数据源类型" align="center" prop="sourceType" width="120">
        <template #default="scope">
          <el-tag type="info">{{ scope.row.sourceTypeDesc }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="目标表" align="center" prop="targetTable" :show-overflow-tooltip="true" />
      <el-table-column label="写入模式" align="center" prop="writeMode" width="100">
        <template #default="scope">
          <el-tag :type="getWriteModeTagType(scope.row.writeMode)">
            {{ scope.row.writeModeDesc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="调度类型" align="center" prop="scheduleType" width="100">
        <template #default="scope">
          <el-tag :type="getScheduleTagType(scope.row.scheduleType)">
            {{ scope.row.scheduleTypeDesc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="最后执行" align="center" width="160">
        <template #default="scope">
          <div v-if="scope.row.lastExecuteTime">
            <div>{{ parseTime(scope.row.lastExecuteTime, '{y}-{m}-{d} {h}:{i}') }}</div>
            <el-tag 
              :type="getExecuteStatusTagType(scope.row.lastExecuteStatus)" 
              size="small"
            >
              {{ getExecuteStatusText(scope.row.lastExecuteStatus) }}
            </el-tag>
          </div>
          <span v-else class="text-muted">未执行</span>
        </template>
      </el-table-column>
      <el-table-column label="执行统计" align="center" width="120">
        <template #default="scope">
          <div class="execution-stats">
            <div>总计: {{ scope.row.totalExecuteCount || 0 }}</div>
            <div>
              <span class="success-count">成功: {{ scope.row.successExecuteCount || 0 }}</span>
              <span class="failed-count">失败: {{ scope.row.failedExecuteCount || 0 }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['datacollection:task:edit']"
          >修改</el-button>
          <el-button
            type="text"
            icon="Setting"
            @click="handleDesign(scope.row)"
            v-hasPermi="['datacollection:task:edit']"
          >设计</el-button>
          <el-button
            type="text"
            icon="VideoPlay"
            @click="handleExecute(scope.row)"
            v-hasPermi="['datacollection:task:execute']"
            :disabled="scope.row.status !== 1"
          >执行</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button type="text" icon="More">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="history" icon="Clock">执行历史</el-dropdown-item>
                <el-dropdown-item command="log" icon="Document">执行日志</el-dropdown-item>
                <el-dropdown-item command="preview" icon="View">预览执行</el-dropdown-item>
                <el-dropdown-item command="copy" icon="CopyDocument">复制任务</el-dropdown-item>
                <el-dropdown-item command="delete" icon="Delete" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改任务对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="form.taskName" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采集类型" prop="collectionType">
              <el-select v-model="form.collectionType" placeholder="请选择采集类型">
                <el-option label="API接口" value="API" />
                <el-option label="SQL查询" value="SQL" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="数据源类型" prop="sourceType">
              <el-select v-model="form.sourceType" placeholder="请选择数据源类型">
                <el-option label="MySQL" value="MYSQL" />
                <el-option label="SQL Server" value="SQLSERVER" />
                <el-option label="API接口" value="API" />
                <el-option label="Oracle" value="ORACLE" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="写入模式" prop="writeMode">
              <el-select v-model="form.writeMode" placeholder="请选择写入模式">
                <el-option label="覆盖写入" value="OVERWRITE" />
                <el-option label="追加写入" value="APPEND" />
                <el-option label="更新插入" value="UPSERT" />
                <el-option label="自动建表" value="AUTO_CREATE" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="目标数据库" prop="targetDatabase">
              <el-input v-model="form.targetDatabase" placeholder="请输入目标数据库" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标表名" prop="targetTable">
              <el-input v-model="form.targetTable" placeholder="请输入目标表名" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="调度类型" prop="scheduleType">
              <el-select v-model="form.scheduleType" placeholder="请选择调度类型">
                <el-option label="手动执行" value="MANUAL" />
                <el-option label="定时调度" value="CRON" />
                <el-option label="实时同步" value="REALTIME" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.scheduleType === 'CRON'">
            <el-form-item label="Cron表达式" prop="cronExpression">
              <el-input v-model="form.cronExpression" placeholder="请输入Cron表达式" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="任务描述" prop="taskDesc">
          <el-input v-model="form.taskDesc" type="textarea" placeholder="请输入任务描述" :rows="3" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
