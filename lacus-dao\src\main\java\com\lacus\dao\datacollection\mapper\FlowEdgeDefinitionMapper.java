package com.lacus.dao.datacollection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lacus.dao.datacollection.entity.FlowEdgeDefinitionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程连线定义Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FlowEdgeDefinitionMapper extends BaseMapper<FlowEdgeDefinitionEntity> {

    /**
     * 根据任务ID查询连线列表
     *
     * @param taskId 任务ID
     * @return 连线列表
     */
    List<FlowEdgeDefinitionEntity> selectByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据任务ID删除连线
     *
     * @param taskId 任务ID
     * @return 删除行数
     */
    int deleteByTaskId(@Param("taskId") Long taskId);

    /**
     * 批量插入连线
     *
     * @param edges 连线列表
     * @return 插入行数
     */
    int insertBatch(@Param("edges") List<FlowEdgeDefinitionEntity> edges);
}
