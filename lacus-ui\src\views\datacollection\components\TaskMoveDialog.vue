<template>
  <el-dialog
    :model-value="modelValue"
    title="移动任务"
    width="500px"
    @update:model-value="handleClose"
    @close="handleClose"
  >
    <div class="move-dialog-content">
      <div class="task-info">
        <h4>任务信息</h4>
        <p><strong>任务名称：</strong>{{ taskData.taskName }}</p>
        <p><strong>任务编码：</strong>{{ taskData.taskCode }}</p>
        <p><strong>当前分类：</strong>{{ currentCategoryName }}</p>
      </div>
      
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="目标分类" prop="targetCategoryId">
          <el-select
            v-model="formData.targetCategoryId"
            placeholder="请选择目标分类"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="option in categoryOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="option.value === taskData.categoryId"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="移动原因" prop="moveReason">
          <el-input
            v-model="formData.moveReason"
            type="textarea"
            :rows="3"
            placeholder="请输入移动原因（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定移动
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { moveDataCollectionTask } from '@/api/datacollection/task'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  taskData: {
    type: Object,
    default: () => ({})
  },
  categoryOptions: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const loading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
  targetCategoryId: null,
  moveReason: ''
})

// 表单验证规则
const formRules = {
  targetCategoryId: [
    { required: true, message: '请选择目标分类', trigger: 'change' }
  ]
}

// 计算属性
const currentCategoryName = computed(() => {
  if (!props.taskData.categoryId || !props.categoryOptions.length) {
    return '未知分类'
  }
  
  const category = props.categoryOptions.find(item => item.value === props.taskData.categoryId)
  return category ? category.label : '未知分类'
})

// 监听对话框打开
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    // 重置表单
    formData.targetCategoryId = null
    formData.moveReason = ''
  }
})

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    loading.value = true
    
    // 提交数据
    await moveDataCollectionTask({
      taskId: props.taskData.taskId,
      targetCategoryId: formData.targetCategoryId,
      moveReason: formData.moveReason
    })
    
    ElMessage.success('移动成功')
    
    // 触发成功事件
    emit('success')
    
  } catch (error) {
    console.error('移动失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.move-dialog-content {
  padding: 0;
}

.task-info {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.task-info h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.task-info p {
  margin: 8px 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
}

.dialog-footer {
  text-align: right;
}
</style>
