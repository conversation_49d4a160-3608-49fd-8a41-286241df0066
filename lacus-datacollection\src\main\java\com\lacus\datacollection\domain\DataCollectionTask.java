package com.lacus.datacollection.domain;

import com.lacus.common.annotation.Excel;
import com.lacus.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 数据采集任务对象 data_collection_task
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class DataCollectionTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    private Long taskId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    @NotBlank(message = "任务名称不能为空")
    @Size(min = 2, max = 200, message = "任务名称长度必须在2-200个字符之间")
    private String taskName;

    /** 任务编码 */
    @Excel(name = "任务编码")
    @NotBlank(message = "任务编码不能为空")
    @Size(min = 2, max = 100, message = "任务编码长度必须在2-100个字符之间")
    private String taskCode;

    /** 分类ID */
    @Excel(name = "分类ID")
    @NotNull(message = "分类ID不能为空")
    private Long categoryId;

    /** 任务描述 */
    @Excel(name = "任务描述")
    private String taskDesc;

    /** 采集类型(API:接口采集 SQL:数据库采集 FILE:文件采集) */
    @Excel(name = "采集类型", readConverterExp = "API=接口采集,SQL=数据库采集,FILE=文件采集")
    @NotBlank(message = "采集类型不能为空")
    private String collectionType;

    /** 数据源类型(MYSQL,SQLSERVER,ORACLE,API,FTP等) */
    @Excel(name = "数据源类型")
    @NotBlank(message = "数据源类型不能为空")
    private String sourceType;

    /** 目标数据库 */
    @Excel(name = "目标数据库")
    @NotBlank(message = "目标数据库不能为空")
    private String targetDatabase;

    /** 目标表名 */
    @Excel(name = "目标表名")
    @NotBlank(message = "目标表名不能为空")
    private String targetTable;

    /** 写入模式(OVERWRITE:覆盖 APPEND:追加 UPSERT:更新插入 AUTO_CREATE:自动建表) */
    @Excel(name = "写入模式", readConverterExp = "OVERWRITE=覆盖,APPEND=追加,UPSERT=更新插入,AUTO_CREATE=自动建表")
    @NotBlank(message = "写入模式不能为空")
    private String writeMode;

    /** 调度类型(MANUAL:手动 CRON:定时 REALTIME:实时) */
    @Excel(name = "调度类型", readConverterExp = "MANUAL=手动,CRON=定时,REALTIME=实时")
    @NotBlank(message = "调度类型不能为空")
    private String scheduleType;

    /** Cron表达式 */
    @Excel(name = "Cron表达式")
    private String cronExpression;

    /** 流程定义JSON */
    private String flowDefinition;

    /** 任务配置JSON */
    private String taskConfig;

    /** 优先级(1-10) */
    @Excel(name = "优先级")
    private Integer priority;

    /** 超时时间(秒) */
    @Excel(name = "超时时间")
    private Integer timeout;

    /** 重试次数 */
    @Excel(name = "重试次数")
    private Integer retryCount;

    /** 状态(0:禁用 1:启用) */
    @Excel(name = "状态", readConverterExp = "0=禁用,1=启用")
    private Integer status;

    /** 版本号 */
    @Excel(name = "版本号")
    private Integer version;

    /** 分类名称 */
    private String categoryName;

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }
    public void setTaskName(String taskName) 
    {
        this.taskName = taskName;
    }

    public String getTaskName() 
    {
        return taskName;
    }
    public void setTaskCode(String taskCode) 
    {
        this.taskCode = taskCode;
    }

    public String getTaskCode() 
    {
        return taskCode;
    }
    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }
    public void setTaskDesc(String taskDesc) 
    {
        this.taskDesc = taskDesc;
    }

    public String getTaskDesc() 
    {
        return taskDesc;
    }
    public void setCollectionType(String collectionType) 
    {
        this.collectionType = collectionType;
    }

    public String getCollectionType() 
    {
        return collectionType;
    }
    public void setSourceType(String sourceType) 
    {
        this.sourceType = sourceType;
    }

    public String getSourceType() 
    {
        return sourceType;
    }
    public void setTargetDatabase(String targetDatabase) 
    {
        this.targetDatabase = targetDatabase;
    }

    public String getTargetDatabase() 
    {
        return targetDatabase;
    }
    public void setTargetTable(String targetTable) 
    {
        this.targetTable = targetTable;
    }

    public String getTargetTable() 
    {
        return targetTable;
    }
    public void setWriteMode(String writeMode) 
    {
        this.writeMode = writeMode;
    }

    public String getWriteMode() 
    {
        return writeMode;
    }
    public void setScheduleType(String scheduleType) 
    {
        this.scheduleType = scheduleType;
    }

    public String getScheduleType() 
    {
        return scheduleType;
    }
    public void setCronExpression(String cronExpression) 
    {
        this.cronExpression = cronExpression;
    }

    public String getCronExpression() 
    {
        return cronExpression;
    }
    public void setFlowDefinition(String flowDefinition) 
    {
        this.flowDefinition = flowDefinition;
    }

    public String getFlowDefinition() 
    {
        return flowDefinition;
    }
    public void setTaskConfig(String taskConfig) 
    {
        this.taskConfig = taskConfig;
    }

    public String getTaskConfig() 
    {
        return taskConfig;
    }
    public void setPriority(Integer priority) 
    {
        this.priority = priority;
    }

    public Integer getPriority() 
    {
        return priority;
    }
    public void setTimeout(Integer timeout) 
    {
        this.timeout = timeout;
    }

    public Integer getTimeout() 
    {
        return timeout;
    }
    public void setRetryCount(Integer retryCount) 
    {
        this.retryCount = retryCount;
    }

    public Integer getRetryCount() 
    {
        return retryCount;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setVersion(Integer version) 
    {
        this.version = version;
    }

    public Integer getVersion() 
    {
        return version;
    }

    public String getCategoryName()
    {
        return categoryName;
    }

    public void setCategoryName(String categoryName)
    {
        this.categoryName = categoryName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskId", getTaskId())
            .append("taskName", getTaskName())
            .append("taskCode", getTaskCode())
            .append("categoryId", getCategoryId())
            .append("taskDesc", getTaskDesc())
            .append("collectionType", getCollectionType())
            .append("sourceType", getSourceType())
            .append("targetDatabase", getTargetDatabase())
            .append("targetTable", getTargetTable())
            .append("writeMode", getWriteMode())
            .append("scheduleType", getScheduleType())
            .append("cronExpression", getCronExpression())
            .append("flowDefinition", getFlowDefinition())
            .append("taskConfig", getTaskConfig())
            .append("priority", getPriority())
            .append("timeout", getTimeout())
            .append("retryCount", getRetryCount())
            .append("status", getStatus())
            .append("version", getVersion())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
