//package com.lacus.admin.controller.metadata;
//
//import com.lacus.common.core.base.BaseController;
//import lombok.RequiredArgsConstructor;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 数据仓库数据库管理控制器
// */
//@RestController
//@RequestMapping("/metadata/database")
//@RequiredArgsConstructor
//public class NewDatabaseController extends BaseController {
//
//    /**
//     * 获取数据库列表
//     */
//    @GetMapping("/list")
//    @PreAuthorize("@permission.has('metadata:database:list')")
//    public R<Object> getDatabaseList(@RequestParam(required = false) Long datasourceId) {
//        // 这里需要实现获取数据库列表的逻辑
//        List<Map<String, Object>> databases = new ArrayList<Map<String, Object>>();
//
//        Map<String, Object> database1 = new HashMap<String, Object>();
//        database1.put("id", 1L);
//        database1.put("name", "data_warehouse");
//        database1.put("comment", "数据仓库");
//        databases.add(database1);
//
//        Map<String, Object> database2 = new HashMap<String, Object>();
//        database2.put("id", 2L);
//        database2.put("name", "business_db");
//        database2.put("comment", "业务数据库");
//        databases.add(database2);
//
//        return R.ok(databases);
//    }
//}
