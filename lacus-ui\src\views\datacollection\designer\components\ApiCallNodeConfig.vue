<template>
  <div class="api-call-node-config">
    <el-form :model="config" label-width="100px" size="small">
      <!-- 基本配置 -->
      <el-form-item label="API地址" required>
        <el-input 
          v-model="config.url" 
          placeholder="请输入API地址，如：http://api.example.com/users"
        />
        <div class="form-tip">支持变量引用，如：http://api.example.com/users?page=${pageNum}</div>
      </el-form-item>

      <el-form-item label="请求方法" required>
        <el-select v-model="config.method" placeholder="请选择请求方法">
          <el-option label="GET" value="GET" />
          <el-option label="POST" value="POST" />
          <el-option label="PUT" value="PUT" />
          <el-option label="DELETE" value="DELETE" />
        </el-select>
      </el-form-item>

      <el-form-item label="超时时间">
        <el-input-number 
          v-model="config.timeout" 
          :min="1000"
          :max="300000"
          :step="1000"
          placeholder="毫秒"
        />
        <span class="form-tip">请求超时时间（毫秒）</span>
      </el-form-item>

      <!-- 请求头配置 -->
      <el-form-item label="请求头">
        <div class="headers-config">
          <div 
            v-for="(header, index) in config.headers" 
            :key="index"
            class="header-item"
          >
            <el-input 
              v-model="header.key" 
              placeholder="Header名称"
              style="width: 150px; margin-right: 8px;"
            />
            <el-input 
              v-model="header.value" 
              placeholder="Header值"
              style="width: 200px; margin-right: 8px;"
            />
            <el-button 
              type="danger" 
              size="small" 
              icon="Delete"
              @click="removeHeader(index)"
            />
          </div>
          <el-button 
            type="primary" 
            size="small" 
            icon="Plus"
            @click="addHeader"
          >
            添加请求头
          </el-button>
        </div>
      </el-form-item>

      <!-- 请求参数配置 -->
      <el-form-item label="请求参数">
        <div class="params-config">
          <div 
            v-for="(param, index) in config.params" 
            :key="index"
            class="param-item"
          >
            <el-input 
              v-model="param.key" 
              placeholder="参数名"
              style="width: 120px; margin-right: 8px;"
            />
            <el-select 
              v-model="param.type" 
              placeholder="类型"
              style="width: 100px; margin-right: 8px;"
            >
              <el-option label="字符串" value="string" />
              <el-option label="数字" value="number" />
              <el-option label="布尔" value="boolean" />
              <el-option label="变量" value="variable" />
            </el-select>
            <el-input 
              v-model="param.value" 
              placeholder="参数值"
              style="width: 150px; margin-right: 8px;"
            />
            <el-button 
              type="danger" 
              size="small" 
              icon="Delete"
              @click="removeParam(index)"
            />
          </div>
          <el-button 
            type="primary" 
            size="small" 
            icon="Plus"
            @click="addParam"
          >
            添加参数
          </el-button>
        </div>
      </el-form-item>

      <!-- 认证配置 -->
      <el-form-item label="认证方式">
        <el-select v-model="config.authType" placeholder="请选择认证方式">
          <el-option label="无认证" value="NONE" />
          <el-option label="Basic认证" value="BASIC" />
          <el-option label="Bearer Token" value="BEARER" />
          <el-option label="API Key" value="API_KEY" />
        </el-select>
      </el-form-item>

      <template v-if="config.authType === 'BASIC'">
        <el-form-item label="用户名">
          <el-input v-model="config.auth.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="config.auth.password" type="password" placeholder="请输入密码" />
        </el-form-item>
      </template>

      <template v-if="config.authType === 'BEARER'">
        <el-form-item label="Token">
          <el-input v-model="config.auth.token" placeholder="请输入Bearer Token" />
        </el-form-item>
      </template>

      <template v-if="config.authType === 'API_KEY'">
        <el-form-item label="API Key名称">
          <el-input v-model="config.auth.keyName" placeholder="如：X-API-Key" />
        </el-form-item>
        <el-form-item label="API Key值">
          <el-input v-model="config.auth.keyValue" placeholder="请输入API Key" />
        </el-form-item>
        <el-form-item label="传递方式">
          <el-select v-model="config.auth.keyLocation" placeholder="选择传递方式">
            <el-option label="请求头" value="HEADER" />
            <el-option label="查询参数" value="QUERY" />
          </el-select>
        </el-form-item>
      </template>

      <!-- 响应处理 -->
      <el-divider content-position="left">响应处理</el-divider>

      <el-form-item label="输出变量">
        <el-input 
          v-model="config.outputVariable" 
          placeholder="响应数据保存的变量名，如：apiResponse"
        />
      </el-form-item>

      <el-form-item label="数据路径">
        <el-input 
          v-model="config.dataPath" 
          placeholder="提取数据的JSON路径，如：data.list"
        />
        <div class="form-tip">用于从响应中提取特定数据，留空则保存完整响应</div>
      </el-form-item>

      <el-form-item label="分页处理">
        <el-switch 
          v-model="config.pagination.enabled" 
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>

      <template v-if="config.pagination.enabled">
        <el-form-item label="总数路径">
          <el-input 
            v-model="config.pagination.totalPath" 
            placeholder="总记录数的JSON路径，如：total"
          />
        </el-form-item>
        <el-form-item label="数据路径">
          <el-input 
            v-model="config.pagination.dataPath" 
            placeholder="数据列表的JSON路径，如：data"
          />
        </el-form-item>
      </template>

      <!-- 错误处理 -->
      <el-divider content-position="left">错误处理</el-divider>

      <el-form-item label="重试次数">
        <el-input-number 
          v-model="config.retryCount" 
          :min="0"
          :max="10"
          placeholder="失败重试次数"
        />
      </el-form-item>

      <el-form-item label="重试间隔">
        <el-input-number 
          v-model="config.retryDelay" 
          :min="1000"
          :max="60000"
          :step="1000"
          placeholder="毫秒"
        />
        <span class="form-tip">重试间隔时间（毫秒）</span>
      </el-form-item>

      <el-form-item label="失败处理">
        <el-select v-model="config.onError" placeholder="请选择失败处理方式">
          <el-option label="停止流程" value="STOP" />
          <el-option label="跳过继续" value="SKIP" />
          <el-option label="使用默认值" value="DEFAULT" />
        </el-select>
      </el-form-item>

      <el-form-item label="默认值" v-if="config.onError === 'DEFAULT'">
        <el-input 
          v-model="config.defaultValue" 
          type="textarea"
          :rows="3"
          placeholder="请输入失败时的默认返回值（JSON格式）"
        />
      </el-form-item>
    </el-form>

    <!-- 配置预览 -->
    <el-divider content-position="left">配置预览</el-divider>
    <div class="config-preview">
      <el-alert 
        :title="getConfigSummary()" 
        type="info" 
        :closable="false"
        show-icon
      />
    </div>

    <!-- 测试按钮 -->
    <div class="test-section" v-if="detailed">
      <el-button type="success" @click="testApi" :loading="testing">
        <el-icon><Connection /></el-icon>
        测试API调用
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Connection } from '@element-plus/icons-vue'

// 类型定义
interface HeaderItem {
  key: string
  value: string
}

interface ParamItem {
  key: string
  type: string
  value: string
}

interface AuthConfig {
  username: string
  password: string
  token: string
  keyName: string
  keyValue: string
  keyLocation: string
}

interface PaginationConfig {
  enabled: boolean
  totalPath: string
  dataPath: string
}

interface ApiConfig {
  url: string
  method: string
  timeout: number
  headers: HeaderItem[]
  params: ParamItem[]
  authType: string
  auth: AuthConfig
  outputVariable: string
  dataPath: string
  pagination: PaginationConfig
  retryCount: number
  retryDelay: number
  onError: string
  defaultValue: string
}

const props = defineProps<{
  modelValue: Partial<ApiConfig>
  node?: Record<string, any>
  detailed?: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: Partial<ApiConfig>]
}>()

// 配置数据
const config = ref<ApiConfig>({
  url: '',
  method: 'GET',
  timeout: 30000,
  headers: [],
  params: [],
  authType: 'NONE',
  auth: {
    username: '',
    password: '',
    token: '',
    keyName: '',
    keyValue: '',
    keyLocation: 'HEADER'
  },
  outputVariable: 'apiResponse',
  dataPath: '',
  pagination: {
    enabled: false,
    totalPath: 'total',
    dataPath: 'data'
  },
  retryCount: 3,
  retryDelay: 5000,
  onError: 'STOP',
  defaultValue: '',
  ...props.modelValue
})

const testing = ref<boolean>(false)

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', newConfig)
}, { deep: true })

// 添加请求头
const addHeader = (): void => {
  config.value.headers.push({ key: '', value: '' })
}

// 删除请求头
const removeHeader = (index: number): void => {
  config.value.headers.splice(index, 1)
}

// 添加参数
const addParam = (): void => {
  config.value.params.push({ key: '', type: 'string', value: '' })
}

// 删除参数
const removeParam = (index: number): void => {
  config.value.params.splice(index, 1)
}

// 配置摘要
const getConfigSummary = computed((): string => {
  const method = config.value.method || 'GET'
  const url = config.value.url || '未设置'
  const auth = config.value.authType !== 'NONE' ? `，${config.value.authType}认证` : ''
  const pagination = config.value.pagination.enabled ? '，支持分页' : ''

  return `API调用：${method} ${url}${auth}${pagination}`
})

// 测试API
const testApi = async (): Promise<void> => {
  if (!config.value.url) {
    ElMessage.warning('请先设置API地址')
    return
  }

  testing.value = true
  try {
    // TODO: 调用实际的测试接口
    // const response = await testApiConnection(config.value)
    ElMessage.success('API连接测试成功')
  } catch (error) {
    console.error('API测试失败:', error)
    ElMessage.error('API连接测试失败')
  } finally {
    testing.value = false
  }
}
</script>

<style scoped>
.api-call-node-config {
  max-height: 600px;
  overflow-y: auto;
}

.headers-config,
.params-config {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}

.header-item,
.param-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.header-item:last-child,
.param-item:last-child {
  margin-bottom: 12px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.config-preview {
  margin-top: 16px;
}

.test-section {
  margin-top: 16px;
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.el-divider {
  margin: 20px 0 16px 0;
}
</style>
