package com.lacus.service.datawarehouse.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lacus.common.core.page.PageDTO;
import com.lacus.dao.datawarehouse.entity.*;
import com.lacus.dao.datawarehouse.mapper.*;
import com.lacus.service.datawarehouse.EtlFieldMappingService;
import com.lacus.service.datawarehouse.EtlQualityRulesService;
import com.lacus.service.datawarehouse.EtlSourceTableService;
import com.lacus.service.datawarehouse.EtlTaskService;
import com.lacus.service.datawarehouse.command.EtlTaskAddCommand;
import com.lacus.service.datawarehouse.dto.*;
import com.lacus.service.datawarehouse.engine.EtlExecutionEngine;
import com.lacus.service.datawarehouse.engine.EtlScheduleManager;
import com.lacus.service.datawarehouse.model.EtlTaskModel;
import com.lacus.service.datawarehouse.query.EtlExecutionHistoryQuery;
import com.lacus.service.datawarehouse.query.EtlTaskQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ETL任务服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EtlTaskServiceImpl extends ServiceImpl<EtlTaskMapper, EtlTaskEntity> implements EtlTaskService {

    @Autowired
    private EtlTaskMapper etlTaskMapper;

    @Autowired
    private EtlFieldMappingMapper etlFieldMappingMapper;

    @Autowired
    private EtlQualityRulesMapper etlQualityRulesMapper;

    @Autowired
    private EtlSourceTableMapper etlSourceTableMapper;

    @Autowired
    private EtlExecutionHistoryMapper executionHistoryMapper;

    @Autowired
    private EtlSourceTableService etlSourceTableService;

    @Autowired
    private EtlQualityRulesService etlQualityRulesService;

    @Autowired
    private EtlFieldMappingService etlFieldMappingService;

    @Autowired
    private EtlScheduleManager etlScheduleManager;

    @Autowired
    private EtlExecutionEngine etlExecutionEngine;

    @Override
    public PageDTO queryEtlTaskDtoList(EtlTaskQuery query) {
        Page<EtlTaskDTO> page = etlTaskMapper.selectPage(query.toPage(), query.toQueryWrapper());
        return new PageDTO(page.getRecords(), page.getTotal());
    }

    @Override
    public EtlTaskDTO queryEtlTaskDtoById(Long taskId) {
        EtlTaskEntity entity = etlTaskMapper.selectById(taskId);
        if (entity == null) {
            throw new RuntimeException("ETL任务不存在");
        }
        return convertToDTO(entity);
    }

    @Override
    public EtlTaskDTO queryEtlTaskById(Long taskId) {
        EtlTaskEntity entity = etlTaskMapper.selectById(taskId);
        if (entity == null) {
            throw new RuntimeException("ETL任务不存在");
        }
        EtlTaskDTO dto =convertToDTO(entity);
        //通过taskId查询出关联的数据源表、字段映射、质量规则等信息
        dto.setSourceTables(etlSourceTableMapper.selectList(new QueryWrapper<EtlSourceTableEntity>().eq("etl_task_id", taskId)));
        dto.setQualityRules(etlQualityRulesMapper.selectList(new QueryWrapper<EtlQualityRulesEntity>().eq("etl_task_id", taskId)));
        dto.setFieldMappings(etlFieldMappingMapper.selectList(new QueryWrapper<EtlFieldMappingEntity>().eq("etl_task_id", taskId)));
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveEtlTask(EtlTaskAddCommand command) {
        // 检查任务名称是否重复
        if (checkTaskNameExists(command.getTaskName(), null)) {
            throw new RuntimeException("任务名称已存在");
        }

        // 验证配置
        validateEtlTaskConfig(command);

        EtlTaskEntity entity = new EtlTaskEntity();
        BeanUtils.copyProperties(command, entity);
        entity.setSourceTableCount(command.getSourceTables().size());
        etlTaskMapper.insert(entity);

        addEtlTaskData(command,entity);
        //获取插入后的任务ID
        return entity.getTaskId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEtlTask(EtlTaskAddCommand model) {
        //修改ETL任务
        // 检查任务名称是否重复
        if (checkTaskNameExists(model.getTaskName(), model.getTaskId())) {
            throw new RuntimeException("任务名称已存在");
        }
        // 验证配置
        validateEtlTaskConfig(model);

        EtlTaskEntity entity = new EtlTaskEntity();
        BeanUtils.copyProperties(model, entity);
        entity.setSourceTableCount(model.getSourceTables().size());
        etlTaskMapper.updateById(entity);

        //删除旧数据
        etlSourceTableMapper.delete(new QueryWrapper<EtlSourceTableEntity>().eq("etl_task_id", model.getTaskId()));
        etlQualityRulesMapper.delete(new QueryWrapper<EtlQualityRulesEntity>().eq("etl_task_id", model.getTaskId()));
        etlFieldMappingMapper.delete(new QueryWrapper<EtlFieldMappingEntity>().eq("etl_task_id", model.getTaskId()));

        addEtlTaskData(model,entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteEtlTask(List<Long> taskIds) {
        etlTaskMapper.deleteBatchIds(taskIds);
        //删除对应配置
        for (Long taskId : taskIds){
            etlSourceTableMapper.delete(new QueryWrapper<EtlSourceTableEntity>().eq("etl_task_id",taskId));
            etlQualityRulesMapper.delete(new QueryWrapper<EtlQualityRulesEntity>().eq("etl_task_id",taskId));
            etlFieldMappingMapper.delete(new QueryWrapper<EtlFieldMappingEntity>().eq("etl_task_id",taskId));
        }
    }

    @Override
    public boolean checkTaskNameExists(String taskName, Long excludeTaskId) {
        EtlTaskEntity entity = etlTaskMapper.selectByTaskName(taskName, excludeTaskId);
        return entity != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEtlTaskStatus(Long taskId, Integer status) {
        EtlTaskEntity entity = new EtlTaskEntity();
        entity.setTaskId(taskId);
        entity.setStatus(status);
        entity.setUpdateTime(new Date());
        etlTaskMapper.updateById(entity);

        // 状态变更后，通知调度管理器
        handleTaskStatusChange(taskId, status);
    }

    /**
     * 处理任务状态变更
     */
    private void handleTaskStatusChange(Long taskId, Integer status) {
        try {
            EtlTaskEntity task = etlTaskMapper.selectById(taskId);
            if (task == null) {
                log.warn("任务不存在，无法处理状态变更: taskId={}", taskId);
                return;
            }

            if (status == 1) { // 启用任务
                log.info("任务已启用，启动调度: taskId={}, scheduleType={}", taskId, task.getScheduleType());
                etlScheduleManager.startSchedule(task);
            } else { // 禁用任务
                log.info("任务已禁用，停止调度: taskId={}", taskId);
                etlScheduleManager.stopSchedule(taskId);
            }
        } catch (Exception e) {
            log.error("处理任务状态变更失败: taskId={}, status={}", taskId, status, e);
            // 不抛出异常，避免影响主流程
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String runEtlTask(Long taskId) {
        log.info("手动执行ETL任务: taskId={}", taskId);

        // 使用调度管理器执行手动任务
        return etlScheduleManager.executeManualTask(taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopEtlTask(Long taskId) {
        log.info("停止ETL任务: taskId={}", taskId);

        // 使用调度管理器停止任务
        etlScheduleManager.stopRunningTask(taskId);
    }

//    @Override
//    public PageDTO getExecutionHistory(EtlExecutionHistoryQuery query) {
//        Map<String, Object> params = buildHistoryQueryParams(query);
//        Page<EtlExecutionHistoryEntity> page = new Page<>(query.getPageNum(), query.getPageSize());
//        IPage<EtlExecutionHistoryEntity> result = executionHistoryMapper.selectExecutionHistoryPage(page, params);
//
//        List<EtlExecutionHistoryDTO> dtoList = result.getRecords().stream()
//                .map(this::convertToHistoryDTO)
//                .collect(Collectors.toList());
//
//        return PageDTO.build(dtoList, result.getTotal());
//    }

    @Override
    public Object previewEtlResult(Object command) {
        // 这里应该实现ETL结果预览逻辑
        // 简化实现：返回模拟数据
        Map<String, Object> result = new HashMap<>();
        result.put("previewData", "模拟预览数据");
        result.put("recordCount", 100);
        result.put("previewTime", LocalDateTime.now());
        return result;
    }

    @Override
    public Object getFieldMappingSuggestions(String sourceLayer, String targetLayer, List<String> sourceTables) {
        // 这里应该实现字段映射建议逻辑
        // 简化实现：返回模拟建议
        Map<String, Object> suggestions = new HashMap<>();
        suggestions.put("sourceLayer", sourceLayer);
        suggestions.put("targetLayer", targetLayer);
        suggestions.put("sourceTables", sourceTables);
        suggestions.put("mappingSuggestions", "模拟映射建议");
        return suggestions;
    }

    @Override
    public EtlTaskStatsDTO getEtlTaskStats() {
        Map<String, Object> stats = etlTaskMapper.selectEtlTaskStats();
        EtlTaskStatsDTO dto = new EtlTaskStatsDTO();
        
        dto.setTotalTasks(getLongValue(stats, "totalTasks"));
        dto.setEnabledTasks(getLongValue(stats, "enabledTasks"));
        dto.setDisabledTasks(getLongValue(stats, "disabledTasks"));
        dto.setRunningTasks(getLongValue(stats, "runningTasks"));
        dto.setTodaySuccessTasks(getLongValue(stats, "todaySuccessTasks"));
        dto.setTodayFailedTasks(getLongValue(stats, "todayFailedTasks"));
        dto.setOdsLayerTasks(getLongValue(stats, "odsLayerTasks"));
        dto.setDwdLayerTasks(getLongValue(stats, "dwdLayerTasks"));
        dto.setDwsLayerTasks(getLongValue(stats, "dwsLayerTasks"));
        dto.setAdsLayerTasks(getLongValue(stats, "adsLayerTasks"));
        
        // 获取今日执行统计
        Map<String, Object> todayStats = executionHistoryMapper.selectTodayStats();
        dto.setTodayExecutions(getLongValue(todayStats, "todayExecutions"));
        
        return dto;
    }

    @Override
    public List<EtlExecutionHistoryEntity> getExecutionHistory(String taskId) {
        return executionHistoryMapper.selectList(new QueryWrapper<EtlExecutionHistoryEntity>()
                .eq("task_id", taskId)
                .orderByDesc("create_time"));
    }

    @Override
    public PageDTO getHistoryList(EtlExecutionHistoryQuery query) {
        Page<EtlExecutionHistoryDTO> page = executionHistoryMapper.selectPage(query.toPage(), query.toQueryWrapper());
        return new PageDTO(page.getRecords(), page.getTotal());
    }

    @Override
    public List<EtlTaskModel> queryTasksByStatus(Integer status) {
        List<EtlTaskEntity> entities = etlTaskMapper.selectByStatus(status);
        return entities.stream()
                .map(this::convertToModel)
                .collect(Collectors.toList());
    }

    @Override
    public List<EtlTaskModel> queryTasksByScheduleType(String scheduleType) {
        List<EtlTaskEntity> entities = etlTaskMapper.selectByScheduleType(scheduleType);
        return entities.stream()
                .map(this::convertToModel)
                .collect(Collectors.toList());
    }



    /**
     * 构建查询参数
     */
    private Map<String, Object> buildQueryParams(EtlTaskQuery query) {
        Map<String, Object> params = new HashMap<>();
        params.put("taskName", query.getTaskName());
        params.put("sourceLayer", query.getSourceLayer());
        params.put("targetLayer", query.getTargetLayer());
        params.put("status", query.getStatus());
        params.put("scheduleType", query.getScheduleType());
        params.put("createBy", query.getCreateBy());
        params.put("beginTime", query.getBeginTime());
        params.put("endTime", query.getEndTime());
        return params;
    }

    /**
     * 构建历史查询参数
     */
    private Map<String, Object> buildHistoryQueryParams(EtlExecutionHistoryQuery query) {
        Map<String, Object> params = new HashMap<>();
        params.put("taskId", query.getTaskId());
        params.put("status", query.getStatus());
        params.put("taskName", query.getTaskName());
        params.put("beginTime", query.getBeginTime());
        params.put("endTime", query.getEndTime());
        return params;
    }

    /**
     * 实体转DTO
     */
    private EtlTaskDTO convertToDTO(EtlTaskEntity entity) {
        EtlTaskDTO dto = new EtlTaskDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 实体转模型
     */
    private EtlTaskModel convertToModel(EtlTaskEntity entity) {
        EtlTaskModel model = new EtlTaskModel();
        BeanUtils.copyProperties(entity, model);
        return model;
    }

    /**
     * 模型转实体
     */
    private EtlTaskEntity convertToEntity(EtlTaskModel model) {
        EtlTaskEntity entity = new EtlTaskEntity();
        BeanUtils.copyProperties(model, entity);
        return entity;
    }

    /**
     * 执行历史实体转DTO
     */
    private EtlExecutionHistoryDTO convertToHistoryDTO(EtlExecutionHistoryEntity entity) {
        EtlExecutionHistoryDTO dto = new EtlExecutionHistoryDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 安全获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return 0L;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return 0L;
    }

    /**
     * 验证ETL任务配置
     */
    private void validateEtlTaskConfig(Object command) {
        // 这里可以添加具体的配置验证逻辑
        // 例如：验证Cron表达式、验证字段映射配置等
        log.debug("验证ETL任务配置");
    }

    /**
     * 添加ETL任务数据源配置、字段映射配置、数据质量配置
     */
    private void addEtlTaskData(EtlTaskAddCommand command,EtlTaskEntity entity) {
        // 这里可以添加具体的配置验证逻辑
        // 例如：验证Cron表达式、验证字段映射配置等
        // 插入ETL任务数据源表信息
        List<EtlSourceTableEntity> sourceTables = command.getSourceTables().stream()
                .map(table -> {
                    table.setEtlTaskId(entity.getTaskId());
                    table.setId(null);
                    EtlSourceTableEntity sourceTableEntity = new EtlSourceTableEntity();
                    BeanUtils.copyProperties(table,sourceTableEntity);
                    return sourceTableEntity;
                }).collect(Collectors.toList());
        // 批量插入数据源表
        etlSourceTableService.saveBatch(sourceTables);

        // 插入ETL任务质量规则信息
        List<EtlQualityRulesEntity> qualityRules = command.getQualityRules().stream()
                .map(rule -> {
                    rule.setEtlTaskId(entity.getTaskId());
                    rule.setId(null);
                    EtlQualityRulesEntity qualityRulesEntity = new EtlQualityRulesEntity();
                    BeanUtils.copyProperties(rule,qualityRulesEntity);
                    return qualityRulesEntity;
                }).collect(Collectors.toList());
        // 批量插入质量规则
        etlQualityRulesService.saveBatch(qualityRules);

        // 插入ETL任务字段映射信息
        List<EtlFieldMappingEntity> fieldMappings = command.getFieldMappings().stream()
                .map(mapping -> {
                    mapping.setEtlTaskId(entity.getTaskId());
                    mapping.setId(null);
                    EtlFieldMappingEntity mappingEntity = new EtlFieldMappingEntity();
                    BeanUtils.copyProperties(mapping,mappingEntity);
                    return mappingEntity;
                }).collect(Collectors.toList());
        // 批量插入字段映射
        etlFieldMappingService.saveBatch(fieldMappings);
    }

    @Override
    public String getExecutionLog(String executionId) {
        log.info("获取执行日志: executionId={}", executionId);

        try {
            // 查询执行历史记录
            EtlExecutionHistoryEntity history = executionHistoryMapper.selectByExecutionId(executionId);
            if (history == null) {
                return "执行记录不存在";
            }

            StringBuilder logContent = new StringBuilder();
            logContent.append("=== ETL任务执行日志 ===\n");
            logContent.append("执行ID: ").append(executionId).append("\n");
            logContent.append("任务ID: ").append(history.getTaskId()).append("\n");
            logContent.append("任务名称: ").append(history.getTaskName()).append("\n");
            logContent.append("开始时间: ").append(history.getStartTime()).append("\n");
            logContent.append("结束时间: ").append(history.getEndTime()).append("\n");
            logContent.append("执行状态: ").append(history.getStatus()).append("\n");
            logContent.append("处理记录数: ").append(history.getProcessedRecords()).append("\n");
            logContent.append("错误记录数: ").append(history.getErrorRecords()).append("\n");
            logContent.append("执行时长: ").append(history.getDuration()).append("ms\n");

            if (history.getErrorMessage() != null) {
                logContent.append("错误信息: ").append(history.getErrorMessage()).append("\n");
            }

            logContent.append("\n=== 详细执行步骤 ===\n");
            logContent.append("1. 加载任务配置\n");
            logContent.append("2. 验证配置参数\n");
            logContent.append("3. 生成SQL语句\n");
            logContent.append("4. 执行数据处理\n");
            logContent.append("5. 数据质量检查\n");
            logContent.append("6. 更新执行状态\n");

            return logContent.toString();

        } catch (Exception e) {
            log.error("获取执行日志失败: executionId={}", executionId, e);
            return "获取执行日志失败: " + e.getMessage();
        }
    }

    @Override
    public Object getExecutionMetrics(String executionId) {
        log.info("获取执行指标: executionId={}", executionId);

        try {
            // 查询执行历史记录
            EtlExecutionHistoryEntity history = executionHistoryMapper.selectByExecutionId(executionId);
            if (history == null) {
                return null;
            }

            Map<String, Object> metrics = new HashMap<>();
            metrics.put("executionId", executionId);
            metrics.put("taskId", history.getTaskId());
            metrics.put("taskName", history.getTaskName());
            metrics.put("status", history.getStatus());
            metrics.put("startTime", history.getStartTime());
            metrics.put("endTime", history.getEndTime());
            metrics.put("duration", history.getDuration());
            metrics.put("processedRecords", history.getProcessedRecords());
            metrics.put("errorRecords", history.getErrorRecords());

            // 计算性能指标
            if (history.getDuration() != null && history.getDuration() > 0) {
                long recordsPerSecond = (history.getProcessedRecords() * 1000) / history.getDuration();
                metrics.put("recordsPerSecond", recordsPerSecond);
            }

            // 计算成功率
            if (history.getProcessedRecords() != null && history.getProcessedRecords() > 0) {
                double successRate = ((double)(history.getProcessedRecords() - history.getErrorRecords())) / history.getProcessedRecords() * 100;
                metrics.put("successRate", Math.round(successRate * 100.0) / 100.0);
            }

            return metrics;

        } catch (Exception e) {
            log.error("获取执行指标失败: executionId={}", executionId, e);
            return null;
        }
    }

    @Override
    public Boolean validateEtlConfig(EtlTaskAddCommand command) {
        log.info("验证ETL配置: taskName={}", command.getTaskName());

        try {
            List<String> errors = new ArrayList<>();

            // 验证基本信息
            if (!StringUtils.hasText(command.getTaskName())) {
                errors.add("任务名称不能为空");
            }

            if (!StringUtils.hasText(command.getSourceLayer())) {
                errors.add("源数据层级不能为空");
            }

            if (!StringUtils.hasText(command.getTargetLayer())) {
                errors.add("目标数据层级不能为空");
            }

            if (!StringUtils.hasText(command.getTargetTable())) {
                errors.add("目标表名不能为空");
            }

            // 验证调度配置
            if ("CRON".equals(command.getScheduleType()) && !StringUtils.hasText(command.getCronExpression())) {
                errors.add("定时任务必须配置Cron表达式");
            }

            // 验证源表配置
            if (command.getSourceTables() == null || command.getSourceTables().isEmpty()) {
                errors.add("源表配置不能为空");
            }

            // 验证字段映射配置
            if (command.getFieldMappings() == null || command.getFieldMappings().isEmpty()) {
                errors.add("字段映射配置不能为空");
            }

            // 验证任务名称唯一性
            if (StringUtils.hasText(command.getTaskName())) {
                QueryWrapper<EtlTaskEntity> wrapper = new QueryWrapper<>();
                wrapper.eq("task_name", command.getTaskName());
                if (command.getTaskId() != null) {
                    wrapper.ne("task_id", command.getTaskId());
                }
                long count = etlTaskMapper.selectCount(wrapper);
                if (count > 0) {
                    errors.add("任务名称已存在");
                }
            }

            if (!errors.isEmpty()) {
                log.warn("ETL配置验证失败: {}", String.join(", ", errors));
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("验证ETL配置失败", e);
            return false;
        }
    }

    @Override
    public Object getDataLineage(Long taskId) {
        log.info("获取数据血缘关系: taskId={}", taskId);

        try {
            // 查询任务信息
            EtlTaskEntity task = etlTaskMapper.selectById(taskId);
            if (task == null) {
                return null;
            }

            // 查询源表配置
            List<EtlSourceTableEntity> sourceTables = etlSourceTableMapper.selectList(
                    new QueryWrapper<EtlSourceTableEntity>().eq("etl_task_id", taskId));

            // 构建血缘关系图
            Map<String, Object> lineage = new HashMap<>();
            lineage.put("taskId", taskId);
            lineage.put("taskName", task.getTaskName());
            lineage.put("sourceLayer", task.getSourceLayer());
            lineage.put("targetLayer", task.getTargetLayer());
            lineage.put("targetTable", task.getTargetTable());

            // 源表信息
            List<Map<String, Object>> sourceNodes = sourceTables.stream()
                    .map(source -> {
                        Map<String, Object> node = new HashMap<>();
                        node.put("id", "source_" + source.getId());
                        node.put("name", source.getTableName());
                        node.put("type", "source");
                        node.put("layer", task.getSourceLayer());
                        return node;
                    })
                    .collect(Collectors.toList());

            // 目标表信息
            Map<String, Object> targetNode = new HashMap<>();
            targetNode.put("id", "target_" + taskId);
            targetNode.put("name", task.getTargetTable());
            targetNode.put("type", "target");
            targetNode.put("layer", task.getTargetLayer());

            // 节点列表
            List<Map<String, Object>> nodes = new ArrayList<>(sourceNodes);
            nodes.add(targetNode);

            // 边列表
            List<Map<String, Object>> edges = sourceTables.stream()
                    .map(source -> {
                        Map<String, Object> edge = new HashMap<>();
                        edge.put("source", "source_" + source.getId());
                        edge.put("target", "target_" + taskId);
                        edge.put("label", "ETL转换");
                        return edge;
                    })
                    .collect(Collectors.toList());

            lineage.put("nodes", nodes);
            lineage.put("edges", edges);

            return lineage;

        } catch (Exception e) {
            log.error("获取数据血缘关系失败: taskId={}", taskId, e);
            return null;
        }
    }

    @Override
    public Object getTaskDependencies(Long taskId) {
        log.info("获取任务依赖关系: taskId={}", taskId);

        try {
            // 查询任务信息
            EtlTaskEntity task = etlTaskMapper.selectById(taskId);
            if (task == null) {
                return null;
            }

            Map<String, Object> dependencies = new HashMap<>();
            dependencies.put("taskId", taskId);
            dependencies.put("taskName", task.getTaskName());

            // 查找上游依赖（基于数据层级）
            List<Map<String, Object>> upstreamTasks = new ArrayList<>();
            if (!"ods_db".equals(task.getSourceLayer())) {
                // 查找源数据层的任务
                List<EtlTaskEntity> sourceTasks = etlTaskMapper.selectList(
                        new QueryWrapper<EtlTaskEntity>()
                                .eq("target_layer", task.getSourceLayer())
                                .eq("status", 1));

                upstreamTasks = sourceTasks.stream()
                        .map(sourceTask -> {
                            Map<String, Object> dep = new HashMap<>();
                            dep.put("taskId", sourceTask.getTaskId());
                            dep.put("taskName", sourceTask.getTaskName());
                            dep.put("layer", sourceTask.getTargetLayer());
                            dep.put("type", "upstream");
                            return dep;
                        })
                        .collect(Collectors.toList());
            }

            // 查找下游依赖
            List<Map<String, Object>> downstreamTasks = new ArrayList<>();
            List<EtlTaskEntity> targetTasks = etlTaskMapper.selectList(
                    new QueryWrapper<EtlTaskEntity>()
                            .eq("source_layer", task.getTargetLayer())
                            .eq("status", 1));

            downstreamTasks = targetTasks.stream()
                    .map(targetTask -> {
                        Map<String, Object> dep = new HashMap<>();
                        dep.put("taskId", targetTask.getTaskId());
                        dep.put("taskName", targetTask.getTaskName());
                        dep.put("layer", targetTask.getTargetLayer());
                        dep.put("type", "downstream");
                        return dep;
                    })
                    .collect(Collectors.toList());

            dependencies.put("upstreamTasks", upstreamTasks);
            dependencies.put("downstreamTasks", downstreamTasks);
            dependencies.put("upstreamCount", upstreamTasks.size());
            dependencies.put("downstreamCount", downstreamTasks.size());

            return dependencies;

        } catch (Exception e) {
            log.error("获取任务依赖关系失败: taskId={}", taskId, e);
            return null;
        }
    }

    @Override
    public Object exportEtlTaskConfig(Long taskId) {
        log.info("导出ETL任务配置: taskId={}", taskId);

        try {
            // 查询任务信息
            EtlTaskEntity task = etlTaskMapper.selectById(taskId);
            if (task == null) {
                return null;
            }

            // 查询相关配置
            List<EtlSourceTableEntity> sourceTables = etlSourceTableMapper.selectList(
                    new QueryWrapper<EtlSourceTableEntity>().eq("etl_task_id", taskId));

            List<EtlFieldMappingEntity> fieldMappings = etlFieldMappingMapper.selectList(
                    new QueryWrapper<EtlFieldMappingEntity>().eq("etl_task_id", taskId));

            List<EtlQualityRulesEntity> qualityRules = etlQualityRulesMapper.selectList(
                    new QueryWrapper<EtlQualityRulesEntity>().eq("etl_task_id", taskId));

            // 构建导出数据
            Map<String, Object> exportData = new HashMap<>();
            exportData.put("version", "1.0");
            exportData.put("exportTime", LocalDateTime.now());
            exportData.put("task", task);
            exportData.put("sourceTables", sourceTables);
            exportData.put("fieldMappings", fieldMappings);
            exportData.put("qualityRules", qualityRules);

            return exportData;

        } catch (Exception e) {
            log.error("导出ETL任务配置失败: taskId={}", taskId, e);
            return null;
        }
    }

    @Override
    public void importEtlTaskConfig(Object data) {
        log.info("导入ETL任务配置");

        try {
            // TODO: 实现导入逻辑
            // 1. 解析导入数据
            // 2. 验证数据格式
            // 3. 创建新任务或更新现有任务
            // 4. 导入相关配置

            log.info("ETL任务配置导入完成");

        } catch (Exception e) {
            log.error("导入ETL任务配置失败", e);
            throw new RuntimeException("导入失败: " + e.getMessage());
        }
    }
}
