package com.lacus.service.datacollection.engine.impl;

import com.lacus.service.datacollection.engine.FlowEngine;
import com.lacus.service.datacollection.engine.NodeExecutor;
import com.lacus.service.datacollection.model.FlowContext;
import com.lacus.service.datacollection.model.FlowDefinition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 流程引擎实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FlowEngineImpl implements FlowEngine {

    @Autowired
    private List<NodeExecutor> nodeExecutors;

    private final Map<String, NodeExecutor> executorMap = new ConcurrentHashMap<>();
    private final Map<String, FlowExecutionStatus> executionStatusMap = new ConcurrentHashMap<>();

    /**
     * 初始化执行器映射
     */
    public void init() {
        for (NodeExecutor executor : nodeExecutors) {
            executorMap.put(executor.getSupportedNodeType(), executor);
        }
        log.info("流程引擎初始化完成，注册节点执行器: {}", executorMap.keySet());
    }

    @Override
    public FlowExecutionResult execute(FlowDefinition flowDefinition, FlowContext context) {
        String executionId = context.getExecutionId();
        log.info("开始执行流程: executionId={}, flowId={}", executionId, flowDefinition.getFlowId());

        try {
            // 更新执行状态
            updateExecutionStatus(executionId, "RUNNING", null, 0, "流程开始执行");

            // 查找开始节点
            FlowDefinition.FlowNode startNode = findStartNode(flowDefinition);
            if (startNode == null) {
                throw new RuntimeException("未找到开始节点");
            }

            // 执行流程
            executeNode(startNode, flowDefinition, context);

            // 流程执行完成
            context.setStatus("SUCCESS");
            context.setEndTime(LocalDateTime.now());
            context.addLog("流程执行完成");

            updateExecutionStatus(executionId, "SUCCESS", null, 100, "流程执行成功");

            return new FlowExecutionResult(executionId, "SUCCESS", "流程执行成功", null, context);

        } catch (Exception e) {
            log.error("流程执行失败: executionId={}", executionId, e);
            context.setStatus("FAILED");
            context.setEndTime(LocalDateTime.now());
            context.setErrorMessage(e.getMessage());
            context.addLog("流程执行失败: " + e.getMessage());

            updateExecutionStatus(executionId, "FAILED", null, 0, "流程执行失败: " + e.getMessage());

            return new FlowExecutionResult(executionId, "FAILED", "流程执行失败: " + e.getMessage(), null, context);
        }
    }

    @Override
    public void stopExecution(String executionId) {
        log.info("停止流程执行: executionId={}", executionId);
        updateExecutionStatus(executionId, "STOPPED", null, 0, "流程已停止");
    }

    @Override
    public void pauseExecution(String executionId) {
        log.info("暂停流程执行: executionId={}", executionId);
        updateExecutionStatus(executionId, "PAUSED", null, 0, "流程已暂停");
    }

    @Override
    public void resumeExecution(String executionId) {
        log.info("恢复流程执行: executionId={}", executionId);
        updateExecutionStatus(executionId, "RUNNING", null, 0, "流程已恢复");
    }

    @Override
    public FlowExecutionStatus getExecutionStatus(String executionId) {
        return executionStatusMap.get(executionId);
    }

    /**
     * 执行节点
     */
    private void executeNode(FlowDefinition.FlowNode node, FlowDefinition flowDefinition, FlowContext context) {
        String executionId = context.getExecutionId();
        log.info("执行节点: executionId={}, nodeId={}, nodeType={}", executionId, node.getId(), node.getType());

        context.addLog("开始执行节点: " + node.getName() + " (" + node.getType() + ")");
        updateExecutionStatus(executionId, "RUNNING", node.getId(), 0, "执行节点: " + node.getName());

        try {
            // 获取节点执行器
            NodeExecutor executor = executorMap.get(node.getType());
            if (executor == null) {
                throw new RuntimeException("不支持的节点类型: " + node.getType());
            }

            // 执行节点
            NodeExecutor.NodeExecutionResult result = executor.execute(node, context);

            if ("SUCCESS".equals(result.getStatus())) {
                context.addLog("节点执行成功: " + node.getName());
                
                // 查找下一个节点
                FlowDefinition.FlowNode nextNode = findNextNode(node, flowDefinition, context, result);
                if (nextNode != null) {
                    executeNode(nextNode, flowDefinition, context);
                }
            } else if ("FAILED".equals(result.getStatus())) {
                throw new RuntimeException("节点执行失败: " + result.getMessage());
            } else if ("SKIP".equals(result.getStatus())) {
                context.addLog("节点被跳过: " + node.getName());
                
                // 查找下一个节点
                FlowDefinition.FlowNode nextNode = findNextNode(node, flowDefinition, context, result);
                if (nextNode != null) {
                    executeNode(nextNode, flowDefinition, context);
                }
            }

        } catch (Exception e) {
            log.error("节点执行失败: nodeId={}", node.getId(), e);
            context.addLog("节点执行失败: " + node.getName() + ", 错误: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 查找开始节点
     */
    private FlowDefinition.FlowNode findStartNode(FlowDefinition flowDefinition) {
        if (flowDefinition.getNodes() == null) {
            return null;
        }

        return flowDefinition.getNodes().stream()
                .filter(node -> "START".equals(node.getType()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 查找下一个节点
     */
    private FlowDefinition.FlowNode findNextNode(FlowDefinition.FlowNode currentNode, 
                                                FlowDefinition flowDefinition, 
                                                FlowContext context,
                                                NodeExecutor.NodeExecutionResult result) {
        
        // 如果是结束节点，返回null
        if ("END".equals(currentNode.getType())) {
            return null;
        }

        // 如果执行结果指定了下一个节点
        if (result.getNextNodeId() != null) {
            return findNodeById(flowDefinition, result.getNextNodeId());
        }

        // 查找连接的下一个节点
        if (flowDefinition.getEdges() == null) {
            return null;
        }

        for (FlowDefinition.FlowEdge edge : flowDefinition.getEdges()) {
            if (edge.getSourceNodeId().equals(currentNode.getId())) {
                // 检查条件
                if (evaluateCondition(edge.getCondition(), context)) {
                    return findNodeById(flowDefinition, edge.getTargetNodeId());
                }
            }
        }

        return null;
    }

    /**
     * 根据ID查找节点
     */
    private FlowDefinition.FlowNode findNodeById(FlowDefinition flowDefinition, String nodeId) {
        if (flowDefinition.getNodes() == null) {
            return null;
        }

        return flowDefinition.getNodes().stream()
                .filter(node -> node.getId().equals(nodeId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 评估条件表达式
     */
    private boolean evaluateCondition(String condition, FlowContext context) {
        // 如果没有条件，默认为true
        if (condition == null || condition.trim().isEmpty()) {
            return true;
        }

        try {
            // 这里可以集成SpEL或其他表达式引擎
            // 暂时简化处理
            return true;
        } catch (Exception e) {
            log.warn("条件表达式评估失败: {}", condition, e);
            return false;
        }
    }

    /**
     * 更新执行状态
     */
    private void updateExecutionStatus(String executionId, String status, String currentNodeId, Integer progress, String message) {
        FlowExecutionStatus executionStatus = new FlowExecutionStatus(executionId, status, currentNodeId, progress, message);
        executionStatusMap.put(executionId, executionStatus);
    }
}
