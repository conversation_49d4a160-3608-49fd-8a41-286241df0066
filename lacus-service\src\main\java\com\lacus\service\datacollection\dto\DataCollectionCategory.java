package com.lacus.service.datacollection.dto;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lacus.common.annotation.Excel;
import com.lacus.common.core.domain.BaseEntity;
import com.lacus.dao.system.query.AbstractPageQuery;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据采集分类对象 data_collection_category
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class DataCollectionCategory extends AbstractPageQuery
{
    private static final long serialVersionUID = 1L;

    /** 分类ID */
    private Long categoryId;

    /** 分类名称 */
    @NotBlank(message = "分类名称不能为空")
    @Size(min = 2, max = 100, message = "分类名称长度必须在2-100个字符之间")
    private String categoryName;

    /** 分类编码 */
    @NotBlank(message = "分类编码不能为空")
    @Size(min = 2, max = 50, message = "分类编码长度必须在2-50个字符之间")
    private String categoryCode;

    /** 父分类ID */
    private Long parentId;

    /** 分类路径 */
    private String categoryPath;

    /** 分类层级 */
    private Integer categoryLevel;

    /** 排序 */
    private Integer sortOrder;

    /** 分类图标 */
    private String categoryIcon;

    /** 分类描述 */
    private String categoryDesc;

    /** 状态(0:禁用 1:启用) */
    private Integer status;

    /** 子分类 */
    private List<DataCollectionCategory> children = new ArrayList<DataCollectionCategory>();

    /** 任务数量 */
    private Integer taskCount;

    @Override
    public QueryWrapper toQueryWrapper() {
        return null;
    }
}
