//package com.lacus.service.datawarehouse.engine;
//
//import com.lacus.service.datawarehouse.dto.EtlFieldMappingDTO;
//import com.lacus.service.datawarehouse.dto.EtlSourceTableDTO;
//import com.lacus.service.datawarehouse.model.EtlTaskModel;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * ETL SQL生成器
// * 根据ETL任务配置生成对应的SQL语句
// *
// * <AUTHOR>
// */
//@Slf4j
//@Component
//public class EtlSqlGenerator {
//
//    /**
//     * 生成ETL SQL语句
//     */
//    public List<String> generateSql(EtlTaskModel taskModel) {
//        List<String> sqlStatements = new ArrayList<>();
//
//        log.info("开始生成ETL SQL: taskId={}, sourceLayer={}, targetLayer={}",
//                taskModel.getTaskId(), taskModel.getSourceLayer(), taskModel.getTargetLayer());
//
//        try {
//            // 根据写入模式生成不同的SQL
//            String writeMode = taskModel.getWriteMode();
//
//            switch (writeMode) {
//                case "OVERWRITE":
//                    sqlStatements.addAll(generateOverwriteSql(taskModel));
//                    break;
//                case "APPEND":
//                    sqlStatements.addAll(generateAppendSql(taskModel));
//                    break;
//                case "UPSERT":
//                    sqlStatements.addAll(generateUpsertSql(taskModel));
//                    break;
//                default:
//                    throw new IllegalArgumentException("不支持的写入模式: " + writeMode);
//            }
//
//            log.info("ETL SQL生成完成: taskId={}, sqlCount={}", taskModel.getTaskId(), sqlStatements.size());
//
//        } catch (Exception e) {
//            log.error("ETL SQL生成失败: taskId={}", taskModel.getTaskId(), e);
//            throw new RuntimeException("SQL生成失败: " + e.getMessage(), e);
//        }
//
//        return sqlStatements;
//    }
//
//    /**
//     * 生成覆盖写入SQL
//     */
//    private List<String> generateOverwriteSql(EtlTaskModel taskModel) {
//        List<String> sqlStatements = new ArrayList<>();
//
//        // 1. 删除目标表数据（如果需要）
//        if (needTruncateTarget(taskModel)) {
//            sqlStatements.add(generateTruncateSql(taskModel));
//        }
//
//        // 2. 生成INSERT SQL
//        sqlStatements.add(generateInsertSql(taskModel));
//
//        return sqlStatements;
//    }
//
//    /**
//     * 生成追加写入SQL
//     */
//    private List<String> generateAppendSql(EtlTaskModel taskModel) {
//        List<String> sqlStatements = new ArrayList<>();
//
//        // 直接生成INSERT SQL
//        sqlStatements.add(generateInsertSql(taskModel));
//
//        return sqlStatements;
//    }
//
//    /**
//     * 生成更新插入SQL
//     */
//    private List<String> generateUpsertSql(EtlTaskModel taskModel) {
//        List<String> sqlStatements = new ArrayList<>();
//
//        // Doris支持的UPSERT语法
//        sqlStatements.add(generateUpsertInsertSql(taskModel));
//
//        return sqlStatements;
//    }
//
//    /**
//     * 生成TRUNCATE SQL
//     */
//    private String generateTruncateSql(EtlTaskModel taskModel) {
//        return String.format("TRUNCATE TABLE %s.%s",
//                getTargetDatabase(taskModel), taskModel.getTargetTable());
//    }
//
//    /**
//     * 生成INSERT SQL
//     */
//    private String generateInsertSql(EtlTaskModel taskModel) {
//        StringBuilder sql = new StringBuilder();
//
//        // INSERT INTO 目标表
//        sql.append("INSERT INTO ").append(getTargetDatabase(taskModel))
//           .append(".").append(taskModel.getTargetTable());
//
//        // 字段列表
//        List<String> targetFields = getTargetFields(taskModel);
//        sql.append(" (").append(String.join(", ", targetFields)).append(")");
//
//        // SELECT 子句
//        sql.append("\nSELECT ");
//        sql.append(generateSelectFields(taskModel));
//
//        // FROM 子句
//        sql.append("\nFROM ");
//        sql.append(generateFromClause(taskModel));
//
//        // WHERE 子句
//        String whereClause = generateWhereClause(taskModel);
//        if (whereClause != null && !whereClause.trim().isEmpty()) {
//            sql.append("\nWHERE ").append(whereClause);
//        }
//
//        return sql.toString();
//    }
//
//    /**
//     * 生成UPSERT INSERT SQL
//     */
//    private String generateUpsertInsertSql(EtlTaskModel taskModel) {
//        StringBuilder sql = new StringBuilder();
//
//        // INSERT INTO 目标表
//        sql.append("INSERT INTO ").append(getTargetDatabase(taskModel))
//           .append(".").append(taskModel.getTargetTable());
//
//        // 字段列表
//        List<String> targetFields = getTargetFields(taskModel);
//        sql.append(" (").append(String.join(", ", targetFields)).append(")");
//
//        // SELECT 子句
//        sql.append("\nSELECT ");
//        sql.append(generateSelectFields(taskModel));
//
//        // FROM 子句
//        sql.append("\nFROM ");
//        sql.append(generateFromClause(taskModel));
//
//        // WHERE 子句
//        String whereClause = generateWhereClause(taskModel);
//        if (whereClause != null && !whereClause.trim().isEmpty()) {
//            sql.append("\nWHERE ").append(whereClause);
//        }
//
//        // ON DUPLICATE KEY UPDATE（Doris语法）
//        sql.append("\nON DUPLICATE KEY UPDATE ");
//        sql.append(generateUpdateFields(taskModel));
//
//        return sql.toString();
//    }
//
//    /**
//     * 生成SELECT字段
//     */
//    private String generateSelectFields(EtlTaskModel taskModel) {
//        if (taskModel.getFieldMappings() == null || taskModel.getFieldMappings().isEmpty()) {
//            return "*";
//        }
//
//        return taskModel.getFieldMappings().stream()
//                .map(mapping -> {
//                    String sourceField = mapping.getSourceField();
//                    String targetField = mapping.getTargetField();
//                    String transformation = mapping.getExpression();
//
//                    if (transformation != null && !transformation.trim().isEmpty()) {
//                        // 应用字段转换
//                        return String.format("%s AS %s", transformation, targetField);
//                    } else {
//                        // 直接映射
//                        return sourceField.equals(targetField) ? sourceField :
//                               String.format("%s AS %s", sourceField, targetField);
//                    }
//                })
//                .collect(Collectors.joining(", "));
//    }
//
//    /**
//     * 生成FROM子句
//     */
//    private String generateFromClause(EtlTaskModel taskModel) {
//        if (taskModel.getSourceTables() == null || taskModel.getSourceTables().isEmpty()) {
//            throw new IllegalArgumentException("源表配置不能为空");
//        }
//
//        StringBuilder fromClause = new StringBuilder();
//
//        // 主表
//        EtlSourceTableDTO mainTable = taskModel.getSourceTables().get(0);
//        String sourceDatabase = getSourceDatabase(taskModel);
//        fromClause.append(sourceDatabase).append(".").append(mainTable.getTableName());
//
//        if (mainTable.getAlias() != null && !mainTable.getAlias().trim().isEmpty()) {
//            fromClause.append(" AS ").append(mainTable.getAlias());
//        }
//
//        // 关联表（如果有多个源表）
//        for (int i = 1; i < taskModel.getSourceTables().size(); i++) {
//            EtlSourceTableDTO joinTable = taskModel.getSourceTables().get(i);
//            fromClause.append("\nLEFT JOIN ").append(sourceDatabase)
//                      .append(".").append(joinTable.getTableName());
//
//            if (joinTable.getAlias() != null && !joinTable.getAlias().trim().isEmpty()) {
//                fromClause.append(" AS ").append(joinTable.getAlias());
//            }
//
//            // TODO: 添加JOIN条件配置
//        }
//
//        return fromClause.toString();
//    }
//
//    /**
//     * 生成WHERE子句
//     */
//    private String generateWhereClause(EtlTaskModel taskModel) {
//        List<String> conditions = new ArrayList<>();
//
//        // 添加源表的WHERE条件
//        if (taskModel.getSourceTables() != null) {
//            for (EtlSourceTableDTO sourceTable : taskModel.getSourceTables()) {
//                if (sourceTable.getWhereCondition() != null &&
//                    !sourceTable.getWhereCondition().trim().isEmpty()) {
//                    conditions.add("(" + sourceTable.getWhereCondition() + ")");
//                }
//            }
//        }
//
//        // 添加增量同步条件
//        String incrementalCondition = generateIncrementalCondition(taskModel);
//        if (incrementalCondition != null && !incrementalCondition.trim().isEmpty()) {
//            conditions.add(incrementalCondition);
//        }
//
//        return conditions.isEmpty() ? null : String.join(" AND ", conditions);
//    }
//
//    /**
//     * 生成增量同步条件
//     */
//    private String generateIncrementalCondition(EtlTaskModel taskModel) {
//        // TODO: 根据任务配置生成增量同步条件
//        // 例如：基于时间戳、版本号等
//        return null;
//    }
//
//    /**
//     * 生成UPDATE字段（用于UPSERT）
//     */
//    private String generateUpdateFields(EtlTaskModel taskModel) {
//        List<String> targetFields = getTargetFields(taskModel);
//
//        return targetFields.stream()
//                .filter(field -> !isPrimaryKey(field, taskModel))
//                .map(field -> String.format("%s = VALUES(%s)", field, field))
//                .collect(Collectors.joining(", "));
//    }
//
//    /**
//     * 获取目标字段列表
//     */
//    private List<String> getTargetFields(EtlTaskModel taskModel) {
//        if (taskModel.getFieldMappings() == null || taskModel.getFieldMappings().isEmpty()) {
//            // 如果没有字段映射配置，返回默认字段
//            return new ArrayList<>(Arrays.asList("*"));
//        }
//
//        return taskModel.getFieldMappings().stream()
//                .map(EtlFieldMappingDTO::getTargetField)
//                .collect(Collectors.toList());
//    }
//
//    /**
//     * 获取源数据库名
//     */
//    private String getSourceDatabase(EtlTaskModel taskModel) {
//        return taskModel.getSourceLayer().toLowerCase();
//    }
//
//    /**
//     * 获取目标数据库名
//     */
//    private String getTargetDatabase(EtlTaskModel taskModel) {
//        return taskModel.getTargetLayer().toLowerCase();
//    }
//
//    /**
//     * 判断是否需要清空目标表
//     */
//    private boolean needTruncateTarget(EtlTaskModel taskModel) {
//        // 全量覆盖模式需要清空目标表
//        return "OVERWRITE".equals(taskModel.getWriteMode());
//    }
//
//    /**
//     * 判断是否为主键字段
//     */
//    private boolean isPrimaryKey(String field, EtlTaskModel taskModel) {
//        // TODO: 实现主键判断逻辑
//        Object primaryKeys = taskConfig.get("primaryKeys");
//        if (primaryKeys instanceof String) {
//            return ((String) primaryKeys).contains(fieldName);
//        } else if (primaryKeys instanceof List) {
//            return ((List<?>) primaryKeys).contains(fieldName);
//        }
//        return false;
//    }
//}
