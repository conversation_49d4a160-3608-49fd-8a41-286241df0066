package com.lacus.dao.datacollection.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 采集执行历史实体
 *
 * <AUTHOR>
 */
@Data
@TableName("collection_execution_history")
public class CollectionExecutionHistoryEntity {

    /**
     * 执行ID
     */
    @TableId("execution_id")
    private String executionId;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 执行类型：MANUAL、SCHEDULE、API
     */
    @TableField("execution_type")
    private String executionType;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 执行时长(毫秒)
     */
    @TableField("duration")
    private Long duration;

    /**
     * 执行状态：RUNNING、SUCCESS、FAILED、CANCELLED
     */
    @TableField("status")
    private String status;

    /**
     * 处理记录数
     */
    @TableField("processed_records")
    private Long processedRecords;

    /**
     * 错误记录数
     */
    @TableField("error_records")
    private Long errorRecords;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 执行日志
     */
    @TableField("execution_log")
    private String executionLog;

    /**
     * 流程上下文JSON
     */
    @TableField("flow_context")
    private String flowContext;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
}
