package com.lacus.service.datacollection.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lacus.dao.datacollection.mapper.DataCollectionTaskMapper;
import com.lacus.dao.datacollection.entity.DataCollectionTaskEntity;
import com.lacus.dao.datacollection.vo.DataCollectionTaskExecutionVO;
import com.lacus.dao.datacollection.vo.DataCollectionTaskPageQuery;
import com.lacus.dao.datacollection.vo.DataCollectionTaskVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据采集任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class DataCollectionTaskServiceImpl implements IDataCollectionTaskService {

    @Autowired
    private DataCollectionTaskMapper taskMapper;

    @Override
    public IPage<DataCollectionTaskVO> selectTaskPage(DataCollectionTaskPageQuery pageQuery) {
        Page<DataCollectionTaskEntity> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        
        IPage<DataCollectionTaskEntity> entityPage = taskMapper.selectTaskPage(
                page,
                pageQuery.getTaskName(),
                pageQuery.getTaskCode(),
                pageQuery.getCategoryId(),
                pageQuery.getCollectionType(),
                pageQuery.getSourceType(),
                pageQuery.getScheduleType(),
                pageQuery.getStatus(),
                pageQuery.getTargetDatabase(),
                pageQuery.getTargetTable(),
                pageQuery.getCreateTimeStart(),
                pageQuery.getCreateTimeEnd(),
                pageQuery.getKeyword()
        );
        
        // 转换为VO
        IPage<DataCollectionTaskVO> voPage = new Page<>();
        BeanUtils.copyProperties(entityPage, voPage);
        
        List<DataCollectionTaskVO> voList = entityPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        voPage.setRecords(voList);
        
        return voPage;
    }

    @Override
    public DataCollectionTaskVO selectTaskById(Long taskId) {
        DataCollectionTaskEntity entity = taskMapper.selectById(taskId);
        if (entity == null) {
            return null;
        }
        
        DataCollectionTaskVO vo = convertToVO(entity);
        // 设置扩展信息
        enrichTaskVO(vo);
        
        return vo;
    }

    @Override
    @Transactional
    public boolean insertTask(DataCollectionTaskVO taskVO) {
        DataCollectionTaskEntity entity = convertToEntity(taskVO);
        
        // 设置默认值
        if (entity.getStatus() == null) {
            entity.setStatus(1);
        }
        if (entity.getPriority() == null) {
            entity.setPriority(5);
        }
        if (entity.getTimeout() == null) {
            entity.setTimeout(3600);
        }
        if (entity.getRetryCount() == null) {
            entity.setRetryCount(3);
        }
        if (entity.getVersion() == null) {
            entity.setVersion(1);
        }
        
        entity.setCreateTime(new Date());
        
        return taskMapper.insert(entity) > 0;
    }

    @Override
    @Transactional
    public boolean updateTask(DataCollectionTaskVO taskVO) {
        DataCollectionTaskEntity entity = convertToEntity(taskVO);
        entity.setUpdateTime(new Date());
        
        // 更新版本号
        updateTaskVersion(entity.getTaskId());
        
        return taskMapper.updateById(entity) > 0;
    }

    @Override
    @Transactional
    public boolean deleteTaskByIds(List<Long> taskIds) {
        // 检查任务是否可以删除（是否正在运行）
        for (Long taskId : taskIds) {
            if (isTaskRunning(taskId)) {
                throw new RuntimeException("任务ID为 " + taskId + " 的任务正在运行，不能删除");
            }
        }
        
        return taskMapper.deleteBatchByIds(taskIds) > 0;
    }

    @Override
    @Transactional
    public boolean deleteTaskById(Long taskId) {
        if (isTaskRunning(taskId)) {
            throw new RuntimeException("任务正在运行，不能删除");
        }
        
        return taskMapper.deleteById(taskId) > 0;
    }

    @Override
    public boolean checkTaskCodeUnique(String taskCode, Long taskId) {
        DataCollectionTaskEntity entity = taskMapper.checkTaskCodeUnique(taskCode, taskId);
        return entity == null;
    }

    @Override
    public boolean checkTaskNameUnique(String taskName, Long categoryId, Long taskId) {
        DataCollectionTaskEntity entity = taskMapper.checkTaskNameUnique(taskName, categoryId, taskId);
        return entity == null;
    }

    @Override
    @Transactional
    public boolean changeTaskStatus(Long taskId, Integer status) {
        // 如果要禁用任务，检查是否正在运行
        if (status == 0 && isTaskRunning(taskId)) {
            throw new RuntimeException("任务正在运行，不能禁用");
        }
        
        return taskMapper.updateTaskStatus(taskId, status) > 0;
    }

    @Override
    @Transactional
    public boolean batchUpdateTaskStatus(List<Long> taskIds, Integer status) {
        // 如果要禁用任务，检查是否有正在运行的任务
        if (status == 0) {
            for (Long taskId : taskIds) {
                if (isTaskRunning(taskId)) {
                    throw new RuntimeException("任务ID为 " + taskId + " 的任务正在运行，不能禁用");
                }
            }
        }
        
        return taskMapper.batchUpdateTaskStatus(taskIds, status) > 0;
    }

    @Override
    public String executeTask(Long taskId) {
        // 检查任务是否存在且启用
        DataCollectionTaskEntity task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        if (task.getStatus() == 0) {
            throw new RuntimeException("任务已禁用，不能执行");
        }
        if (isTaskRunning(taskId)) {
            throw new RuntimeException("任务正在运行中");
        }
        
        // 生成执行ID
        String executionId = "exec_" + taskId + "_" + System.currentTimeMillis();
        
        // TODO: 实际的任务执行逻辑
        // 这里应该调用任务执行引擎
        
        return executionId;
    }

    @Override
    public boolean stopTask(Long taskId) {
        // 检查任务是否正在运行
        if (!isTaskRunning(taskId)) {
            throw new RuntimeException("任务未在运行中");
        }
        
        // TODO: 实际的任务停止逻辑
        
        return true;
    }

    @Override
    @Transactional
    public Long copyTask(Long taskId, String newTaskName) {
        DataCollectionTaskEntity originalTask = taskMapper.selectById(taskId);
        if (originalTask == null) {
            throw new RuntimeException("原任务不存在");
        }
        
        // 创建新任务
        DataCollectionTaskEntity newTask = new DataCollectionTaskEntity();
        BeanUtils.copyProperties(originalTask, newTask);
        
        // 重置关键字段
        newTask.setTaskId(null);
        newTask.setTaskName(newTaskName);
        newTask.setTaskCode(generateTaskCode());
        newTask.setStatus(0); // 默认禁用
        newTask.setVersion(1);
        newTask.setCreateTime(new Date());
        
        taskMapper.insert(newTask);
        return newTask.getTaskId();
    }

    @Override
    @Transactional
    public boolean moveTask(Long taskId, Long targetCategoryId, String moveReason) {
        return taskMapper.batchMoveTasksToCategory(Arrays.asList(taskId), targetCategoryId) > 0;
    }

    @Override
    public List<DataCollectionTaskExecutionVO> getTaskHistory(Long taskId, String status, String startTime, String endTime) {
        // TODO: 实现任务执行历史查询
        return new ArrayList<>();
    }

    @Override
    public DataCollectionTaskExecutionVO getExecutionDetail(String executionId) {
        // TODO: 实现执行详情查询
        return new DataCollectionTaskExecutionVO();
    }

    @Override
    public Map<String, Object> validateTask(Long taskId) {
        Map<String, Object> result = new HashMap<>();
        
        DataCollectionTaskEntity task = taskMapper.selectById(taskId);
        if (task == null) {
            result.put("valid", false);
            result.put("message", "任务不存在");
            return result;
        }
        
        List<String> errors = new ArrayList<>();
        
        // 验证基本信息
        if (!StringUtils.hasText(task.getTaskName())) {
            errors.add("任务名称不能为空");
        }
        if (!StringUtils.hasText(task.getTaskCode())) {
            errors.add("任务编码不能为空");
        }
        if (!StringUtils.hasText(task.getCollectionType())) {
            errors.add("采集类型不能为空");
        }
        if (!StringUtils.hasText(task.getSourceType())) {
            errors.add("数据源类型不能为空");
        }
        
        // 验证调度配置
        if ("CRON".equals(task.getScheduleType()) && !StringUtils.hasText(task.getCronExpression())) {
            errors.add("CRON调度类型必须配置Cron表达式");
        }
        
        // 验证目标配置
        if (!StringUtils.hasText(task.getTargetDatabase())) {
            errors.add("目标数据库不能为空");
        }
        if (!StringUtils.hasText(task.getTargetTable())) {
            errors.add("目标表不能为空");
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("message", errors.isEmpty() ? "验证通过" : "验证失败");
        
        return result;
    }

    @Override
    public Map<String, Object> previewTask(Long taskId) {
        // TODO: 实现任务预览功能
        Map<String, Object> result = new HashMap<>();
        result.put("preview", "任务预览功能待实现");
        return result;
    }

    @Override
    public void exportTask(HttpServletResponse response, Long taskId) {
        // TODO: 实现任务导出功能
    }

    @Override
    public Map<String, Object> importTask(Map<String, Object> data) {
        // TODO: 实现任务导入功能
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "任务导入功能待实现");
        return result;
    }

    @Override
    public Map<String, Object> getTaskStats(Long categoryId) {
        return taskMapper.selectTaskStats(categoryId);
    }

    @Override
    public Map<String, Object> getTaskDependencies(Long taskId) {
        Map<String, Object> result = new HashMap<>();
        result.put("dependencies", taskMapper.selectTaskDependencies(taskId));
        result.put("preDependencies", taskMapper.selectTaskPreDependencies(taskId));
        result.put("postDependencies", taskMapper.selectTaskPostDependencies(taskId));
        return result;
    }

    @Override
    public Map<String, Object> saveFlowDefinition(Map<String, Object> data) {
        // TODO: 实现流程定义保存
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "流程定义保存功能待实现");
        return result;
    }

    @Override
    public Map<String, Object> getFlowDefinition(Long taskId) {
        DataCollectionTaskEntity task = taskMapper.selectById(taskId);
        Map<String, Object> result = new HashMap<>();
        if (task != null && StringUtils.hasText(task.getFlowDefinition())) {
            // TODO: 解析JSON流程定义
            result.put("flowDefinition", task.getFlowDefinition());
        }
        return result;
    }

    @Override
    public Map<String, Object> validateFlowDefinition(Map<String, Object> data) {
        // TODO: 实现流程定义验证
        Map<String, Object> result = new HashMap<>();
        result.put("valid", true);
        result.put("message", "流程定义验证功能待实现");
        return result;
    }

    @Override
    public Map<String, Object> executeFlow(Map<String, Object> data) {
        // TODO: 实现流程执行
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "流程执行功能待实现");
        return result;
    }

    @Override
    public DataCollectionTaskVO selectTaskByCode(String taskCode) {
        DataCollectionTaskEntity entity = taskMapper.selectByTaskCode(taskCode);
        return entity != null ? convertToVO(entity) : null;
    }

    @Override
    public DataCollectionTaskVO selectTaskByName(String taskName) {
        DataCollectionTaskEntity entity = taskMapper.selectByTaskName(taskName);
        return entity != null ? convertToVO(entity) : null;
    }

    @Override
    public List<DataCollectionTaskVO> selectEnabledTasks() {
        List<DataCollectionTaskEntity> entities = taskMapper.selectEnabledTasks();
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DataCollectionTaskVO> selectTasksByScheduleType(String scheduleType) {
        List<DataCollectionTaskEntity> entities = taskMapper.selectByScheduleType(scheduleType);
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DataCollectionTaskVO> selectTasksByCategoryId(Long categoryId, Boolean onlyEnabled) {
        Integer status = onlyEnabled != null && onlyEnabled ? 1 : null;
        List<DataCollectionTaskEntity> entities = taskMapper.selectByCategoryId(categoryId, status);
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DataCollectionTaskVO> selectRecentExecutedTasks(Integer limit) {
        List<DataCollectionTaskEntity> entities = taskMapper.selectRecentExecutedTasks(limit);
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DataCollectionTaskVO> selectFailedTasks() {
        List<DataCollectionTaskEntity> entities = taskMapper.selectFailedTasks();
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DataCollectionTaskVO> selectRunningTasks() {
        List<DataCollectionTaskEntity> entities = taskMapper.selectRunningTasks();
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DataCollectionTaskVO> selectHotTasks(Integer limit) {
        List<DataCollectionTaskEntity> entities = taskMapper.selectHotTasks(limit);
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DataCollectionTaskVO> selectTasksByCollectionType(String collectionType, Boolean onlyEnabled) {
        Integer status = onlyEnabled != null && onlyEnabled ? 1 : null;
        List<DataCollectionTaskEntity> entities = taskMapper.selectByCollectionType(collectionType, status);
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DataCollectionTaskVO> selectTasksBySourceType(String sourceType, Boolean onlyEnabled) {
        Integer status = onlyEnabled != null && onlyEnabled ? 1 : null;
        List<DataCollectionTaskEntity> entities = taskMapper.selectBySourceType(sourceType, status);
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getTaskCreateStats(Integer days) {
        return taskMapper.selectTaskCreateStats(days);
    }

    @Override
    @Transactional
    public boolean updateTaskVersion(Long taskId) {
        return taskMapper.updateTaskVersion(taskId) > 0;
    }

    // ==================== 私有方法 ====================

    /**
     * 实体转VO
     */
    private DataCollectionTaskVO convertToVO(DataCollectionTaskEntity entity) {
        DataCollectionTaskVO vo = new DataCollectionTaskVO();
        BeanUtils.copyProperties(entity, vo);

        // 设置状态文本
        vo.setStatusText(entity.getStatus() == 1 ? "启用" : "禁用");

        // 设置调度类型文本
        vo.setScheduleTypeText(getScheduleTypeText(entity.getScheduleType()));

        // 设置采集类型文本
        vo.setCollectionTypeText(getCollectionTypeText(entity.getCollectionType()));

        // 设置数据源类型文本
        vo.setSourceTypeText(getSourceTypeText(entity.getSourceType()));

        // 设置写入模式文本
        vo.setWriteModeText(getWriteModeText(entity.getWriteMode()));

        return vo;
    }

    /**
     * VO转实体
     */
    private DataCollectionTaskEntity convertToEntity(DataCollectionTaskVO vo) {
        DataCollectionTaskEntity entity = new DataCollectionTaskEntity();
        BeanUtils.copyProperties(vo, entity);
        return entity;
    }

    /**
     * 丰富任务VO信息
     */
    private void enrichTaskVO(DataCollectionTaskVO vo) {
        // 设置是否可以执行
        vo.setCanExecute(vo.getStatus() == 1 && !isTaskRunning(vo.getTaskId()));

        // 设置是否正在运行
        vo.setIsRunning(isTaskRunning(vo.getTaskId()));

        // TODO: 设置其他扩展信息
        vo.setExecuteCount(0);
        vo.setSuccessCount(0);
        vo.setFailureCount(0);
        vo.setAvgExecuteTime(0L);
        vo.setSuccessRate(0.0);
    }

    /**
     * 检查任务是否正在运行
     */
    private boolean isTaskRunning(Long taskId) {
        // TODO: 实际的运行状态检查逻辑
        return false;
    }

    /**
     * 生成任务编码
     */
    private String generateTaskCode() {
        return "TASK_" + System.currentTimeMillis();
    }

    /**
     * 获取调度类型文本
     */
    private String getScheduleTypeText(String scheduleType) {
        if (scheduleType == null) return "";
        switch (scheduleType) {
            case "MANUAL": return "手动执行";
            case "CRON": return "定时调度";
            case "REALTIME": return "实时采集";
            default: return scheduleType;
        }
    }

    /**
     * 获取采集类型文本
     */
    private String getCollectionTypeText(String collectionType) {
        if (collectionType == null) return "";
        switch (collectionType) {
            case "API": return "API采集";
            case "SQL": return "SQL采集";
            case "FILE": return "文件采集";
            default: return collectionType;
        }
    }

    /**
     * 获取数据源类型文本
     */
    private String getSourceTypeText(String sourceType) {
        if (sourceType == null) return "";
        switch (sourceType) {
            case "MYSQL": return "MySQL";
            case "SQLSERVER": return "SQL Server";
            case "ORACLE": return "Oracle";
            case "API": return "API接口";
            default: return sourceType;
        }
    }

    /**
     * 获取写入模式文本
     */
    private String getWriteModeText(String writeMode) {
        if (writeMode == null) return "";
        switch (writeMode) {
            case "OVERWRITE": return "覆盖写入";
            case "APPEND": return "追加写入";
            case "UPSERT": return "更新插入";
            case "AUTO_CREATE": return "自动创建";
            default: return writeMode;
        }
    }
}
