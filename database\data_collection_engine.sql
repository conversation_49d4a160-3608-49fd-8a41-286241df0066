-- 数据采集规则引擎相关表

-- 1. 数据采集任务表
CREATE TABLE `data_collection_task` (
  `task_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_desc` varchar(500) DEFAULT NULL COMMENT '任务描述',
  `collection_type` varchar(20) NOT NULL COMMENT '采集类型：API、SQL',
  `source_type` varchar(20) NOT NULL COMMENT '数据源类型：MYSQL、SQLSERVER、API',
  `source_config` text COMMENT '数据源配置JSON',
  `target_database` varchar(50) NOT NULL COMMENT '目标数据库',
  `target_table` varchar(100) NOT NULL COMMENT '目标表名',
  `write_mode` varchar(20) NOT NULL COMMENT '写入模式：OVERWRITE、APPEND、UPSERT、AUTO_CREATE',
  `flow_definition` longtext COMMENT '流程定义JSON',
  `schedule_type` varchar(20) DEFAULT 'MANUAL' COMMENT '调度类型：MANUAL、CRON、REALTIME',
  `cron_expression` varchar(100) DEFAULT NULL COMMENT 'Cron表达式',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`task_id`),
  KEY `idx_task_name` (`task_name`),
  KEY `idx_status` (`status`),
  KEY `idx_collection_type` (`collection_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据采集任务表';

-- 2. 流程节点定义表
CREATE TABLE `flow_node_definition` (
  `node_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '节点ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `node_key` varchar(50) NOT NULL COMMENT '节点唯一标识',
  `node_name` varchar(100) NOT NULL COMMENT '节点名称',
  `node_type` varchar(30) NOT NULL COMMENT '节点类型：START、END、API_CALL、SQL_QUERY、LOOP、CONDITION、DATA_TRANSFORM、DATA_WRITE',
  `node_config` text COMMENT '节点配置JSON',
  `position_x` int(11) DEFAULT '0' COMMENT 'X坐标',
  `position_y` int(11) DEFAULT '0' COMMENT 'Y坐标',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`node_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_node_key` (`node_key`),
  CONSTRAINT `fk_flow_node_task` FOREIGN KEY (`task_id`) REFERENCES `data_collection_task` (`task_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程节点定义表';

-- 3. 流程连线定义表
CREATE TABLE `flow_edge_definition` (
  `edge_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '连线ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `edge_key` varchar(50) NOT NULL COMMENT '连线唯一标识',
  `source_node_key` varchar(50) NOT NULL COMMENT '源节点标识',
  `target_node_key` varchar(50) NOT NULL COMMENT '目标节点标识',
  `condition_expression` varchar(500) DEFAULT NULL COMMENT '条件表达式',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`edge_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_source_node` (`source_node_key`),
  KEY `idx_target_node` (`target_node_key`),
  CONSTRAINT `fk_flow_edge_task` FOREIGN KEY (`task_id`) REFERENCES `data_collection_task` (`task_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程连线定义表';

-- 4. 数据源配置表
CREATE TABLE `data_source_config` (
  `source_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据源ID',
  `source_name` varchar(100) NOT NULL COMMENT '数据源名称',
  `source_type` varchar(20) NOT NULL COMMENT '数据源类型：MYSQL、SQLSERVER、API、ORACLE',
  `connection_config` text NOT NULL COMMENT '连接配置JSON',
  `test_sql` varchar(500) DEFAULT NULL COMMENT '测试SQL',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`source_id`),
  UNIQUE KEY `uk_source_name` (`source_name`),
  KEY `idx_source_type` (`source_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据源配置表';

-- 5. 任务执行历史表
CREATE TABLE `collection_execution_history` (
  `execution_id` varchar(64) NOT NULL COMMENT '执行ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `execution_type` varchar(20) NOT NULL COMMENT '执行类型：MANUAL、SCHEDULE、API',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` bigint(20) DEFAULT NULL COMMENT '执行时长(毫秒)',
  `status` varchar(20) NOT NULL COMMENT '执行状态：RUNNING、SUCCESS、FAILED、CANCELLED',
  `processed_records` bigint(20) DEFAULT '0' COMMENT '处理记录数',
  `error_records` bigint(20) DEFAULT '0' COMMENT '错误记录数',
  `error_message` text COMMENT '错误信息',
  `execution_log` longtext COMMENT '执行日志',
  `flow_context` text COMMENT '流程上下文JSON',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`execution_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采集任务执行历史表';

-- 6. 节点执行历史表
CREATE TABLE `node_execution_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `execution_id` varchar(64) NOT NULL COMMENT '执行ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `node_key` varchar(50) NOT NULL COMMENT '节点标识',
  `node_name` varchar(100) NOT NULL COMMENT '节点名称',
  `node_type` varchar(30) NOT NULL COMMENT '节点类型',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` bigint(20) DEFAULT NULL COMMENT '执行时长(毫秒)',
  `status` varchar(20) NOT NULL COMMENT '执行状态：RUNNING、SUCCESS、FAILED、SKIPPED',
  `input_data` text COMMENT '输入数据JSON',
  `output_data` text COMMENT '输出数据JSON',
  `error_message` text COMMENT '错误信息',
  `execution_log` text COMMENT '执行日志',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_execution_id` (`execution_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_node_key` (`node_key`),
  CONSTRAINT `fk_node_execution_history` FOREIGN KEY (`execution_id`) REFERENCES `collection_execution_history` (`execution_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节点执行历史表';

-- 7. 变量定义表
CREATE TABLE `flow_variable_definition` (
  `variable_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '变量ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `variable_name` varchar(50) NOT NULL COMMENT '变量名称',
  `variable_type` varchar(20) NOT NULL COMMENT '变量类型：STRING、NUMBER、BOOLEAN、OBJECT、ARRAY',
  `default_value` text COMMENT '默认值',
  `description` varchar(200) DEFAULT NULL COMMENT '变量描述',
  `is_global` tinyint(1) DEFAULT '0' COMMENT '是否全局变量：0-否，1-是',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`variable_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_variable_name` (`variable_name`),
  CONSTRAINT `fk_flow_variable_task` FOREIGN KEY (`task_id`) REFERENCES `data_collection_task` (`task_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程变量定义表';

-- 插入一些示例数据源配置
INSERT INTO `data_source_config` (`source_name`, `source_type`, `connection_config`, `test_sql`, `create_by`, `remark`) VALUES
('MySQL测试库', 'MYSQL', '{"host":"localhost","port":3306,"database":"test_db","username":"root","password":"123456","charset":"utf8mb4"}', 'SELECT 1', 'admin', 'MySQL测试数据源'),
('SQLServer测试库', 'SQLSERVER', '{"host":"localhost","port":1433,"database":"test_db","username":"sa","password":"123456"}', 'SELECT 1', 'admin', 'SQLServer测试数据源'),
('API数据源', 'API', '{"baseUrl":"http://api.example.com","timeout":30000,"headers":{"Content-Type":"application/json"}}', NULL, 'admin', 'API接口数据源');
