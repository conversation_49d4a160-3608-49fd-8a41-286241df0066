package com.lacus.service.datacollection.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lacus.dao.datacollection.mapper.DataCollectionCategoryMapper;
import com.lacus.dao.datacollection.entity.DataCollectionCategoryEntity;
import com.lacus.dao.datacollection.vo.DataCollectionCategoryPageQuery;
import com.lacus.dao.datacollection.vo.DataCollectionCategoryTreeVO;
import com.lacus.dao.datacollection.vo.DataCollectionCategoryVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据采集分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class DataCollectionCategoryServiceImpl implements IDataCollectionCategoryService {

    @Autowired
    private DataCollectionCategoryMapper categoryMapper;

    @Override
    public IPage<DataCollectionCategoryVO> selectCategoryPage(DataCollectionCategoryPageQuery pageQuery) {
        Page<DataCollectionCategoryEntity> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        
        IPage<DataCollectionCategoryEntity> entityPage = categoryMapper.selectCategoryPage(
                page,
                pageQuery.getCategoryName(),
                pageQuery.getCategoryCode(),
                pageQuery.getParentId(),
                pageQuery.getStatus(),
                pageQuery.getCreateTimeStart(),
                pageQuery.getCreateTimeEnd()
        );
        
        // 转换为VO
        IPage<DataCollectionCategoryVO> voPage = new Page<>();
        BeanUtils.copyProperties(entityPage, voPage);
        
        List<DataCollectionCategoryVO> voList = entityPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        voPage.setRecords(voList);
        
        return voPage;
    }

    @Override
    public DataCollectionCategoryVO selectCategoryById(Long categoryId) {
        DataCollectionCategoryEntity entity = categoryMapper.selectById(categoryId);
        if (entity == null) {
            return null;
        }
        
        DataCollectionCategoryVO vo = convertToVO(entity);
        // 设置任务数量
        vo.setTaskCount(getTaskCountByCategory(categoryId));
        // 设置是否有子分类
        vo.setHasChildren(hasChildCategory(categoryId));
        // 设置是否可以删除
        vo.setCanDelete(canDeleteCategory(categoryId));
        
        return vo;
    }

    @Override
    public List<DataCollectionCategoryTreeVO> selectCategoryTree(Boolean onlyEnabled) {
        Integer status = onlyEnabled != null && onlyEnabled ? 1 : null;
        List<DataCollectionCategoryEntity> entities = categoryMapper.selectCategoryTree(status);
        
        // 转换为TreeVO并构建树形结构
        List<DataCollectionCategoryTreeVO> treeVOList = entities.stream()
                .map(this::convertToTreeVO)
                .collect(Collectors.toList());
        
        return buildTree(treeVOList, 0L);
    }

    @Override
    public List<DataCollectionCategoryVO> selectCategoryByParentId(Long parentId, Boolean onlyEnabled) {
        Integer status = onlyEnabled != null && onlyEnabled ? 1 : null;
        List<DataCollectionCategoryEntity> entities = categoryMapper.selectCategoryByParentId(parentId, status);
        
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean insertCategory(DataCollectionCategoryVO categoryVO) {
        DataCollectionCategoryEntity entity = convertToEntity(categoryVO);
        
        // 设置默认值
        if (entity.getParentId() == null) {
            entity.setParentId(0L);
        }
        if (entity.getSortOrder() == null) {
            entity.setSortOrder(0);
        }
        if (entity.getStatus() == null) {
            entity.setStatus(1);
        }
        if (!StringUtils.hasText(entity.getCategoryIcon())) {
            entity.setCategoryIcon("folder");
        }
        
        // 设置分类层级
        entity.setCategoryLevel(getCategoryLevel(entity.getParentId()));
        entity.setCreateTime(new Date());
        
        // 插入分类
        int result = categoryMapper.insert(entity);
        
        // 构建并更新分类路径
        if (result > 0) {
            updateCategoryPath(entity.getCategoryId());
        }
        
        return result > 0;
    }

    @Override
    @Transactional
    public boolean updateCategory(DataCollectionCategoryVO categoryVO) {
        DataCollectionCategoryEntity entity = convertToEntity(categoryVO);
        
        // 如果修改了父级分类，需要更新层级和路径
        DataCollectionCategoryEntity oldEntity = categoryMapper.selectById(entity.getCategoryId());
        if (oldEntity != null && !Objects.equals(oldEntity.getParentId(), entity.getParentId())) {
            entity.setCategoryLevel(getCategoryLevel(entity.getParentId()));
        }
        
        entity.setUpdateTime(new Date());
        int result = categoryMapper.updateById(entity);
        
        // 更新分类路径
        if (result > 0) {
            updateCategoryPath(entity.getCategoryId());
            // 更新所有子分类的路径
            updateChildCategoryPaths(entity.getCategoryId());
        }
        
        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteCategoryByIds(List<Long> categoryIds) {
        // 检查是否可以删除
        for (Long categoryId : categoryIds) {
            if (!canDeleteCategory(categoryId)) {
                throw new RuntimeException("分类ID为 " + categoryId + " 的分类不能删除，存在子分类或关联任务");
            }
        }
        
        return categoryMapper.batchDeleteCategory(categoryIds) > 0;
    }

    @Override
    @Transactional
    public boolean deleteCategoryById(Long categoryId) {
        if (!canDeleteCategory(categoryId)) {
            throw new RuntimeException("该分类不能删除，存在子分类或关联任务");
        }
        
        return categoryMapper.deleteById(categoryId) > 0;
    }

    @Override
    public boolean checkCategoryCodeUnique(String categoryCode, Long categoryId) {
        DataCollectionCategoryEntity entity = categoryMapper.checkCategoryCodeUnique(categoryCode, categoryId);
        return entity == null;
    }

    @Override
    public boolean checkCategoryNameUnique(String categoryName, Long parentId, Long categoryId) {
        DataCollectionCategoryEntity entity = categoryMapper.checkCategoryNameUnique(categoryName, parentId, categoryId);
        return entity == null;
    }

    @Override
    public boolean hasChildCategory(Long categoryId) {
        return categoryMapper.hasChildCategory(categoryId) > 0;
    }

    @Override
    public boolean hasTask(Long categoryId) {
        // TODO: 需要实现任务数量查询
        return false;
    }

    @Override
    @Transactional
    public boolean moveCategory(Long categoryId, Long targetParentId) {
        DataCollectionCategoryEntity entity = new DataCollectionCategoryEntity();
        entity.setCategoryId(categoryId);
        entity.setParentId(targetParentId);
        entity.setCategoryLevel(getCategoryLevel(targetParentId));
        entity.setUpdateTime(new Date());
        
        int result = categoryMapper.updateById(entity);
        
        // 更新分类路径
        if (result > 0) {
            updateCategoryPath(categoryId);
            // 更新所有子分类的路径
            updateChildCategoryPaths(categoryId);
        }
        
        return result > 0;
    }

    @Override
    public Map<String, Object> getCategoryStats(Long categoryId) {
        Map<String, Object> stats = new HashMap<>();
        
        // 获取分类信息
        DataCollectionCategoryVO category = selectCategoryById(categoryId);
        if (category != null) {
            stats.put("categoryInfo", category);
            
            // 获取子分类数量
            int childCount = categoryMapper.hasChildCategory(categoryId);
            stats.put("childCategoryCount", childCount);
            
            // 获取任务数量
            int taskCount = getTaskCountByCategory(categoryId);
            stats.put("taskCount", taskCount);
            
            // 获取所有子分类的任务数量
            List<Long> childCategoryIds = getChildCategoryIds(categoryId);
            int totalTaskCount = taskCount;
            for (Long childId : childCategoryIds) {
                totalTaskCount += getTaskCountByCategory(childId);
            }
            stats.put("totalTaskCount", totalTaskCount);
        }
        
        return stats;
    }

    @Override
    @Transactional
    public boolean batchUpdateStatus(List<Long> categoryIds, Integer status) {
        return categoryMapper.batchUpdateStatus(categoryIds, status) > 0;
    }

    @Override
    public String buildCategoryPath(Long categoryId) {
        if (categoryId == null || categoryId == 0) {
            return "/";
        }
        
        List<String> pathParts = new ArrayList<>();
        DataCollectionCategoryEntity category = categoryMapper.selectById(categoryId);
        
        while (category != null && category.getParentId() != null && category.getParentId() != 0) {
            pathParts.add(0, category.getCategoryCode());
            category = categoryMapper.selectById(category.getParentId());
        }
        
        if (category != null) {
            pathParts.add(0, category.getCategoryCode());
        }
        
        return "/" + String.join("/", pathParts);
    }

    @Override
    public boolean updateCategoryPath(Long categoryId) {
        String categoryPath = buildCategoryPath(categoryId);
        return categoryMapper.updateCategoryPath(categoryId, categoryPath) > 0;
    }

    @Override
    public int getCategoryLevel(Long parentId) {
        if (parentId == null || parentId == 0) {
            return 1;
        }
        
        DataCollectionCategoryEntity parent = categoryMapper.selectById(parentId);
        if (parent != null) {
            return parent.getCategoryLevel() + 1;
        }
        
        return 1;
    }

    @Override
    public List<Long> getChildCategoryIds(Long categoryId) {
        return categoryMapper.selectDescendantCategoryIds(categoryId);
    }

    @Override
    public DataCollectionCategoryVO selectCategoryByPath(String categoryPath) {
        DataCollectionCategoryEntity entity = categoryMapper.selectCategoryByPath(categoryPath);
        return entity != null ? convertToVO(entity) : null;
    }

    @Override
    public String getCategoryFullName(Long categoryId) {
        return categoryMapper.selectCategoryFullName(categoryId);
    }

    @Override
    public boolean canDeleteCategory(Long categoryId) {
        // 检查是否有子分类
        if (hasChildCategory(categoryId)) {
            return false;
        }
        
        // 检查是否有关联任务
        if (hasTask(categoryId)) {
            return false;
        }
        
        return true;
    }

    @Override
    public int getTaskCountByCategory(Long categoryId) {
        // TODO: 需要实现任务数量查询
        return 0;
    }

    @Override
    public List<DataCollectionCategoryVO> selectEnabledCategories() {
        LambdaQueryWrapper<DataCollectionCategoryEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DataCollectionCategoryEntity::getStatus, 1);
        wrapper.orderByAsc(DataCollectionCategoryEntity::getSortOrder);

        List<DataCollectionCategoryEntity> entities = categoryMapper.selectList(wrapper);
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public DataCollectionCategoryVO selectCategoryByCode(String categoryCode) {
        DataCollectionCategoryEntity entity = categoryMapper.selectCategoryByCode(categoryCode);
        return entity != null ? convertToVO(entity) : null;
    }

    @Override
    public List<DataCollectionCategoryVO> searchCategoryByName(String categoryName, Boolean onlyEnabled) {
        Integer status = onlyEnabled != null && onlyEnabled ? 1 : null;
        List<DataCollectionCategoryEntity> entities = categoryMapper.selectCategoryByNameLike(categoryName, status);
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getCategoryLevelStats() {
        return categoryMapper.selectCategoryLevelStats();
    }

    @Override
    public List<Map<String, Object>> getCategoryStatusStats() {
        return categoryMapper.selectCategoryStatusStats();
    }

    @Override
    public List<Map<String, Object>> getCategoryUsageStats() {
        return categoryMapper.selectCategoryUsageStats();
    }

    @Override
    public List<DataCollectionCategoryVO> selectHotCategories(Integer limit) {
        List<DataCollectionCategoryEntity> entities = categoryMapper.selectHotCategories(limit);
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DataCollectionCategoryVO> selectEmptyCategories() {
        List<DataCollectionCategoryEntity> entities = categoryMapper.selectEmptyCategories();
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public boolean checkCircularReference(Long categoryId, Long parentId) {
        return categoryMapper.checkCircularReference(categoryId, parentId);
    }

    // ==================== 私有方法 ====================

    /**
     * 实体转VO
     */
    private DataCollectionCategoryVO convertToVO(DataCollectionCategoryEntity entity) {
        DataCollectionCategoryVO vo = new DataCollectionCategoryVO();
        BeanUtils.copyProperties(entity, vo);

        // 设置状态文本
        vo.setStatusText(entity.getStatus() == 1 ? "启用" : "禁用");

        // 设置层级文本
        vo.setLevelText("第" + entity.getCategoryLevel() + "级");

        return vo;
    }

    /**
     * VO转实体
     */
    private DataCollectionCategoryEntity convertToEntity(DataCollectionCategoryVO vo) {
        DataCollectionCategoryEntity entity = new DataCollectionCategoryEntity();
        BeanUtils.copyProperties(vo, entity);
        return entity;
    }

    /**
     * 实体转TreeVO
     */
    private DataCollectionCategoryTreeVO convertToTreeVO(DataCollectionCategoryEntity entity) {
        DataCollectionCategoryTreeVO treeVO = new DataCollectionCategoryTreeVO();
        BeanUtils.copyProperties(entity, treeVO);

        // 设置树形节点属性
        treeVO.setLabel(entity.getCategoryName());
        treeVO.setValue(entity.getCategoryId().toString());
        treeVO.setKey(entity.getCategoryId().toString());
        treeVO.setHasChildren(hasChildCategory(entity.getCategoryId()));
        treeVO.setTaskCount(getTaskCountByCategory(entity.getCategoryId()));

        return treeVO;
    }

    /**
     * 构建树形结构
     */
    private List<DataCollectionCategoryTreeVO> buildTree(List<DataCollectionCategoryTreeVO> categories, Long parentId) {
        List<DataCollectionCategoryTreeVO> tree = new ArrayList<>();

        for (DataCollectionCategoryTreeVO category : categories) {
            if (Objects.equals(category.getParentId(), parentId)) {
                List<DataCollectionCategoryTreeVO> children = buildTree(categories, category.getCategoryId());
                category.setChildren(children);
                tree.add(category);
            }
        }

        // 按排序字段排序
        tree.sort(Comparator.comparing(DataCollectionCategoryTreeVO::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())));

        return tree;
    }

    /**
     * 更新子分类路径
     */
    private void updateChildCategoryPaths(Long parentId) {
        List<DataCollectionCategoryEntity> children = categoryMapper.selectCategoryByParentId(parentId, null);
        for (DataCollectionCategoryEntity child : children) {
            updateCategoryPath(child.getCategoryId());
            updateChildCategoryPaths(child.getCategoryId());
        }
    }
}
