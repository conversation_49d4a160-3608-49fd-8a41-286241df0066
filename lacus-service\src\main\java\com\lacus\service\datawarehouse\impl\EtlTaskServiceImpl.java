package com.lacus.service.datawarehouse.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lacus.common.core.page.PageDTO;
import com.lacus.dao.datawarehouse.entity.*;
import com.lacus.dao.datawarehouse.mapper.*;
import com.lacus.service.datawarehouse.EtlFieldMappingService;
import com.lacus.service.datawarehouse.EtlQualityRulesService;
import com.lacus.service.datawarehouse.EtlSourceTableService;
import com.lacus.service.datawarehouse.EtlTaskService;
import com.lacus.service.datawarehouse.command.EtlTaskAddCommand;
import com.lacus.service.datawarehouse.dto.*;
import com.lacus.service.datawarehouse.model.EtlTaskModel;
import com.lacus.service.datawarehouse.query.EtlExecutionHistoryQuery;
import com.lacus.service.datawarehouse.query.EtlTaskQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ETL任务服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EtlTaskServiceImpl extends ServiceImpl<EtlTaskMapper, EtlTaskEntity> implements EtlTaskService {

    @Autowired
    private EtlTaskMapper etlTaskMapper;

    @Autowired
    private EtlFieldMappingMapper etlFieldMappingMapper;

    @Autowired
    private EtlQualityRulesMapper etlQualityRulesMapper;

    @Autowired
    private EtlSourceTableMapper etlSourceTableMapper;

    @Autowired
    private EtlExecutionHistoryMapper executionHistoryMapper;

    @Autowired
    private EtlSourceTableService etlSourceTableService;

    @Autowired
    private EtlQualityRulesService etlQualityRulesService;

    @Autowired
    private EtlFieldMappingService etlFieldMappingService;

    @Override
    public PageDTO queryEtlTaskDtoList(EtlTaskQuery query) {
        Page<EtlTaskDTO> page = etlTaskMapper.selectPage(query.toPage(), query.toQueryWrapper());
        return new PageDTO(page.getRecords(), page.getTotal());
    }

    @Override
    public EtlTaskDTO queryEtlTaskDtoById(Long taskId) {
        EtlTaskEntity entity = etlTaskMapper.selectById(taskId);
        if (entity == null) {
            throw new RuntimeException("ETL任务不存在");
        }
        return convertToDTO(entity);
    }

    @Override
    public EtlTaskDTO queryEtlTaskById(Long taskId) {
        EtlTaskEntity entity = etlTaskMapper.selectById(taskId);
        if (entity == null) {
            throw new RuntimeException("ETL任务不存在");
        }
        EtlTaskDTO dto =convertToDTO(entity);
        //通过taskId查询出关联的数据源表、字段映射、质量规则等信息
        dto.setSourceTables(etlSourceTableMapper.selectList(new QueryWrapper<EtlSourceTableEntity>().eq("etl_task_id", taskId)));
        dto.setQualityRules(etlQualityRulesMapper.selectList(new QueryWrapper<EtlQualityRulesEntity>().eq("etl_task_id", taskId)));
        dto.setFieldMappings(etlFieldMappingMapper.selectList(new QueryWrapper<EtlFieldMappingEntity>().eq("etl_task_id", taskId)));
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveEtlTask(EtlTaskAddCommand command) {
        // 检查任务名称是否重复
        if (checkTaskNameExists(command.getTaskName(), null)) {
            throw new RuntimeException("任务名称已存在");
        }

        // 验证配置
        validateEtlTaskConfig(command);

        EtlTaskEntity entity = new EtlTaskEntity();
        BeanUtils.copyProperties(command, entity);
        entity.setSourceTableCount(command.getSourceTables().size());
        etlTaskMapper.insert(entity);

        addEtlTaskData(command,entity);
        //获取插入后的任务ID
        return entity.getTaskId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEtlTask(EtlTaskAddCommand model) {
        //修改ETL任务
        // 检查任务名称是否重复
        if (checkTaskNameExists(model.getTaskName(), model.getTaskId())) {
            throw new RuntimeException("任务名称已存在");
        }
        // 验证配置
        validateEtlTaskConfig(model);

        EtlTaskEntity entity = new EtlTaskEntity();
        BeanUtils.copyProperties(model, entity);
        entity.setSourceTableCount(model.getSourceTables().size());
        etlTaskMapper.updateById(entity);

        //删除旧数据
        etlSourceTableMapper.delete(new QueryWrapper<EtlSourceTableEntity>().eq("etl_task_id", model.getTaskId()));
        etlQualityRulesMapper.delete(new QueryWrapper<EtlQualityRulesEntity>().eq("etl_task_id", model.getTaskId()));
        etlFieldMappingMapper.delete(new QueryWrapper<EtlFieldMappingEntity>().eq("etl_task_id", model.getTaskId()));

        addEtlTaskData(model,entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteEtlTask(List<Long> taskIds) {
        etlTaskMapper.deleteBatchIds(taskIds);
        //删除对应配置
        for (Long taskId : taskIds){
            etlSourceTableMapper.delete(new QueryWrapper<EtlSourceTableEntity>().eq("etl_task_id",taskId));
            etlQualityRulesMapper.delete(new QueryWrapper<EtlQualityRulesEntity>().eq("etl_task_id",taskId));
            etlFieldMappingMapper.delete(new QueryWrapper<EtlFieldMappingEntity>().eq("etl_task_id",taskId));
        }
    }

    @Override
    public boolean checkTaskNameExists(String taskName, Long excludeTaskId) {
        EtlTaskEntity entity = etlTaskMapper.selectByTaskName(taskName, excludeTaskId);
        return entity != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEtlTaskStatus(Long taskId, Integer status) {
        EtlTaskEntity entity = new EtlTaskEntity();
        entity.setTaskId(taskId);
        entity.setStatus(status);
        entity.setUpdateTime(new Date());
        etlTaskMapper.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String runEtlTask(Long taskId) {
        EtlTaskEntity entity = etlTaskMapper.selectById(taskId);
        if (entity == null) {
            throw new RuntimeException("ETL任务不存在");
        }
        
        if (entity.getStatus() != 1) {
            throw new RuntimeException("ETL任务未启用");
        }
        
        // 生成执行ID
        String executionId = UUID.randomUUID().toString();
        
        // 创建执行历史记录
        EtlExecutionHistoryEntity historyEntity = new EtlExecutionHistoryEntity();
        historyEntity.setExecutionId(executionId);
        historyEntity.setTaskId(taskId);
        historyEntity.setTaskName(entity.getTaskName());
        historyEntity.setStatus("RUNNING");
        historyEntity.setStartTime(new Date());
        historyEntity.setCreateTime(new Date());
        executionHistoryMapper.insert(historyEntity);
        
        // 更新任务最后执行时间和状态
        etlTaskMapper.updateExecutionInfo(taskId, new Date(), "RUNNING");
        
        log.info("启动ETL任务执行，任务ID: {}, 执行ID: {}", taskId, executionId);
        
        // 这里应该调用实际的ETL执行引擎
        // 简化实现：异步模拟执行
        simulateEtlExecution(executionId, taskId);
        
        return executionId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopEtlTask(Long taskId) {
        // 这里应该停止正在运行的ETL任务
        log.info("停止ETL任务，任务ID: {}", taskId);
        
        // 更新任务状态
        etlTaskMapper.updateExecutionInfo(taskId, new Date(), "STOPPED");
    }

//    @Override
//    public PageDTO getExecutionHistory(EtlExecutionHistoryQuery query) {
//        Map<String, Object> params = buildHistoryQueryParams(query);
//        Page<EtlExecutionHistoryEntity> page = new Page<>(query.getPageNum(), query.getPageSize());
//        IPage<EtlExecutionHistoryEntity> result = executionHistoryMapper.selectExecutionHistoryPage(page, params);
//
//        List<EtlExecutionHistoryDTO> dtoList = result.getRecords().stream()
//                .map(this::convertToHistoryDTO)
//                .collect(Collectors.toList());
//
//        return PageDTO.build(dtoList, result.getTotal());
//    }

    @Override
    public Object previewEtlResult(Object command) {
        // 这里应该实现ETL结果预览逻辑
        // 简化实现：返回模拟数据
        Map<String, Object> result = new HashMap<>();
        result.put("previewData", "模拟预览数据");
        result.put("recordCount", 100);
        result.put("previewTime", LocalDateTime.now());
        return result;
    }

    @Override
    public Object getFieldMappingSuggestions(String sourceLayer, String targetLayer, List<String> sourceTables) {
        // 这里应该实现字段映射建议逻辑
        // 简化实现：返回模拟建议
        Map<String, Object> suggestions = new HashMap<>();
        suggestions.put("sourceLayer", sourceLayer);
        suggestions.put("targetLayer", targetLayer);
        suggestions.put("sourceTables", sourceTables);
        suggestions.put("mappingSuggestions", "模拟映射建议");
        return suggestions;
    }

    @Override
    public EtlTaskStatsDTO getEtlTaskStats() {
        Map<String, Object> stats = etlTaskMapper.selectEtlTaskStats();
        EtlTaskStatsDTO dto = new EtlTaskStatsDTO();
        
        dto.setTotalTasks(getLongValue(stats, "totalTasks"));
        dto.setEnabledTasks(getLongValue(stats, "enabledTasks"));
        dto.setDisabledTasks(getLongValue(stats, "disabledTasks"));
        dto.setRunningTasks(getLongValue(stats, "runningTasks"));
        dto.setTodaySuccessTasks(getLongValue(stats, "todaySuccessTasks"));
        dto.setTodayFailedTasks(getLongValue(stats, "todayFailedTasks"));
        dto.setOdsLayerTasks(getLongValue(stats, "odsLayerTasks"));
        dto.setDwdLayerTasks(getLongValue(stats, "dwdLayerTasks"));
        dto.setDwsLayerTasks(getLongValue(stats, "dwsLayerTasks"));
        dto.setAdsLayerTasks(getLongValue(stats, "adsLayerTasks"));
        
        // 获取今日执行统计
        Map<String, Object> todayStats = executionHistoryMapper.selectTodayStats();
        dto.setTodayExecutions(getLongValue(todayStats, "todayExecutions"));
        
        return dto;
    }

    @Override
    public List<EtlExecutionHistoryEntity> getExecutionHistory(String taskId) {
        return executionHistoryMapper.selectList(new QueryWrapper<EtlExecutionHistoryEntity>()
                .eq("task_id", taskId)
                .orderByDesc("create_time"));
    }

    @Override
    public List<EtlTaskModel> queryTasksByStatus(Integer status) {
        List<EtlTaskEntity> entities = etlTaskMapper.selectByStatus(status);
        return entities.stream()
                .map(this::convertToModel)
                .collect(Collectors.toList());
    }

    @Override
    public List<EtlTaskModel> queryTasksByScheduleType(String scheduleType) {
        List<EtlTaskEntity> entities = etlTaskMapper.selectByScheduleType(scheduleType);
        return entities.stream()
                .map(this::convertToModel)
                .collect(Collectors.toList());
    }

    /**
     * 模拟ETL执行
     */
    private void simulateEtlExecution(String executionId, Long taskId) {
        // 异步执行模拟
        new Thread(() -> {
            try {
                Thread.sleep(5000); // 模拟执行时间
                
                // 更新执行历史
                EtlExecutionHistoryEntity historyEntity = new EtlExecutionHistoryEntity();
                historyEntity.setExecutionId(executionId);
                historyEntity.setStatus("SUCCESS");
                historyEntity.setEndTime(new Date());
                historyEntity.setDuration(5000L);
                historyEntity.setProcessedRecords(1000L);
                historyEntity.setErrorRecords(0L);
                executionHistoryMapper.updateById(historyEntity);
                
                // 更新任务状态
                etlTaskMapper.updateExecutionInfo(taskId, new Date(), "SUCCESS");
                
                log.info("ETL任务执行完成，执行ID: {}", executionId);
            } catch (Exception e) {
                log.error("ETL任务执行失败，执行ID: {}", executionId, e);
                
                // 更新为失败状态
                EtlExecutionHistoryEntity historyEntity = new EtlExecutionHistoryEntity();
                historyEntity.setExecutionId(executionId);
                historyEntity.setStatus("FAILED");
                historyEntity.setEndTime(new Date());
                historyEntity.setErrorMessage(e.getMessage());
                executionHistoryMapper.updateById(historyEntity);
                
                etlTaskMapper.updateExecutionInfo(taskId, new Date(), "FAILED");
            }
        }).start();
    }

    /**
     * 构建查询参数
     */
    private Map<String, Object> buildQueryParams(EtlTaskQuery query) {
        Map<String, Object> params = new HashMap<>();
        params.put("taskName", query.getTaskName());
        params.put("sourceLayer", query.getSourceLayer());
        params.put("targetLayer", query.getTargetLayer());
        params.put("status", query.getStatus());
        params.put("scheduleType", query.getScheduleType());
        params.put("createBy", query.getCreateBy());
        params.put("beginTime", query.getBeginTime());
        params.put("endTime", query.getEndTime());
        return params;
    }

    /**
     * 构建历史查询参数
     */
    private Map<String, Object> buildHistoryQueryParams(EtlExecutionHistoryQuery query) {
        Map<String, Object> params = new HashMap<>();
        params.put("taskId", query.getTaskId());
        params.put("status", query.getStatus());
        params.put("taskName", query.getTaskName());
        params.put("beginTime", query.getBeginTime());
        params.put("endTime", query.getEndTime());
        return params;
    }

    /**
     * 实体转DTO
     */
    private EtlTaskDTO convertToDTO(EtlTaskEntity entity) {
        EtlTaskDTO dto = new EtlTaskDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 实体转模型
     */
    private EtlTaskModel convertToModel(EtlTaskEntity entity) {
        EtlTaskModel model = new EtlTaskModel();
        BeanUtils.copyProperties(entity, model);
        return model;
    }

    /**
     * 模型转实体
     */
    private EtlTaskEntity convertToEntity(EtlTaskModel model) {
        EtlTaskEntity entity = new EtlTaskEntity();
        BeanUtils.copyProperties(model, entity);
        return entity;
    }

    /**
     * 执行历史实体转DTO
     */
    private EtlExecutionHistoryDTO convertToHistoryDTO(EtlExecutionHistoryEntity entity) {
        EtlExecutionHistoryDTO dto = new EtlExecutionHistoryDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 安全获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return 0L;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return 0L;
    }

    /**
     * 验证ETL任务配置
     */
    private void validateEtlTaskConfig(Object command) {
        // 这里可以添加具体的配置验证逻辑
        // 例如：验证Cron表达式、验证字段映射配置等
        log.debug("验证ETL任务配置");
    }

    /**
     * 添加ETL任务数据源配置、字段映射配置、数据质量配置
     */
    private void addEtlTaskData(EtlTaskAddCommand command,EtlTaskEntity entity) {
        // 这里可以添加具体的配置验证逻辑
        // 例如：验证Cron表达式、验证字段映射配置等
        // 插入ETL任务数据源表信息
        List<EtlSourceTableEntity> sourceTables = command.getSourceTables().stream()
                .map(table -> {
                    table.setEtlTaskId(entity.getTaskId());
                    table.setId(null);
                    EtlSourceTableEntity sourceTableEntity = new EtlSourceTableEntity();
                    BeanUtils.copyProperties(table,sourceTableEntity);
                    return sourceTableEntity;
                }).collect(Collectors.toList());
        // 批量插入数据源表
        etlSourceTableService.saveBatch(sourceTables);

        // 插入ETL任务质量规则信息
        List<EtlQualityRulesEntity> qualityRules = command.getQualityRules().stream()
                .map(rule -> {
                    rule.setEtlTaskId(entity.getTaskId());
                    rule.setId(null);
                    EtlQualityRulesEntity qualityRulesEntity = new EtlQualityRulesEntity();
                    BeanUtils.copyProperties(rule,qualityRulesEntity);
                    return qualityRulesEntity;
                }).collect(Collectors.toList());
        // 批量插入质量规则
        etlQualityRulesService.saveBatch(qualityRules);

        // 插入ETL任务字段映射信息
        List<EtlFieldMappingEntity> fieldMappings = command.getFieldMappings().stream()
                .map(mapping -> {
                    mapping.setEtlTaskId(entity.getTaskId());
                    mapping.setId(null);
                    EtlFieldMappingEntity mappingEntity = new EtlFieldMappingEntity();
                    BeanUtils.copyProperties(mapping,mappingEntity);
                    return mappingEntity;
                }).collect(Collectors.toList());
        // 批量插入字段映射
        etlFieldMappingService.saveBatch(fieldMappings);
    }
}
