package com.lacus.datacollection.service.impl;

import com.lacus.common.core.text.Convert;
import com.lacus.common.utils.DateUtils;
import com.lacus.common.utils.StringUtils;
import com.lacus.datacollection.domain.DataCollectionCategory;
import com.lacus.datacollection.mapper.DataCollectionCategoryMapper;
import com.lacus.datacollection.mapper.DataCollectionTaskMapper;
import com.lacus.datacollection.service.IDataCollectionCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据采集分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class DataCollectionCategoryServiceImpl implements IDataCollectionCategoryService 
{
    @Autowired
    private DataCollectionCategoryMapper dataCollectionCategoryMapper;

    @Autowired
    private DataCollectionTaskMapper dataCollectionTaskMapper;

    /**
     * 查询数据采集分类
     * 
     * @param categoryId 数据采集分类主键
     * @return 数据采集分类
     */
    @Override
    public DataCollectionCategory selectDataCollectionCategoryByCategoryId(Long categoryId)
    {
        DataCollectionCategory category = dataCollectionCategoryMapper.selectDataCollectionCategoryByCategoryId(categoryId);
        if (category != null) {
            // 设置任务数量
            category.setTaskCount(getTaskCountByCategory(categoryId));
        }
        return category;
    }

    /**
     * 查询数据采集分类列表
     * 
     * @param dataCollectionCategory 数据采集分类
     * @return 数据采集分类
     */
    @Override
    public List<DataCollectionCategory> selectDataCollectionCategoryList(DataCollectionCategory dataCollectionCategory)
    {
        List<DataCollectionCategory> list = dataCollectionCategoryMapper.selectDataCollectionCategoryList(dataCollectionCategory);
        // 为每个分类设置任务数量
        for (DataCollectionCategory category : list) {
            category.setTaskCount(getTaskCountByCategory(category.getCategoryId()));
        }
        return list;
    }

    /**
     * 查询分类树形结构
     * 
     * @return 分类树
     */
    @Override
    public List<DataCollectionCategory> selectCategoryTree()
    {
        List<DataCollectionCategory> allCategories = dataCollectionCategoryMapper.selectDataCollectionCategoryList(new DataCollectionCategory());
        
        // 为每个分类设置任务数量
        for (DataCollectionCategory category : allCategories) {
            category.setTaskCount(getTaskCountByCategory(category.getCategoryId()));
        }
        
        return buildTree(allCategories, 0L);
    }

    /**
     * 根据父级ID查询子分类列表
     * 
     * @param parentId 父级ID
     * @return 子分类列表
     */
    @Override
    public List<DataCollectionCategory> selectCategoryByParentId(Long parentId)
    {
        DataCollectionCategory query = new DataCollectionCategory();
        query.setParentId(parentId);
        return dataCollectionCategoryMapper.selectDataCollectionCategoryList(query);
    }

    /**
     * 新增数据采集分类
     * 
     * @param dataCollectionCategory 数据采集分类
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDataCollectionCategory(DataCollectionCategory dataCollectionCategory)
    {
        // 设置创建时间
        dataCollectionCategory.setCreateTime(DateUtils.getNowDate());
        
        // 设置分类层级
        dataCollectionCategory.setCategoryLevel(getCategoryLevel(dataCollectionCategory.getParentId()));
        
        // 插入分类
        int result = dataCollectionCategoryMapper.insertDataCollectionCategory(dataCollectionCategory);
        
        // 构建并更新分类路径
        if (result > 0) {
            updateCategoryPath(dataCollectionCategory.getCategoryId());
        }
        
        return result;
    }

    /**
     * 修改数据采集分类
     * 
     * @param dataCollectionCategory 数据采集分类
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDataCollectionCategory(DataCollectionCategory dataCollectionCategory)
    {
        dataCollectionCategory.setUpdateTime(DateUtils.getNowDate());
        
        // 如果修改了父级分类，需要更新层级和路径
        DataCollectionCategory oldCategory = dataCollectionCategoryMapper.selectDataCollectionCategoryByCategoryId(dataCollectionCategory.getCategoryId());
        if (oldCategory != null && !Objects.equals(oldCategory.getParentId(), dataCollectionCategory.getParentId())) {
            dataCollectionCategory.setCategoryLevel(getCategoryLevel(dataCollectionCategory.getParentId()));
        }
        
        int result = dataCollectionCategoryMapper.updateDataCollectionCategory(dataCollectionCategory);
        
        // 更新分类路径
        if (result > 0) {
            updateCategoryPath(dataCollectionCategory.getCategoryId());
            // 更新所有子分类的路径
            updateChildCategoryPaths(dataCollectionCategory.getCategoryId());
        }
        
        return result;
    }

    /**
     * 批量删除数据采集分类
     * 
     * @param categoryIds 需要删除的数据采集分类主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDataCollectionCategoryByCategoryIds(Long[] categoryIds)
    {
        // 检查是否可以删除
        for (Long categoryId : categoryIds) {
            if (!canDeleteCategory(categoryId)) {
                throw new RuntimeException("分类ID为 " + categoryId + " 的分类不能删除，存在子分类或关联任务");
            }
        }
        
        return dataCollectionCategoryMapper.deleteDataCollectionCategoryByCategoryIds(categoryIds);
    }

    /**
     * 删除数据采集分类信息
     * 
     * @param categoryId 数据采集分类主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDataCollectionCategoryByCategoryId(Long categoryId)
    {
        if (!canDeleteCategory(categoryId)) {
            throw new RuntimeException("该分类不能删除，存在子分类或关联任务");
        }
        
        return dataCollectionCategoryMapper.deleteDataCollectionCategoryByCategoryId(categoryId);
    }

    /**
     * 检查分类编码是否唯一
     * 
     * @param dataCollectionCategory 分类信息
     * @return 结果
     */
    @Override
    public boolean checkCategoryCodeUnique(DataCollectionCategory dataCollectionCategory)
    {
        Long categoryId = StringUtils.isNull(dataCollectionCategory.getCategoryId()) ? -1L : dataCollectionCategory.getCategoryId();
        DataCollectionCategory info = dataCollectionCategoryMapper.checkCategoryCodeUnique(dataCollectionCategory.getCategoryCode());
        if (StringUtils.isNotNull(info) && info.getCategoryId().longValue() != categoryId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 检查分类名称是否唯一
     * 
     * @param dataCollectionCategory 分类信息
     * @return 结果
     */
    @Override
    public boolean checkCategoryNameUnique(DataCollectionCategory dataCollectionCategory)
    {
        Long categoryId = StringUtils.isNull(dataCollectionCategory.getCategoryId()) ? -1L : dataCollectionCategory.getCategoryId();
        DataCollectionCategory info = dataCollectionCategoryMapper.checkCategoryNameUnique(dataCollectionCategory.getCategoryName(), dataCollectionCategory.getParentId());
        if (StringUtils.isNotNull(info) && info.getCategoryId().longValue() != categoryId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 检查是否存在子分类
     * 
     * @param categoryId 分类ID
     * @return 结果
     */
    @Override
    public boolean hasChildCategory(Long categoryId)
    {
        int result = dataCollectionCategoryMapper.hasChildCategory(categoryId);
        return result > 0;
    }

    /**
     * 检查分类下是否存在任务
     * 
     * @param categoryId 分类ID
     * @return 结果
     */
    @Override
    public boolean hasTask(Long categoryId)
    {
        int result = dataCollectionTaskMapper.selectTaskCountByCategory(categoryId);
        return result > 0;
    }

    /**
     * 移动分类
     * 
     * @param dataCollectionCategory 分类信息
     * @return 结果
     */
    @Override
    @Transactional
    public int moveCategory(DataCollectionCategory dataCollectionCategory)
    {
        // 更新分类的父级ID
        dataCollectionCategory.setCategoryLevel(getCategoryLevel(dataCollectionCategory.getParentId()));
        dataCollectionCategory.setUpdateTime(DateUtils.getNowDate());
        
        int result = dataCollectionCategoryMapper.updateDataCollectionCategory(dataCollectionCategory);
        
        // 更新分类路径
        if (result > 0) {
            updateCategoryPath(dataCollectionCategory.getCategoryId());
            // 更新所有子分类的路径
            updateChildCategoryPaths(dataCollectionCategory.getCategoryId());
        }
        
        return result;
    }

    /**
     * 获取分类统计信息
     * 
     * @param categoryId 分类ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getCategoryStats(Long categoryId)
    {
        Map<String, Object> stats = new HashMap<>();
        
        // 获取分类信息
        DataCollectionCategory category = selectDataCollectionCategoryByCategoryId(categoryId);
        if (category != null) {
            stats.put("categoryInfo", category);
            
            // 获取子分类数量
            int childCount = dataCollectionCategoryMapper.hasChildCategory(categoryId);
            stats.put("childCategoryCount", childCount);
            
            // 获取任务数量
            int taskCount = getTaskCountByCategory(categoryId);
            stats.put("taskCount", taskCount);
            
            // 获取所有子分类的任务数量
            List<Long> childCategoryIds = getChildCategoryIds(categoryId);
            int totalTaskCount = taskCount;
            for (Long childId : childCategoryIds) {
                totalTaskCount += getTaskCountByCategory(childId);
            }
            stats.put("totalTaskCount", totalTaskCount);
            
            // 获取任务状态统计
            Map<String, Integer> taskStatusStats = dataCollectionTaskMapper.selectTaskStatusStatsByCategory(categoryId);
            stats.put("taskStatusStats", taskStatusStats);
        }
        
        return stats;
    }

    /**
     * 批量删除分类
     * 
     * @param categoryIds 分类ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int batchDeleteCategory(Long[] categoryIds)
    {
        return deleteDataCollectionCategoryByCategoryIds(categoryIds);
    }

    /**
     * 构建分类路径
     * 
     * @param categoryId 分类ID
     * @return 分类路径
     */
    @Override
    public String buildCategoryPath(Long categoryId)
    {
        if (categoryId == null || categoryId == 0) {
            return "/";
        }
        
        List<String> pathParts = new ArrayList<>();
        DataCollectionCategory category = dataCollectionCategoryMapper.selectDataCollectionCategoryByCategoryId(categoryId);
        
        while (category != null && category.getParentId() != null && category.getParentId() != 0) {
            pathParts.add(0, category.getCategoryCode());
            category = dataCollectionCategoryMapper.selectDataCollectionCategoryByCategoryId(category.getParentId());
        }
        
        if (category != null) {
            pathParts.add(0, category.getCategoryCode());
        }
        
        return "/" + String.join("/", pathParts);
    }

    /**
     * 更新分类路径
     *
     * @param categoryId 分类ID
     * @return 结果
     */
    @Override
    public int updateCategoryPath(Long categoryId)
    {
        String categoryPath = buildCategoryPath(categoryId);
        return dataCollectionCategoryMapper.updateCategoryPath(categoryId, categoryPath);
    }

    /**
     * 获取分类层级
     *
     * @param parentId 父级ID
     * @return 层级
     */
    @Override
    public int getCategoryLevel(Long parentId)
    {
        if (parentId == null || parentId == 0) {
            return 1;
        }

        DataCollectionCategory parent = dataCollectionCategoryMapper.selectDataCollectionCategoryByCategoryId(parentId);
        if (parent != null) {
            return parent.getCategoryLevel() + 1;
        }

        return 1;
    }

    /**
     * 获取分类的所有子分类ID
     *
     * @param categoryId 分类ID
     * @return 子分类ID列表
     */
    @Override
    public List<Long> getChildCategoryIds(Long categoryId)
    {
        List<Long> childIds = new ArrayList<>();
        List<DataCollectionCategory> children = selectCategoryByParentId(categoryId);

        for (DataCollectionCategory child : children) {
            childIds.add(child.getCategoryId());
            // 递归获取子分类的子分类
            childIds.addAll(getChildCategoryIds(child.getCategoryId()));
        }

        return childIds;
    }

    /**
     * 根据分类路径查询分类
     *
     * @param categoryPath 分类路径
     * @return 分类信息
     */
    @Override
    public DataCollectionCategory selectCategoryByPath(String categoryPath)
    {
        return dataCollectionCategoryMapper.selectCategoryByPath(categoryPath);
    }

    /**
     * 获取分类的完整路径名称
     *
     * @param categoryId 分类ID
     * @return 完整路径名称
     */
    @Override
    public String getCategoryFullName(Long categoryId)
    {
        if (categoryId == null || categoryId == 0) {
            return "";
        }

        List<String> nameList = new ArrayList<>();
        DataCollectionCategory category = dataCollectionCategoryMapper.selectDataCollectionCategoryByCategoryId(categoryId);

        while (category != null && category.getParentId() != null && category.getParentId() != 0) {
            nameList.add(0, category.getCategoryName());
            category = dataCollectionCategoryMapper.selectDataCollectionCategoryByCategoryId(category.getParentId());
        }

        if (category != null) {
            nameList.add(0, category.getCategoryName());
        }

        return String.join(" > ", nameList);
    }

    /**
     * 检查分类是否可以删除
     *
     * @param categoryId 分类ID
     * @return 结果
     */
    @Override
    public boolean canDeleteCategory(Long categoryId)
    {
        // 检查是否有子分类
        if (hasChildCategory(categoryId)) {
            return false;
        }

        // 检查是否有关联任务
        if (hasTask(categoryId)) {
            return false;
        }

        return true;
    }

    /**
     * 获取分类的任务数量统计
     *
     * @param categoryId 分类ID
     * @return 任务数量
     */
    @Override
    public int getTaskCountByCategory(Long categoryId)
    {
        return dataCollectionTaskMapper.selectTaskCountByCategory(categoryId);
    }

    /**
     * 更新分类的任务数量
     *
     * @param categoryId 分类ID
     * @return 结果
     */
    @Override
    public int updateTaskCount(Long categoryId)
    {
        int taskCount = getTaskCountByCategory(categoryId);
        return dataCollectionCategoryMapper.updateTaskCount(categoryId, taskCount);
    }

    /**
     * 获取所有启用的分类
     *
     * @return 分类列表
     */
    @Override
    public List<DataCollectionCategory> selectEnabledCategories()
    {
        DataCollectionCategory query = new DataCollectionCategory();
        query.setStatus(1);
        return dataCollectionCategoryMapper.selectDataCollectionCategoryList(query);
    }

    /**
     * 根据分类编码查询分类
     *
     * @param categoryCode 分类编码
     * @return 分类信息
     */
    @Override
    public DataCollectionCategory selectCategoryByCode(String categoryCode)
    {
        return dataCollectionCategoryMapper.selectCategoryByCode(categoryCode);
    }

    /**
     * 构建树形结构
     *
     * @param categories 分类列表
     * @param parentId 父级ID
     * @return 树形结构
     */
    private List<DataCollectionCategory> buildTree(List<DataCollectionCategory> categories, Long parentId)
    {
        List<DataCollectionCategory> tree = new ArrayList<>();

        for (DataCollectionCategory category : categories) {
            if (Objects.equals(category.getParentId(), parentId)) {
                List<DataCollectionCategory> children = buildTree(categories, category.getCategoryId());
                category.setChildren(children);
                tree.add(category);
            }
        }

        // 按排序字段排序
        tree.sort(Comparator.comparing(DataCollectionCategory::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())));

        return tree;
    }

    /**
     * 更新子分类路径
     *
     * @param parentId 父级ID
     */
    private void updateChildCategoryPaths(Long parentId)
    {
        List<DataCollectionCategory> children = selectCategoryByParentId(parentId);
        for (DataCollectionCategory child : children) {
            updateCategoryPath(child.getCategoryId());
            updateChildCategoryPaths(child.getCategoryId());
        }
    }
}
