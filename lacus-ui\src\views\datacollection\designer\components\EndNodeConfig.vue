<template>
  <div class="end-node-config">
    <el-form :model="config" label-width="100px" size="small">
      <el-form-item label="节点描述">
        <el-input 
          v-model="config.description" 
          type="textarea"
          :rows="3"
          placeholder="请输入结束节点的描述信息"
        />
      </el-form-item>

      <el-form-item label="执行结果">
        <el-select v-model="config.resultType" placeholder="请选择执行结果类型">
          <el-option label="成功完成" value="SUCCESS" />
          <el-option label="失败结束" value="FAILED" />
          <el-option label="条件结束" value="CONDITIONAL" />
        </el-select>
      </el-form-item>

      <el-form-item label="结果消息" v-if="config.resultType">
        <el-input 
          v-model="config.resultMessage" 
          placeholder="请输入结果消息"
        />
        <div class="form-tip">支持变量引用，如：处理了 ${totalRecords} 条记录</div>
      </el-form-item>

      <el-form-item label="输出变量">
        <div class="output-variables">
          <div 
            v-for="(variable, index) in config.outputVariables" 
            :key="index"
            class="variable-item"
          >
            <el-input 
              v-model="variable.name" 
              placeholder="变量名"
              style="width: 150px; margin-right: 8px;"
            />
            <el-input 
              v-model="variable.expression" 
              placeholder="表达式或值"
              style="width: 200px; margin-right: 8px;"
            />
            <el-button 
              type="danger" 
              size="small" 
              icon="Delete"
              @click="removeOutputVariable(index)"
            />
          </div>
          <el-button 
            type="primary" 
            size="small" 
            icon="Plus"
            @click="addOutputVariable"
          >
            添加输出变量
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="执行后操作">
        <el-checkbox-group v-model="config.postActions">
          <el-checkbox label="generateReport">生成执行报告</el-checkbox>
          <el-checkbox label="sendNotification">发送通知</el-checkbox>
          <el-checkbox label="cleanupTemp">清理临时数据</el-checkbox>
          <el-checkbox label="updateStatus">更新任务状态</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="通知配置" v-if="config.postActions.includes('sendNotification')">
        <el-input 
          v-model="config.notificationConfig.recipients" 
          placeholder="通知接收人邮箱，多个用逗号分隔"
        />
        <el-select 
          v-model="config.notificationConfig.condition" 
          placeholder="通知条件"
          style="margin-top: 8px; width: 100%;"
        >
          <el-option label="总是通知" value="ALWAYS" />
          <el-option label="仅成功时" value="SUCCESS_ONLY" />
          <el-option label="仅失败时" value="FAILED_ONLY" />
        </el-select>
      </el-form-item>

      <el-form-item label="数据统计">
        <el-checkbox-group v-model="config.statistics">
          <el-checkbox label="executionTime">执行时长</el-checkbox>
          <el-checkbox label="processedRecords">处理记录数</el-checkbox>
          <el-checkbox label="errorCount">错误数量</el-checkbox>
          <el-checkbox label="memoryUsage">内存使用</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <!-- 配置预览 -->
    <el-divider content-position="left">配置预览</el-divider>
    <div class="config-preview">
      <el-alert 
        :title="getConfigSummary()" 
        type="info" 
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  node: {
    type: Object,
    default: () => ({})
  },
  detailed: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

// 配置数据
const config = ref({
  description: '',
  resultType: 'SUCCESS',
  resultMessage: '流程执行完成',
  outputVariables: [],
  postActions: ['updateStatus'],
  notificationConfig: {
    recipients: '',
    condition: 'FAILED_ONLY'
  },
  statistics: ['executionTime', 'processedRecords'],
  ...props.modelValue
})

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', newConfig)
}, { deep: true })

// 添加输出变量
function addOutputVariable() {
  config.value.outputVariables.push({
    name: '',
    expression: ''
  })
}

// 删除输出变量
function removeOutputVariable(index) {
  config.value.outputVariables.splice(index, 1)
}

// 配置摘要
const getConfigSummary = computed(() => {
  const resultTypeMap = {
    'SUCCESS': '成功结束',
    'FAILED': '失败结束',
    'CONDITIONAL': '条件结束'
  }
  
  const resultType = resultTypeMap[config.value.resultType] || '未设置'
  const actionCount = config.value.postActions.length
  const statCount = config.value.statistics.length
  
  return `结束节点：${resultType}，执行 ${actionCount} 个后置操作，统计 ${statCount} 项指标`
})
</script>

<style scoped>
.end-node-config {
  max-height: 600px;
  overflow-y: auto;
}

.output-variables {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}

.variable-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.variable-item:last-child {
  margin-bottom: 12px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.config-preview {
  margin-top: 16px;
}

.el-divider {
  margin: 20px 0 16px 0;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
