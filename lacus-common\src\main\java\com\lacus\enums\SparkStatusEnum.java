package com.lacus.enums;

public enum SparkStatusEnum {
    SUBMITTED, CONNECTED, RUNNING, FINISHED, FAILED, K<PERSON>LED, UNKNOWN, CREATED, LOST;

    public static SparkStatusEnum getSparkStateEnum(String state) {
        for (SparkStatusEnum stateEnum : SparkStatusEnum.values()) {
            if (stateEnum.name().equals(state)) {
                return stateEnum;
            }
        }
        return UNKNOWN;
    }
}
