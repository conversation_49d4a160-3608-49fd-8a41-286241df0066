package com.lacus.admin.controller.datacollection;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lacus.dao.datacollection.vo.DataCollectionTaskExecutionVO;
import com.lacus.dao.datacollection.vo.DataCollectionTaskPageQuery;
import com.lacus.dao.datacollection.vo.DataCollectionTaskVO;
import com.lacus.common.core.base.BaseController;
import com.lacus.common.core.dto.ResponseDTO;
import com.lacus.service.datacollection.service.IDataCollectionTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 数据采集任务Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/datacollection/task")
public class DataCollectionTaskController extends BaseController {

    @Autowired
    private IDataCollectionTaskService taskService;

    /**
     * 分页查询数据采集任务列表
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:list')")
    @GetMapping("/list")
    public ResponseDTO<?> list(DataCollectionTaskPageQuery pageQuery) {
        IPage<DataCollectionTaskVO> page = taskService.selectTaskPage(pageQuery);
        return ResponseDTO.ok(page);
    }

    /**
     * 获取数据采集任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping(value = "/{taskId}")
    public ResponseDTO<?> getInfo(@PathVariable("taskId") Long taskId) {
        DataCollectionTaskVO task = taskService.selectTaskById(taskId);
        return ResponseDTO.ok(task);
    }

    /**
     * 新增数据采集任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:add')")
    @PostMapping
    public ResponseDTO<?> add(@Validated @RequestBody DataCollectionTaskVO taskVO) {
        // 检查任务编码是否唯一
        if (!taskService.checkTaskCodeUnique(taskVO.getTaskCode(), null)) {
            return ResponseDTO.fail("新增任务'" + taskVO.getTaskName() + "'失败，任务编码已存在");
        }

        // 检查任务名称在同一分类下是否唯一
        if (!taskService.checkTaskNameUnique(taskVO.getTaskName(), taskVO.getCategoryId(), null)) {
            return ResponseDTO.fail("新增任务'" + taskVO.getTaskName() + "'失败，同一分类下任务名称已存在");
        }

        boolean result = taskService.insertTask(taskVO);
        return ResponseDTO.ok(result);
    }

    /**
     * 修改数据采集任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:edit')")
    @PutMapping
    public ResponseDTO<?> edit(@Validated @RequestBody DataCollectionTaskVO taskVO) {
        // 检查任务编码是否唯一
        if (!taskService.checkTaskCodeUnique(taskVO.getTaskCode(), taskVO.getTaskId())) {
            return ResponseDTO.fail("修改任务'" + taskVO.getTaskName() + "'失败，任务编码已存在");
        }

        // 检查任务名称在同一分类下是否唯一
        if (!taskService.checkTaskNameUnique(taskVO.getTaskName(), taskVO.getCategoryId(), taskVO.getTaskId())) {
            return ResponseDTO.fail("修改任务'" + taskVO.getTaskName() + "'失败，同一分类下任务名称已存在");
        }

        boolean result = taskService.updateTask(taskVO);
        return ResponseDTO.ok(result);
    }

    /**
     * 删除数据采集任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:remove')")
    @DeleteMapping("/{taskIds}")
    public ResponseDTO<?> remove(@PathVariable Long[] taskIds) {
        List<Long> taskIdList = Arrays.asList(taskIds);
        boolean result = taskService.deleteTaskByIds(taskIdList);
        return ResponseDTO.ok(result);
    }

    /**
     * 批量删除任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:remove')")
    @DeleteMapping("/batch")
    public ResponseDTO<?> batchRemove(@RequestBody Long[] taskIds) {
        if (taskIds == null || taskIds.length == 0) {
            return ResponseDTO.fail("请选择要删除的任务");
        }

        List<Long> taskIdList = Arrays.asList(taskIds);
        boolean result = taskService.deleteTaskByIds(taskIdList);
        return ResponseDTO.ok(result);
    }

    /**
     * 改变任务状态
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:edit')")
    @PutMapping("/changeStatus")
    public ResponseDTO<?> changeStatus(@RequestBody Map<String, Object> params) {
        Long taskId = Long.valueOf(params.get("taskId").toString());
        Integer status = Integer.valueOf(params.get("status").toString());
        boolean result = taskService.changeTaskStatus(taskId, status);
        return ResponseDTO.ok(result);
    }

    /**
     * 批量更新任务状态
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:edit')")
    @PutMapping("/batchUpdateStatus")
    public ResponseDTO<?> batchUpdateStatus(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Integer> taskIdInts = (List<Integer>) params.get("taskIds");
        Integer status = (Integer) params.get("status");

        if (taskIdInts == null || taskIdInts.isEmpty()) {
            return ResponseDTO.ok("请选择要更新的任务");
        }

        List<Long> taskIds = taskIdInts.stream()
                .map(Integer::longValue)
                .collect(java.util.stream.Collectors.toList());

        boolean result = taskService.batchUpdateTaskStatus(taskIds, status);
        return ResponseDTO.ok(result);
    }

    /**
     * 执行任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:execute')")
    @PostMapping("/execute/{taskId}")
    public ResponseDTO<?> execute(@PathVariable Long taskId) {
        String executionId = taskService.executeTask(taskId);
        return ResponseDTO.ok(executionId);
    }

    /**
     * 停止任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:execute')")
    @PostMapping("/stop/{taskId}")
    public ResponseDTO<?> stop(@PathVariable Long taskId) {
        boolean result = taskService.stopTask(taskId);
        return ResponseDTO.ok(result);
    }

    /**
     * 复制任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:add')")
    @PostMapping("/copy")
    public ResponseDTO<?> copy(@RequestBody Map<String, Object> params) {
        Long taskId = Long.valueOf(params.get("taskId").toString());
        String newTaskName = params.get("newTaskName").toString();

        if (StringUtils.isEmpty(newTaskName)) {
            return ResponseDTO.fail("新任务名称不能为空");
        }

        Long newTaskId = taskService.copyTask(taskId, newTaskName);
        return ResponseDTO.ok(newTaskId);
    }

    /**
     * 移动任务到其他分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:edit')")
    @PutMapping("/move")
    public ResponseDTO<?> move(@RequestBody Map<String, Object> params) {
        Long taskId = Long.valueOf(params.get("taskId").toString());
        Long targetCategoryId = Long.valueOf(params.get("targetCategoryId").toString());
        String moveReason = params.get("moveReason") != null ? params.get("moveReason").toString() : "";

        boolean result = taskService.moveTask(taskId, targetCategoryId, moveReason);
        return ResponseDTO.ok(result);
    }

    /**
     * 获取任务执行历史
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping("/history")
    public ResponseDTO<?> getHistory(@RequestParam(required = false) Long taskId,
                                   @RequestParam(required = false) String status,
                                   @RequestParam(required = false) String startTime,
                                   @RequestParam(required = false) String endTime) {
        List<DataCollectionTaskExecutionVO> list = taskService.getTaskHistory(taskId, status, startTime, endTime);
        return ResponseDTO.ok(list);
    }

    /**
     * 获取任务执行详情
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping("/execution/{executionId}")
    public ResponseDTO<?> getExecutionDetail(@PathVariable String executionId) {
        DataCollectionTaskExecutionVO execution = taskService.getExecutionDetail(executionId);
        return ResponseDTO.ok(execution);
    }

    /**
     * 验证任务配置
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @PostMapping("/validate/{taskId}")
    public ResponseDTO<?> validate(@PathVariable Long taskId) {
        Map<String, Object> result = taskService.validateTask(taskId);
        return ResponseDTO.ok(result);
    }

    /**
     * 预览任务执行结果
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @PostMapping("/preview/{taskId}")
    public ResponseDTO<?> preview(@PathVariable Long taskId) {
        Map<String, Object> result = taskService.previewTask(taskId);
        return ResponseDTO.ok(result);
    }

    /**
     * 导出任务配置
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:export')")
    @GetMapping("/export/{taskId}")
    public void exportTask(HttpServletResponse response, @PathVariable Long taskId) {
        taskService.exportTask(response, taskId);
    }

    /**
     * 导入任务配置
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:import')")
    @PostMapping("/import")
    public ResponseDTO<?> importTask(@RequestBody Map<String, Object> data) {
        Map<String, Object> result = taskService.importTask(data);
        return ResponseDTO.ok(result);
    }

    /**
     * 获取任务统计信息
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping("/stats")
    public ResponseDTO<?> getStats(@RequestParam(required = false) Long categoryId) {
        Map<String, Object> stats = taskService.getTaskStats(categoryId);
        return ResponseDTO.ok(stats);
    }

    /**
     * 检查任务编码是否唯一
     */
    @GetMapping("/checkCode")
    public ResponseDTO<?> checkTaskCode(@RequestParam String taskCode,
                                   @RequestParam(required = false) Long taskId) {
        if (StringUtils.isEmpty(taskCode)) {
            return ResponseDTO.fail("任务编码不能为空");
        }

        boolean unique = taskService.checkTaskCodeUnique(taskCode, taskId);
        return ResponseDTO.ok(unique);
    }

    /**
     * 检查任务名称是否唯一
     */
    @GetMapping("/checkName")
    public ResponseDTO<?> checkTaskName(@RequestParam String taskName,
                                   @RequestParam Long categoryId,
                                   @RequestParam(required = false) Long taskId) {
        if (StringUtils.isEmpty(taskName)) {
            return ResponseDTO.fail("任务名称不能为空");
        }

        boolean unique = taskService.checkTaskNameUnique(taskName, categoryId, taskId);
        return ResponseDTO.ok(unique);
    }

    /**
     * 获取任务依赖关系
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping("/dependencies/{taskId}")
    public ResponseDTO<?> getDependencies(@PathVariable Long taskId) {
        Map<String, Object> dependencies = taskService.getTaskDependencies(taskId);
        return ResponseDTO.ok(dependencies);
    }

    /**
     * 保存流程定义
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:edit')")
    @PostMapping("/flow")
    public ResponseDTO<?> saveFlow(@RequestBody Map<String, Object> data) {
        Map<String, Object> result = taskService.saveFlowDefinition(data);
        return ResponseDTO.ok(result);
    }

    /**
     * 获取流程定义
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping("/flow/{taskId}")
    public ResponseDTO<?> getFlow(@PathVariable Long taskId) {
        Map<String, Object> flow = taskService.getFlowDefinition(taskId);
        return ResponseDTO.ok(flow);
    }

    /**
     * 验证流程定义
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @PostMapping("/flow/validate")
    public ResponseDTO<?> validateFlow(@RequestBody Map<String, Object> data) {
        Map<String, Object> result = taskService.validateFlowDefinition(data);
        return ResponseDTO.ok(result);
    }

    /**
     * 执行流程
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:execute')")
    @PostMapping("/flow/execute")
    public ResponseDTO<?> executeFlow(@RequestBody Map<String, Object> data) {
        Map<String, Object> result = taskService.executeFlow(data);
        return ResponseDTO.ok(result);
    }

    /**
     * 根据任务编码查询任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping("/code/{taskCode}")
    public ResponseDTO<?> getTaskByCode(@PathVariable String taskCode) {
        DataCollectionTaskVO task = taskService.selectTaskByCode(taskCode);
        return ResponseDTO.ok(task);
    }

    /**
     * 根据任务名称查询任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping("/name/{taskName}")
    public ResponseDTO<?> getTaskByName(@PathVariable String taskName) {
        DataCollectionTaskVO task = taskService.selectTaskByName(taskName);
        return ResponseDTO.ok(task);
    }

    /**
     * 获取所有启用的任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:list')")
    @GetMapping("/enabled")
    public ResponseDTO<?> getEnabledTasks() {
        List<DataCollectionTaskVO> list = taskService.selectEnabledTasks();
        return ResponseDTO.ok(list);
    }

    /**
     * 根据调度类型查询任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:list')")
    @GetMapping("/schedule/{scheduleType}")
    public ResponseDTO<?> getTasksByScheduleType(@PathVariable String scheduleType) {
        List<DataCollectionTaskVO> list = taskService.selectTasksByScheduleType(scheduleType);
        return ResponseDTO.ok(list);
    }

    /**
     * 根据分类ID查询任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:list')")
    @GetMapping("/category/{categoryId}")
    public ResponseDTO<?> getTasksByCategoryId(@PathVariable Long categoryId,
                                          @RequestParam(required = false, defaultValue = "false") Boolean onlyEnabled) {
        List<DataCollectionTaskVO> list = taskService.selectTasksByCategoryId(categoryId, onlyEnabled);
        return ResponseDTO.ok(list);
    }

    /**
     * 查询最近执行的任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:list')")
    @GetMapping("/recent")
    public ResponseDTO<?> getRecentExecutedTasks(@RequestParam(required = false, defaultValue = "10") Integer limit) {
        List<DataCollectionTaskVO> list = taskService.selectRecentExecutedTasks(limit);
        return ResponseDTO.ok(list);
    }

    /**
     * 查询执行失败的任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:list')")
    @GetMapping("/failed")
    public ResponseDTO<?> getFailedTasks() {
        List<DataCollectionTaskVO> list = taskService.selectFailedTasks();
        return ResponseDTO.ok(list);
    }

    /**
     * 查询正在运行的任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:list')")
    @GetMapping("/running")
    public ResponseDTO<?> getRunningTasks() {
        List<DataCollectionTaskVO> list = taskService.selectRunningTasks();
        return ResponseDTO.ok(list);
    }

    /**
     * 查询热门任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:list')")
    @GetMapping("/hot")
    public ResponseDTO<?> getHotTasks(@RequestParam(required = false, defaultValue = "10") Integer limit) {
        List<DataCollectionTaskVO> list = taskService.selectHotTasks(limit);
        return ResponseDTO.ok(list);
    }

    /**
     * 根据采集类型查询任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:list')")
    @GetMapping("/collection/{collectionType}")
    public ResponseDTO<?> getTasksByCollectionType(@PathVariable String collectionType,
                                              @RequestParam(required = false, defaultValue = "false") Boolean onlyEnabled) {
        List<DataCollectionTaskVO> list = taskService.selectTasksByCollectionType(collectionType, onlyEnabled);
        return ResponseDTO.ok(list);
    }

    /**
     * 根据数据源类型查询任务
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:list')")
    @GetMapping("/source/{sourceType}")
    public ResponseDTO<?> getTasksBySourceType(@PathVariable String sourceType,
                                          @RequestParam(required = false, defaultValue = "false") Boolean onlyEnabled) {
        List<DataCollectionTaskVO> list = taskService.selectTasksBySourceType(sourceType, onlyEnabled);
        return ResponseDTO.ok(list);
    }

    /**
     * 获取任务创建统计
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:query')")
    @GetMapping("/createStats")
    public ResponseDTO<?> getTaskCreateStats(@RequestParam(required = false, defaultValue = "30") Integer days) {
        List<Map<String, Object>> stats = taskService.getTaskCreateStats(days);
        return ResponseDTO.ok(stats);
    }

    /**
     * 更新任务版本号
     */
    @PreAuthorize("@ss.hasPermi('datacollection:task:edit')")
    @PutMapping("/updateVersion/{taskId}")
    public ResponseDTO<?> updateTaskVersion(@PathVariable Long taskId) {
        boolean result = taskService.updateTaskVersion(taskId);
        return ResponseDTO.ok(result);
    }
}
