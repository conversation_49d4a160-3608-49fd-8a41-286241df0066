<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lacus.dao.datawarehouse.mapper.EtlTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lacus.dao.datawarehouse.entity.EtlTaskEntity">
        <id column="task_id" property="taskId" />
        <result column="task_name" property="taskName" />
        <result column="description" property="description" />
        <result column="source_layer" property="sourceLayer" />
        <result column="target_layer" property="targetLayer" />
        <result column="schedule_type" property="scheduleType" />
        <result column="cron_expression" property="cronExpression" />
        <result column="timezone" property="timezone" />
        <result column="target_table" property="targetTable" />
        <result column="write_mode" property="writeMode" />
        <result column="primary_keys" property="primaryKeys" />
        <result column="source_tables_config" property="sourceTablesConfig" />
        <result column="field_mappings_config" property="fieldMappingsConfig" />
        <result column="quality_rules_config" property="qualityRulesConfig" />
        <result column="status" property="status" />
        <result column="last_run_time" property="lastRunTime" />
        <result column="last_run_status" property="lastRunStatus" />
        <result column="source_table_count" property="sourceTableCount" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectEtlTaskVo">
        select task_id, task_name, description, source_layer, target_layer, schedule_type, 
               cron_expression, timezone, target_table, write_mode, primary_keys, 
               source_tables_config, field_mappings_config, quality_rules_config, 
               status, last_run_time, last_run_status, source_table_count, 
               create_time, update_time, deleted
        from etl_task
    </sql>

    <!-- 分页查询ETL任务列表 -->
    <select id="selectEtlTaskPage" resultMap="BaseResultMap">
        <include refid="selectEtlTaskVo"/>
        <where>
            deleted = 0
            <if test="params.taskName != null and params.taskName != ''">
                AND task_name like concat('%', #{params.taskName}, '%')
            </if>
            <if test="params.sourceLayer != null and params.sourceLayer != ''">
                AND source_layer = #{params.sourceLayer}
            </if>
            <if test="params.targetLayer != null and params.targetLayer != ''">
                AND target_layer = #{params.targetLayer}
            </if>
            <if test="params.status != null">
                AND status = #{params.status}
            </if>
            <if test="params.scheduleType != null and params.scheduleType != ''">
                AND schedule_type = #{params.scheduleType}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND create_time >= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND create_time &lt;= #{params.endTime}
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 查询ETL任务列表 -->
    <select id="selectEtlTaskList" resultMap="BaseResultMap">
        <include refid="selectEtlTaskVo"/>
        <where>
            deleted = 0
            <if test="params.taskName != null and params.taskName != ''">
                AND task_name like concat('%', #{params.taskName}, '%')
            </if>
            <if test="params.sourceLayer != null and params.sourceLayer != ''">
                AND source_layer = #{params.sourceLayer}
            </if>
            <if test="params.targetLayer != null and params.targetLayer != ''">
                AND target_layer = #{params.targetLayer}
            </if>
            <if test="params.status != null">
                AND status = #{params.status}
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 根据任务名称查询任务 -->
    <select id="selectByTaskName" resultMap="BaseResultMap">
        <include refid="selectEtlTaskVo"/>
        where deleted = 0 and task_name = #{taskName}
        <if test="excludeId != null">
            and task_id != #{excludeId}
        </if>
        limit 1
    </select>

    <!-- 根据状态查询任务列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        <include refid="selectEtlTaskVo"/>
        where deleted = 0 and status = #{status}
        order by create_time desc
    </select>

    <!-- 根据调度类型查询任务列表 -->
    <select id="selectByScheduleType" resultMap="BaseResultMap">
        <include refid="selectEtlTaskVo"/>
        where deleted = 0 and schedule_type = #{scheduleType}
        order by create_time desc
    </select>

    <!-- 根据数据层级查询任务列表 -->
    <select id="selectByLayer" resultMap="BaseResultMap">
        <include refid="selectEtlTaskVo"/>
        where deleted = 0
        <if test="sourceLayer != null and sourceLayer != ''">
            and source_layer = #{sourceLayer}
        </if>
        <if test="targetLayer != null and targetLayer != ''">
            and target_layer = #{targetLayer}
        </if>
        order by create_time desc
    </select>

    <!-- 获取ETL任务统计信息 -->
    <select id="selectEtlTaskStats" resultType="map">
        select 
            count(*) as totalTasks,
            sum(case when status = 1 then 1 else 0 end) as enabledTasks,
            sum(case when status = 0 then 1 else 0 end) as disabledTasks,
            sum(case when last_run_status = 'RUNNING' then 1 else 0 end) as runningTasks,
            sum(case when date(last_run_time) = curdate() and last_run_status = 'SUCCESS' then 1 else 0 end) as todaySuccessTasks,
            sum(case when date(last_run_time) = curdate() and last_run_status = 'FAILED' then 1 else 0 end) as todayFailedTasks,
            sum(case when source_layer = 'ODS' then 1 else 0 end) as odsLayerTasks,
            sum(case when target_layer = 'DWD' then 1 else 0 end) as dwdLayerTasks,
            sum(case when target_layer = 'DWS' then 1 else 0 end) as dwsLayerTasks,
            sum(case when target_layer = 'ADS' then 1 else 0 end) as adsLayerTasks
        from etl_task 
        where deleted = 0
    </select>

    <!-- 批量更新任务状态 -->
    <update id="batchUpdateStatus">
        update etl_task set status = #{status}, update_time = now()
        where task_id in
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        and deleted = 0
    </update>

    <!-- 更新任务执行信息 -->
    <update id="updateExecutionInfo">
        update etl_task 
        set last_run_time = #{lastRunTime}, 
            last_run_status = #{lastRunStatus}, 
            update_time = now()
        where task_id = #{taskId} and deleted = 0
    </update>

    <!-- 获取任务执行趋势数据 -->
    <select id="selectExecutionTrend" resultType="map">
        select 
            date(last_run_time) as date,
            count(*) as totalExecutions,
            sum(case when last_run_status = 'SUCCESS' then 1 else 0 end) as successExecutions,
            sum(case when last_run_status = 'FAILED' then 1 else 0 end) as failedExecutions
        from etl_task 
        where deleted = 0 
          and last_run_time >= date_sub(curdate(), interval #{days} day)
          and last_run_time is not null
        group by date(last_run_time)
        order by date(last_run_time)
    </select>

</mapper>
