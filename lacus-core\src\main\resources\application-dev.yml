# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: **********************************************************************************************************************************************
                username: root
                password: 123456
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url:
                username:
                password:
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: lacus
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
        # Doris数据源配置
        doris:
            type: com.alibaba.druid.pool.DruidDataSource
            driverClassName: com.mysql.cj.jdbc.Driver
            url: **********************************************************************************************************************************************
            username: root
            password: 123456
            # 连接池配置
            initialSize: 3
            minIdle: 3
            maxActive: 10
            maxWait: 60000
            timeBetweenEvictionRunsMillis: 60000
            minEvictableIdleTimeMillis: 300000
            maxEvictableIdleTimeMillis: 900000
            validationQuery: SELECT 1
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
    # redis 配置
    redis:
        # 地址
        host: 127.0.0.1
        # 端口，默认为6379
        port: 6379
        # 数据库索引
        database: 0
        # 密码
#        password: 123456
        # 连接超时时间
        timeout: 10s
        lettuce:
            pool:
                # 连接池中的最小空闲连接
                min-idle: 0
                # 连接池中的最大空闲连接
                max-idle: 8
                # 连接池的最大数据库连接数
                max-active: 8
                # #连接池最大阻塞等待时间（使用负值表示没有限制）
                max-wait: -1ms

logging:
    config: classpath:logback-dev.xml

# 数据仓库配置
datawarehouse:
  # ETL配置
  etl:
    # 最大并发任务数
    max-concurrent-tasks: 10
    # 任务超时时间（分钟）
    task-timeout-minutes: 60
    # 最大重试次数
    max-retry-count: 3
    # 预览数据最大行数
    preview-max-rows: 100
    # 支持的数据层级
    supported-layers:
      - ods_db
      - dwd_db
      - dws_db
      - ads_db

  # 质量监控配置
  quality:
    # 默认检查频率
    default-check-frequency: DAILY
    # 质量告警阈值
    alert-threshold: 80

  # 调度配置
  schedule:
    # 最大并发调度任务数
    max-concurrent-jobs: 5
    # 调度器线程池大小
    scheduler-pool-size: 10
    # 任务执行超时时间（分钟）
    job-timeout-minutes: 120

  # Doris数据库配置
  doris:
    # 连接超时时间（秒）
    connection-timeout: 30
    # 查询超时时间（秒）
    query-timeout: 300
    # 数据库映射
    databases:
      ods: ods_db
      dwd: dwd_db
      dws: dws_db
      ads: ads_db

  # 线程池配置
  thread-pool:
    # ETL执行器配置
    etl-executor:
      core-pool-size: 5
      max-pool-size: 20
      queue-capacity: 100
      thread-name-prefix: etl-executor-
      keep-alive-seconds: 60
    # 质量监控执行器配置
    quality-executor:
      core-pool-size: 3
      max-pool-size: 10
      queue-capacity: 50
      thread-name-prefix: quality-executor-
      keep-alive-seconds: 60
    # 调度任务执行器配置
    schedule-executor:
      core-pool-size: 2
      max-pool-size: 8
      queue-capacity: 30
      thread-name-prefix: schedule-executor-
      keep-alive-seconds: 60
