<template>
  <div class="condition-node-config">
    <el-form :model="config" label-width="100px" size="small">
      <!-- 条件类型 -->
      <el-form-item label="条件类型" required>
        <el-select v-model="config.conditionType" placeholder="请选择条件类型" @change="handleConditionTypeChange">
          <el-option label="简单条件" value="SIMPLE" />
          <el-option label="复合条件" value="COMPLEX" />
          <el-option label="表达式条件" value="EXPRESSION" />
          <el-option label="数据条件" value="DATA" />
        </el-select>
      </el-form-item>

      <!-- 简单条件配置 -->
      <template v-if="config.conditionType === 'SIMPLE'">
        <el-form-item label="左操作数" required>
          <el-input 
            v-model="config.simple.leftOperand" 
            placeholder="变量名或值，如：${recordCount}"
          />
        </el-form-item>
        <el-form-item label="操作符" required>
          <el-select v-model="config.simple.operator" placeholder="请选择操作符">
            <el-option label="等于 (==)" value="EQUALS" />
            <el-option label="不等于 (!=)" value="NOT_EQUALS" />
            <el-option label="大于 (>)" value="GREATER_THAN" />
            <el-option label="大于等于 (>=)" value="GREATER_EQUAL" />
            <el-option label="小于 (<)" value="LESS_THAN" />
            <el-option label="小于等于 (<=)" value="LESS_EQUAL" />
            <el-option label="包含" value="CONTAINS" />
            <el-option label="不包含" value="NOT_CONTAINS" />
            <el-option label="为空" value="IS_NULL" />
            <el-option label="不为空" value="IS_NOT_NULL" />
          </el-select>
        </el-form-item>
        <el-form-item label="右操作数" v-if="!['IS_NULL', 'IS_NOT_NULL'].includes(config.simple.operator)">
          <el-input 
            v-model="config.simple.rightOperand" 
            placeholder="变量名或值，如：100"
          />
        </el-form-item>
      </template>

      <!-- 复合条件配置 -->
      <template v-if="config.conditionType === 'COMPLEX'">
        <el-form-item label="条件组合">
          <div class="complex-conditions">
            <div 
              v-for="(condition, index) in config.complex.conditions" 
              :key="index"
              class="condition-item"
            >
              <div class="condition-row">
                <el-input 
                  v-model="condition.leftOperand" 
                  placeholder="左操作数"
                  style="width: 120px; margin-right: 8px;"
                />
                <el-select 
                  v-model="condition.operator" 
                  placeholder="操作符"
                  style="width: 100px; margin-right: 8px;"
                >
                  <el-option label="==" value="EQUALS" />
                  <el-option label="!=" value="NOT_EQUALS" />
                  <el-option label=">" value="GREATER_THAN" />
                  <el-option label=">=" value="GREATER_EQUAL" />
                  <el-option label="<" value="LESS_THAN" />
                  <el-option label="<=" value="LESS_EQUAL" />
                </el-select>
                <el-input 
                  v-model="condition.rightOperand" 
                  placeholder="右操作数"
                  style="width: 120px; margin-right: 8px;"
                />
                <el-button 
                  type="danger" 
                  size="small" 
                  icon="Delete"
                  @click="removeCondition(index)"
                />
              </div>
              <div class="logic-operator" v-if="index < config.complex.conditions.length - 1">
                <el-select 
                  v-model="condition.logicOperator" 
                  placeholder="逻辑操作符"
                  style="width: 100px;"
                >
                  <el-option label="AND" value="AND" />
                  <el-option label="OR" value="OR" />
                </el-select>
              </div>
            </div>
            <el-button 
              type="primary" 
              size="small" 
              icon="Plus"
              @click="addCondition"
            >
              添加条件
            </el-button>
          </div>
        </el-form-item>
      </template>

      <!-- 表达式条件配置 -->
      <template v-if="config.conditionType === 'EXPRESSION'">
        <el-form-item label="条件表达式" required>
          <el-input 
            v-model="config.expression.formula" 
            type="textarea"
            :rows="4"
            placeholder="请输入条件表达式，如：${recordCount} > 0 && ${errorCount} < 10"
          />
      <div class="form-tip">
        支持的操作符：==, !=, &gt;, &gt;=, &lt;, &lt;=, &&, ||, !, ()
        <br>支持变量引用：${variableName}
      </div>
        </el-form-item>
        <el-form-item label="表达式说明">
          <el-input 
            v-model="config.expression.description" 
            placeholder="请输入表达式的说明"
          />
        </el-form-item>
      </template>

      <!-- 数据条件配置 -->
      <template v-if="config.conditionType === 'DATA'">
        <el-form-item label="数据源变量" required>
          <el-input 
            v-model="config.data.sourceVariable" 
            placeholder="数据源变量名，如：queryResult"
          />
        </el-form-item>
        <el-form-item label="条件类型" required>
          <el-select v-model="config.data.checkType" placeholder="请选择检查类型">
            <el-option label="数据为空" value="EMPTY" />
            <el-option label="数据不为空" value="NOT_EMPTY" />
            <el-option label="记录数检查" value="COUNT_CHECK" />
            <el-option label="字段值检查" value="FIELD_CHECK" />
          </el-select>
        </el-form-item>
        <template v-if="config.data.checkType === 'COUNT_CHECK'">
          <el-form-item label="记录数条件">
            <el-select 
              v-model="config.data.countOperator" 
              placeholder="操作符"
              style="width: 100px; margin-right: 8px;"
            >
              <el-option label=">" value="GREATER_THAN" />
              <el-option label=">=" value="GREATER_EQUAL" />
              <el-option label="<" value="LESS_THAN" />
              <el-option label="<=" value="LESS_EQUAL" />
              <el-option label="==" value="EQUALS" />
            </el-select>
            <el-input-number 
              v-model="config.data.countValue" 
              :min="0"
              placeholder="记录数"
            />
          </el-form-item>
        </template>
        <template v-if="config.data.checkType === 'FIELD_CHECK'">
          <el-form-item label="字段名">
            <el-input 
              v-model="config.data.fieldName" 
              placeholder="要检查的字段名"
            />
          </el-form-item>
          <el-form-item label="字段条件">
            <el-select 
              v-model="config.data.fieldOperator" 
              placeholder="操作符"
              style="width: 100px; margin-right: 8px;"
            >
              <el-option label="==" value="EQUALS" />
              <el-option label="!=" value="NOT_EQUALS" />
              <el-option label=">" value="GREATER_THAN" />
              <el-option label="<" value="LESS_THAN" />
              <el-option label="包含" value="CONTAINS" />
            </el-select>
            <el-input 
              v-model="config.data.fieldValue" 
              placeholder="比较值"
              style="width: 150px;"
            />
          </el-form-item>
        </template>
      </template>

      <!-- 分支配置 -->
      <el-divider content-position="left">分支配置</el-divider>

      <el-form-item label="True分支">
        <el-input 
          v-model="config.branches.trueNodeId" 
          placeholder="条件为真时执行的节点ID"
        />
        <div class="form-tip">条件满足时跳转到的节点</div>
      </el-form-item>

      <el-form-item label="False分支">
        <el-input 
          v-model="config.branches.falseNodeId" 
          placeholder="条件为假时执行的节点ID"
        />
        <div class="form-tip">条件不满足时跳转到的节点</div>
      </el-form-item>

      <!-- 高级配置 -->
      <el-divider content-position="left">高级配置</el-divider>

      <el-form-item label="条件缓存">
        <el-switch 
          v-model="config.cache.enabled" 
          active-text="启用"
          inactive-text="禁用"
        />
        <div class="form-tip">缓存条件计算结果，提高性能</div>
      </el-form-item>

      <el-form-item label="缓存时间" v-if="config.cache.enabled">
        <el-input-number 
          v-model="config.cache.ttl" 
          :min="60"
          :max="3600"
          placeholder="秒"
        />
        <span class="form-tip">缓存有效时间（秒）</span>
      </el-form-item>

      <el-form-item label="错误处理">
        <el-select v-model="config.onError" placeholder="条件计算错误时的处理方式">
          <el-option label="抛出异常" value="THROW" />
          <el-option label="返回False" value="FALSE" />
          <el-option label="返回True" value="TRUE" />
          <el-option label="跳过节点" value="SKIP" />
        </el-select>
      </el-form-item>

      <el-form-item label="调试模式">
        <el-switch 
          v-model="config.debug" 
          active-text="启用"
          inactive-text="禁用"
        />
        <div class="form-tip">启用后会记录详细的条件计算过程</div>
      </el-form-item>
    </el-form>

    <!-- 配置预览 -->
    <el-divider content-position="left">配置预览</el-divider>
    <div class="config-preview">
      <el-alert 
        :title="getConfigSummary()" 
        type="info" 
        :closable="false"
        show-icon
      />
    </div>

    <!-- 测试按钮 -->
    <div class="test-section" v-if="detailed">
      <el-button type="success" @click="testCondition" :loading="testing">
        <el-icon><Operation /></el-icon>
        测试条件
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { Operation } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  node: {
    type: Object,
    default: () => ({})
  },
  detailed: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

// 配置数据
const config = ref({
  conditionType: 'SIMPLE',
  simple: {
    leftOperand: '',
    operator: 'EQUALS',
    rightOperand: ''
  },
  complex: {
    conditions: []
  },
  expression: {
    formula: '',
    description: ''
  },
  data: {
    sourceVariable: '',
    checkType: 'NOT_EMPTY',
    countOperator: 'GREATER_THAN',
    countValue: 0,
    fieldName: '',
    fieldOperator: 'EQUALS',
    fieldValue: ''
  },
  branches: {
    trueNodeId: '',
    falseNodeId: ''
  },
  cache: {
    enabled: false,
    ttl: 300
  },
  onError: 'THROW',
  debug: false,
  ...props.modelValue
})

const testing = ref(false)

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', newConfig)
}, { deep: true })

// 条件类型变化处理
function handleConditionTypeChange() {
  // 重置相关配置
  switch (config.value.conditionType) {
    case 'COMPLEX':
      if (config.value.complex.conditions.length === 0) {
        addCondition()
      }
      break
  }
}

// 添加复合条件
function addCondition() {
  config.value.complex.conditions.push({
    leftOperand: '',
    operator: 'EQUALS',
    rightOperand: '',
    logicOperator: 'AND'
  })
}

// 删除复合条件
function removeCondition(index) {
  config.value.complex.conditions.splice(index, 1)
}

// 测试条件
function testCondition() {
  testing.value = true
  setTimeout(() => {
    testing.value = false
    // 显示测试结果
  }, 2000)
}

// 配置摘要
const getConfigSummary = computed(() => {
  switch (config.value.conditionType) {
    case 'SIMPLE':
      const simple = config.value.simple
      return `简单条件：${simple.leftOperand || '?'} ${simple.operator || '?'} ${simple.rightOperand || '?'}`
    case 'COMPLEX':
      const condCount = config.value.complex.conditions.length
      return `复合条件：包含 ${condCount} 个子条件`
    case 'EXPRESSION':
      const expr = config.value.expression.formula
      return `表达式条件：${expr || '未设置表达式'}`
    case 'DATA':
      const dataCheck = config.value.data.checkType
      const variable = config.value.data.sourceVariable
      return `数据条件：检查 ${variable || '?'} 的 ${dataCheck || '?'}`
    default:
      return '未配置条件类型'
  }
})
</script>

<style scoped>
.condition-node-config {
  max-height: 600px;
  overflow-y: auto;
}

.complex-conditions {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}

.condition-item {
  margin-bottom: 12px;
}

.condition-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.logic-operator {
  text-align: center;
  margin-bottom: 8px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.config-preview {
  margin-top: 16px;
}

.test-section {
  margin-top: 16px;
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.el-divider {
  margin: 20px 0 16px 0;
}
</style>
