package com.lacus.admin.controller.datawarehouse;

import com.lacus.common.core.base.BaseController;
import com.lacus.common.core.dto.ResponseDTO;
import com.lacus.common.core.page.PageDTO;
import com.lacus.domain.datawarehouse.quality.QualityMonitorBusiness;
import com.lacus.service.datawarehouse.command.QualityMonitorAddCommand;
import com.lacus.service.datawarehouse.command.QualityMonitorUpdateCommand;
import com.lacus.service.datawarehouse.dto.QualityMonitorDTO;
import com.lacus.service.datawarehouse.dto.QualityStatsDTO;
import com.lacus.service.datawarehouse.dto.QualityTrendDTO;
import com.lacus.service.datawarehouse.query.QualityMonitorQuery;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.lacus.core.security.AuthenticationUtils.getUsername;

/**
 * 数据质量监控控制器
 */
@Api(value = "数据质量监控控制器", tags = {"数据质量监控控制器"})
@RestController
@RequestMapping("/datawarehouse/quality")
public class QualityMonitorController extends BaseController {

    @Autowired
    private QualityMonitorBusiness qualityMonitorBusiness;

    /**
     * 查询质量监控列表
     */
    @GetMapping("/monitor/list")
    @PreAuthorize("@permission.has('datawarehouse:quality:list')")
    public ResponseDTO<?> list(QualityMonitorQuery query) {
        PageDTO page = qualityMonitorBusiness.queryQualityMonitorList(query);
        return ResponseDTO.ok(page);
    }

    /**
     * 获取质量监控详细信息
     */
    @GetMapping("/monitor/{monitorId}")
    @PreAuthorize("@permission.has('datawarehouse:quality:query')")
    public ResponseDTO<QualityMonitorDTO> getInfo(@PathVariable Long monitorId) {
        return ResponseDTO.ok(qualityMonitorBusiness.queryQualityMonitorById(monitorId));
    }

    /**
     * 新增质量监控
     */
    @PostMapping("/monitor")
    @PreAuthorize("@permission.has('datawarehouse:quality:add')")
    public ResponseDTO add(@RequestBody QualityMonitorAddCommand command) {
        command.setCreateBy(getUsername()); // 这里可以替换为实际的创建用户
        return qualityMonitorBusiness.addQualityMonitor(command);
    }

    /**
     * 修改质量监控
     */
    @PutMapping("/monitor")
    @PreAuthorize("@permission.has('datawarehouse:quality:edit')")
    public ResponseDTO<String> edit(@RequestBody QualityMonitorUpdateCommand command) {
        command.setUpdateBy(getUsername()); // 这里可以替换为实际的更新用户
        return qualityMonitorBusiness.updateQualityMonitor(command);
    }

    /**
     * 删除质量监控
     */
    @DeleteMapping("/monitor/{monitorIds}")
    @PreAuthorize("@permission.has('datawarehouse:quality:remove')")
    public ResponseDTO<String> remove(@PathVariable Long[] monitorIds) {
        return qualityMonitorBusiness.batchDeleteQualityMonitor(Arrays.asList(monitorIds));
    }

    /**
     * 执行质量检查
     */
    @PostMapping("/check/{monitorId}")
    @PreAuthorize("@permission.has('datawarehouse:quality:check')")
    public ResponseDTO<String> check(@PathVariable Long monitorId) {
        return qualityMonitorBusiness.runQualityCheck(monitorId);
    }

    /**
     * 批量执行质量检查
     */
    @PostMapping("/batch-run-check")
    @PreAuthorize("@permission.has('datawarehouse:quality:check')")
    public ResponseDTO<Void> batchRunCheck(@RequestBody Long[] monitorIds) {
        qualityMonitorBusiness.batchRunQualityCheck(Arrays.asList(monitorIds));
        return ResponseDTO.ok();
    }

    /**
     * 批量执行质量检查
     */
    @PostMapping("/batch-check")
    @PreAuthorize("@permission.has('datawarehouse:quality:check')")
    public ResponseDTO<String> batchCheck(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> monitorIds = (List<Long>) params.get("monitorIds");
        return qualityMonitorBusiness.batchRunQualityCheck(monitorIds);
    }

    /**
     * 获取质量统计信息
     */
    @GetMapping("/stats")
    @PreAuthorize("@permission.has('datawarehouse:quality:list')")
    public ResponseDTO<QualityStatsDTO> getStats() {
        return qualityMonitorBusiness.getQualityStats();
    }

    /**
     * 获取质量趋势数据
     */
    @GetMapping("/trend")
    @PreAuthorize("@permission.has('datawarehouse:quality:list')")
    public ResponseDTO<QualityTrendDTO> getTrend(@RequestParam String period) {
        return qualityMonitorBusiness.getQualityTrend(period);
    }

    /**
     * 获取质量报告
     */
    @GetMapping("/report/{monitorId}")
    @PreAuthorize("@permission.has('datawarehouse:quality:query')")
    public ResponseDTO<Object> getReport(@PathVariable Long monitorId) {
        return ResponseDTO.ok(qualityMonitorBusiness.getQualityReport(monitorId));
    }

    /**
     * 获取质量检查历史
     */
    @GetMapping("/history/{monitorId}")
    @PreAuthorize("@permission.has('datawarehouse:quality:query')")
    public ResponseDTO<Object> getHistory(@PathVariable Long monitorId) {
        return qualityMonitorBusiness.getQualityCheckHistory(monitorId);
    }

    /**
     * 获取质量异常详情
     */
    @GetMapping("/issues/{monitorId}")
    @PreAuthorize("@permission.has('datawarehouse:quality:query')")
    public ResponseDTO<Object> getIssues(@PathVariable Long monitorId) {
        return qualityMonitorBusiness.getQualityIssues(monitorId);
    }

    /**
     * 处理质量异常
     */
    @PostMapping("/issues/{issueId}/handle")
    @PreAuthorize("@permission.has('datawarehouse:quality:handle')")
    public ResponseDTO<String> handleIssue(@PathVariable Long issueId, @RequestBody Map<String, String> params) {
        String action = params.get("action");
        String comment = params.get("comment");
        return qualityMonitorBusiness.handleQualityIssue(issueId, action, comment);
    }

    /**
     * 获取质量评分详情
     */
    @GetMapping("/score/{monitorId}")
    @PreAuthorize("@permission.has('datawarehouse:quality:query')")
    public ResponseDTO<Object> getScoreDetail(@PathVariable Long monitorId) {
        return ResponseDTO.ok(qualityMonitorBusiness.getQualityScoreDetail(monitorId));
    }

    /**
     * 设置质量阈值
     */
    @PostMapping("/threshold")
    @PreAuthorize("@permission.has('datawarehouse:quality:config')")
    public ResponseDTO<String> setThreshold(@RequestBody Object data) {
        return qualityMonitorBusiness.setQualityThreshold(data);
    }

    /**
     * 获取质量阈值配置
     */
    @GetMapping("/threshold/{monitorId}")
    @PreAuthorize("@permission.has('datawarehouse:quality:query')")
    public ResponseDTO<Object> getThreshold(@PathVariable Long monitorId) {
        return ResponseDTO.ok(qualityMonitorBusiness.getQualityThreshold(monitorId));
    }

    /**
     * 导出质量报告
     */
    @GetMapping("/export")
    @PreAuthorize("@permission.has('datawarehouse:quality:export')")
    public ResponseDTO<Object> export(QualityMonitorQuery query) {
        return ResponseDTO.ok(qualityMonitorBusiness.exportQualityReport(query));
    }

    /**
     * 获取表字段信息
     */
    @GetMapping("/table/fields")
    @PreAuthorize("@permission.has('datawarehouse:quality:query')")
    public ResponseDTO<Object> getTableFields(@RequestParam String tableName) {
        return ResponseDTO.ok(qualityMonitorBusiness.getTableFields(tableName));
    }

    /**
     * 获取质量规则模板
     */
    @GetMapping("/rule/templates")
    @PreAuthorize("@permission.has('datawarehouse:quality:query')")
    public ResponseDTO<Object> getRuleTemplates() {
        return ResponseDTO.ok(qualityMonitorBusiness.getQualityRuleTemplates());
    }

    /**
     * 验证质量规则
     */
    @PostMapping("/rule/validate")
    @PreAuthorize("@permission.has('datawarehouse:quality:config')")
    public ResponseDTO<Boolean> validateRule(@RequestBody Object data) {
        return qualityMonitorBusiness.validateQualityRule(data);
    }
}
