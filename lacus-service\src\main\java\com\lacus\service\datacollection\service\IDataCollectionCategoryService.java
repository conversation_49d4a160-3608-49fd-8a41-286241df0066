package com.lacus.service.datacollection.service;

import com.lacus.datacollection.domain.DataCollectionCategory;

import java.util.List;
import java.util.Map;

/**
 * 数据采集分类Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IDataCollectionCategoryService 
{
    /**
     * 查询数据采集分类
     * 
     * @param categoryId 数据采集分类主键
     * @return 数据采集分类
     */
    public DataCollectionCategory selectDataCollectionCategoryByCategoryId(Long categoryId);

    /**
     * 查询数据采集分类列表
     * 
     * @param dataCollectionCategory 数据采集分类
     * @return 数据采集分类集合
     */
    public List<DataCollectionCategory> selectDataCollectionCategoryList(DataCollectionCategory dataCollectionCategory);

    /**
     * 查询分类树形结构
     * 
     * @return 分类树
     */
    public List<DataCollectionCategory> selectCategoryTree();

    /**
     * 根据父级ID查询子分类列表
     * 
     * @param parentId 父级ID
     * @return 子分类列表
     */
    public List<DataCollectionCategory> selectCategoryByParentId(Long parentId);

    /**
     * 新增数据采集分类
     * 
     * @param dataCollectionCategory 数据采集分类
     * @return 结果
     */
    public int insertDataCollectionCategory(DataCollectionCategory dataCollectionCategory);

    /**
     * 修改数据采集分类
     * 
     * @param dataCollectionCategory 数据采集分类
     * @return 结果
     */
    public int updateDataCollectionCategory(DataCollectionCategory dataCollectionCategory);

    /**
     * 批量删除数据采集分类
     * 
     * @param categoryIds 需要删除的数据采集分类主键集合
     * @return 结果
     */
    public int deleteDataCollectionCategoryByCategoryIds(Long[] categoryIds);

    /**
     * 删除数据采集分类信息
     * 
     * @param categoryId 数据采集分类主键
     * @return 结果
     */
    public int deleteDataCollectionCategoryByCategoryId(Long categoryId);

    /**
     * 检查分类编码是否唯一
     * 
     * @param dataCollectionCategory 分类信息
     * @return 结果
     */
    public boolean checkCategoryCodeUnique(DataCollectionCategory dataCollectionCategory);

    /**
     * 检查分类名称是否唯一
     * 
     * @param dataCollectionCategory 分类信息
     * @return 结果
     */
    public boolean checkCategoryNameUnique(DataCollectionCategory dataCollectionCategory);

    /**
     * 检查是否存在子分类
     * 
     * @param categoryId 分类ID
     * @return 结果
     */
    public boolean hasChildCategory(Long categoryId);

    /**
     * 检查分类下是否存在任务
     * 
     * @param categoryId 分类ID
     * @return 结果
     */
    public boolean hasTask(Long categoryId);

    /**
     * 移动分类
     * 
     * @param dataCollectionCategory 分类信息
     * @return 结果
     */
    public int moveCategory(DataCollectionCategory dataCollectionCategory);

    /**
     * 获取分类统计信息
     * 
     * @param categoryId 分类ID
     * @return 统计信息
     */
    public Map<String, Object> getCategoryStats(Long categoryId);

    /**
     * 批量删除分类
     * 
     * @param categoryIds 分类ID数组
     * @return 结果
     */
    public int batchDeleteCategory(Long[] categoryIds);

    /**
     * 构建分类路径
     * 
     * @param categoryId 分类ID
     * @return 分类路径
     */
    public String buildCategoryPath(Long categoryId);

    /**
     * 更新分类路径
     * 
     * @param categoryId 分类ID
     * @return 结果
     */
    public int updateCategoryPath(Long categoryId);

    /**
     * 获取分类层级
     * 
     * @param parentId 父级ID
     * @return 层级
     */
    public int getCategoryLevel(Long parentId);

    /**
     * 获取分类的所有子分类ID
     * 
     * @param categoryId 分类ID
     * @return 子分类ID列表
     */
    public List<Long> getChildCategoryIds(Long categoryId);

    /**
     * 根据分类路径查询分类
     * 
     * @param categoryPath 分类路径
     * @return 分类信息
     */
    public DataCollectionCategory selectCategoryByPath(String categoryPath);

    /**
     * 获取分类的完整路径名称
     * 
     * @param categoryId 分类ID
     * @return 完整路径名称
     */
    public String getCategoryFullName(Long categoryId);

    /**
     * 检查分类是否可以删除
     * 
     * @param categoryId 分类ID
     * @return 结果
     */
    public boolean canDeleteCategory(Long categoryId);

    /**
     * 获取分类的任务数量统计
     * 
     * @param categoryId 分类ID
     * @return 任务数量
     */
    public int getTaskCountByCategory(Long categoryId);

    /**
     * 更新分类的任务数量
     * 
     * @param categoryId 分类ID
     * @return 结果
     */
    public int updateTaskCount(Long categoryId);

    /**
     * 获取所有启用的分类
     * 
     * @return 分类列表
     */
    public List<DataCollectionCategory> selectEnabledCategories();

    /**
     * 根据分类编码查询分类
     * 
     * @param categoryCode 分类编码
     * @return 分类信息
     */
    public DataCollectionCategory selectCategoryByCode(String categoryCode);
}
