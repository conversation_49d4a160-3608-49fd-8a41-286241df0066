package com.lacus.service.datacollection.engine.executor;

import com.lacus.service.datacollection.engine.NodeExecutor;
import com.lacus.service.datacollection.model.FlowContext;
import com.lacus.service.datacollection.model.FlowDefinition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL查询节点执行器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SqlQueryNodeExecutor implements NodeExecutor {

    @Autowired
    private DataSource dataSource;

    private final Pattern variablePattern = Pattern.compile("\\$\\{([^}]+)\\}");

    @Override
    public String getSupportedNodeType() {
        return "SQL_QUERY";
    }

    @Override
    public NodeExecutionResult execute(FlowDefinition.FlowNode node, FlowContext context) {
        try {
            log.info("开始执行SQL查询节点: {}", node.getName());
            context.addLog("开始执行SQL查询节点: " + node.getName());

            Map<String, Object> config = node.getConfig();
            
            // 获取配置参数
            String sql = (String) config.get("sql");
            String dataSourceName = (String) config.get("dataSource");
            Map<String, Object> parameters = (Map<String, Object>) config.getOrDefault("parameters", new HashMap<>());
            Integer fetchSize = (Integer) config.getOrDefault("fetchSize", 1000);
            String outputVariable = (String) config.getOrDefault("outputVariable", "queryResult");

            // 替换SQL中的变量
            String processedSql = replaceSqlVariables(sql, context, parameters);
            context.addLog("执行SQL: " + processedSql);

            // 创建JdbcTemplate
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            jdbcTemplate.setFetchSize(fetchSize);

            // 执行查询
            List<Map<String, Object>> result = jdbcTemplate.queryForList(processedSql);
            
            context.addLog("SQL查询完成，返回记录数: " + result.size());

            // 保存查询结果到上下文
            context.setGlobalVariable(outputVariable, result);
            context.setDataCache("lastQueryResult", result);

            // 处理分页查询结果
            handlePaginationResult(result, context, config);

            // 如果是在循环中，可能需要累积结果
            handleLoopAccumulation(result, context, config);

            context.addLog("SQL查询节点执行成功，数据已保存到变量: " + outputVariable);
            return new NodeExecutionResult("SUCCESS", "SQL查询成功", result);

        } catch (Exception e) {
            log.error("SQL查询节点执行失败: {}", e.getMessage(), e);
            context.addLog("SQL查询节点执行失败: " + e.getMessage());
            return new NodeExecutionResult("FAILED", "SQL查询失败: " + e.getMessage(), null);
        }
    }

    @Override
    public ValidationResult validate(FlowDefinition.FlowNode node) {
        Map<String, Object> config = node.getConfig();
        if (config == null) {
            return new ValidationResult(false, "SQL查询节点配置不能为空");
        }

        String sql = (String) config.get("sql");
        if (!StringUtils.hasText(sql)) {
            return new ValidationResult(false, "SQL语句不能为空");
        }

        // 验证SQL语法（简单检查）
        String upperSql = sql.trim().toUpperCase();
        if (!upperSql.startsWith("SELECT")) {
            return new ValidationResult(false, "只支持SELECT查询语句");
        }

        // 检查是否包含危险的SQL关键字
        String[] dangerousKeywords = {"DROP", "DELETE", "UPDATE", "INSERT", "TRUNCATE", "ALTER"};
        for (String keyword : dangerousKeywords) {
            if (upperSql.contains(keyword)) {
                return new ValidationResult(false, "SQL语句包含危险关键字: " + keyword);
            }
        }

        return new ValidationResult(true, "验证通过");
    }

    /**
     * 替换SQL中的变量
     */
    private String replaceSqlVariables(String sql, FlowContext context, Map<String, Object> parameters) {
        if (sql == null) {
            return null;
        }

        String result = sql;
        
        // 首先替换parameters中的变量
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            if (result.contains(placeholder)) {
                Object value = entry.getValue();
                // 如果value本身包含变量，先替换
                if (value instanceof String) {
                    value = replaceGlobalVariables((String) value, context);
                }
                result = result.replace(placeholder, String.valueOf(value));
            }
        }
        
        // 然后替换全局变量
        result = replaceGlobalVariables(result, context);
        
        return result;
    }

    /**
     * 替换全局变量
     */
    private String replaceGlobalVariables(String text, FlowContext context) {
        if (text == null) {
            return null;
        }

        String result = text;
        Matcher matcher = variablePattern.matcher(result);
        
        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object variableValue = context.getGlobalVariable(variableName);
            
            if (variableValue != null) {
                String placeholder = "${" + variableName + "}";
                result = result.replace(placeholder, String.valueOf(variableValue));
            }
        }
        
        return result;
    }

    /**
     * 处理分页查询结果
     */
    private void handlePaginationResult(List<Map<String, Object>> result, FlowContext context, Map<String, Object> config) {
        try {
            // 保存当前页数据
            context.setGlobalVariable("currentPageData", result);
            context.setGlobalVariable("resultCount", result.size());
            
            // 获取页大小配置
            Object pageSizeObj = context.getGlobalVariable("pageSize");
            if (pageSizeObj instanceof Integer) {
                Integer pageSize = (Integer) pageSizeObj;
                
                // 如果返回的记录数小于页大小，说明已经是最后一页
                if (result.size() < pageSize) {
                    context.setGlobalVariable("isLastPage", true);
                    context.addLog("已到达最后一页，返回记录数: " + result.size() + "，页大小: " + pageSize);
                } else {
                    context.setGlobalVariable("isLastPage", false);
                }
            }
            
        } catch (Exception e) {
            log.warn("处理分页查询结果失败", e);
        }
    }

    /**
     * 处理循环中的结果累积
     */
    private void handleLoopAccumulation(List<Map<String, Object>> result, FlowContext context, Map<String, Object> config) {
        try {
            Boolean accumulate = (Boolean) config.getOrDefault("accumulateResults", false);
            if (accumulate) {
                String accumulateVariable = (String) config.getOrDefault("accumulateVariable", "allResults");
                
                // 获取已累积的结果
                Object existingResults = context.getGlobalVariable(accumulateVariable);
                List<Map<String, Object>> allResults;
                
                if (existingResults instanceof List) {
                    allResults = (List<Map<String, Object>>) existingResults;
                } else {
                    allResults = new java.util.ArrayList<>();
                }
                
                // 添加当前结果
                allResults.addAll(result);
                
                // 保存累积结果
                context.setGlobalVariable(accumulateVariable, allResults);
                context.addLog("累积结果已更新，总记录数: " + allResults.size());
            }
        } catch (Exception e) {
            log.warn("处理循环结果累积失败", e);
        }
    }

    /**
     * 构建分页SQL
     */
    private String buildPaginationSql(String baseSql, int pageNum, int pageSize, String databaseType) {
        switch (databaseType.toUpperCase()) {
            case "MYSQL":
                int offset = (pageNum - 1) * pageSize;
                return baseSql + " LIMIT " + offset + ", " + pageSize;
                
            case "SQLSERVER":
                // SQL Server 2012+ 语法
                return baseSql + " OFFSET " + ((pageNum - 1) * pageSize) + " ROWS FETCH NEXT " + pageSize + " ROWS ONLY";
                
            case "ORACLE":
                // Oracle 12c+ 语法
                return baseSql + " OFFSET " + ((pageNum - 1) * pageSize) + " ROWS FETCH NEXT " + pageSize + " ROWS ONLY";
                
            case "POSTGRESQL":
                return baseSql + " LIMIT " + pageSize + " OFFSET " + ((pageNum - 1) * pageSize);
                
            default:
                // 默认使用MySQL语法
                return baseSql + " LIMIT " + ((pageNum - 1) * pageSize) + ", " + pageSize;
        }
    }

    /**
     * 获取数据库类型
     */
    private String getDatabaseType() {
        try {
            String url = dataSource.getConnection().getMetaData().getURL();
            if (url.contains("mysql")) {
                return "MYSQL";
            } else if (url.contains("sqlserver")) {
                return "SQLSERVER";
            } else if (url.contains("oracle")) {
                return "ORACLE";
            } else if (url.contains("postgresql")) {
                return "POSTGRESQL";
            } else {
                return "MYSQL"; // 默认
            }
        } catch (Exception e) {
            log.warn("获取数据库类型失败，使用默认类型MYSQL", e);
            return "MYSQL";
        }
    }
}
