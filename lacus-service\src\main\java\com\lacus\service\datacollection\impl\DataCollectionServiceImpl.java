package com.lacus.service.datacollection.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lacus.common.core.page.PageDTO;
import com.lacus.dao.datacollection.entity.*;
import com.lacus.dao.datacollection.mapper.*;
import com.lacus.service.datacollection.service.DataCollectionService;
import com.lacus.service.datacollection.command.DataCollectionTaskAddCommand;
import com.lacus.service.datacollection.engine.FlowEngine;
import com.lacus.service.datacollection.model.FlowContext;
import com.lacus.service.datacollection.model.FlowDefinition;
import com.lacus.service.datacollection.query.DataCollectionTaskQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据采集服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DataCollectionServiceImpl implements DataCollectionService {

    @Autowired
    private DataCollectionTaskMapper dataCollectionTaskMapper;

    @Autowired
    private FlowNodeDefinitionMapper flowNodeDefinitionMapper;

    @Autowired
    private FlowEdgeDefinitionMapper flowEdgeDefinitionMapper;

    @Autowired
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Autowired
    private CollectionExecutionHistoryMapper executionHistoryMapper;

    @Autowired
    private FlowEngine flowEngine;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public PageDTO queryTaskList(DataCollectionTaskQuery query) {
        log.info("查询数据采集任务列表: {}", query);

        // 构建查询条件
        QueryWrapper<DataCollectionTaskEntity> wrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(query.getTaskName())) {
            wrapper.like("task_name", query.getTaskName());
        }
        if (StringUtils.hasText(query.getCollectionType())) {
            wrapper.eq("collection_type", query.getCollectionType());
        }
        if (StringUtils.hasText(query.getSourceType())) {
            wrapper.eq("source_type", query.getSourceType());
        }
        if (StringUtils.hasText(query.getTargetDatabase())) {
            wrapper.like("target_database", query.getTargetDatabase());
        }
        if (StringUtils.hasText(query.getTargetTable())) {
            wrapper.like("target_table", query.getTargetTable());
        }
        if (StringUtils.hasText(query.getWriteMode())) {
            wrapper.eq("write_mode", query.getWriteMode());
        }
        if (StringUtils.hasText(query.getScheduleType())) {
            wrapper.eq("schedule_type", query.getScheduleType());
        }
        if (query.getStatus() != null) {
            wrapper.eq("status", query.getStatus());
        }
        if (StringUtils.hasText(query.getCreateBy())) {
            wrapper.like("create_by", query.getCreateBy());
        }
        if (query.getBeginTime()!= null) {
            wrapper.ge("create_time", query.getBeginTime());
        }
        if (query.getEndTime()!=null) {
            wrapper.le("create_time", query.getEndTime());
        }

        wrapper.orderByDesc("create_time");

        // 分页查询
        Page<DataCollectionTaskEntity> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<DataCollectionTaskEntity> pageResult = dataCollectionTaskMapper.selectPage(page, wrapper);

        // 转换为DTO
        List<DataCollectionTaskDTO> dtoList = pageResult.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        // 填充执行统计信息
        fillExecutionStatistics(dtoList);
        return new PageDTO(page.getRecords(), page.getTotal());
    }

    @Override
    public DataCollectionTaskDTO queryTaskById(Long taskId) {
        log.info("查询数据采集任务详情: taskId={}", taskId);

        DataCollectionTaskEntity entity = dataCollectionTaskMapper.selectById(taskId);
        if (entity == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        DataCollectionTaskDTO dto = convertToDTO(entity);
        
        // 填充执行统计信息
        fillExecutionStatistics(Collections.singletonList(dto));

        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveTask(DataCollectionTaskAddCommand command) {
        log.info("新增数据采集任务: {}", command.getTaskName());

        // 检查任务名称唯一性
        DataCollectionTaskEntity existingTask = dataCollectionTaskMapper.selectByTaskName(command.getTaskName());
        if (existingTask != null) {
            throw new RuntimeException("任务名称已存在: " + command.getTaskName());
        }

        // 转换为实体
        DataCollectionTaskEntity entity = new DataCollectionTaskEntity();
        BeanUtils.copyProperties(entity, command);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        // 保存任务
        dataCollectionTaskMapper.insert(entity);

        log.info("数据采集任务创建成功: taskId={}", entity.getTaskId());
        return entity.getTaskId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTask(DataCollectionTaskAddCommand command) {
        log.info("修改数据采集任务: taskId={}", command.getTaskId());

        if (command.getTaskId() == null) {
            throw new RuntimeException("任务ID不能为空");
        }

        // 检查任务是否存在
        DataCollectionTaskEntity existingTask = dataCollectionTaskMapper.selectById(command.getTaskId());
        if (existingTask == null) {
            throw new RuntimeException("任务不存在: " + command.getTaskId());
        }

        // 检查任务名称唯一性（排除自己）
        DataCollectionTaskEntity taskWithSameName = dataCollectionTaskMapper.selectByTaskName(command.getTaskName());
        if (taskWithSameName != null && !taskWithSameName.getTaskId().equals(command.getTaskId())) {
            throw new RuntimeException("任务名称已存在: " + command.getTaskName());
        }

        // 更新任务
        DataCollectionTaskEntity entity = new DataCollectionTaskEntity();
        BeanUtils.copyProperties(entity, command);
        entity.setUpdateTime(new Date());

        dataCollectionTaskMapper.updateById(entity);

        log.info("数据采集任务修改成功: taskId={}", command.getTaskId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(List<Long> taskIds) {
        log.info("删除数据采集任务: taskIds={}", taskIds);

        if (taskIds == null || taskIds.isEmpty()) {
            throw new RuntimeException("任务ID列表不能为空");
        }

        // 检查任务是否正在执行
        for (Long taskId : taskIds) {
            // TODO: 检查任务执行状态
        }

        // 删除相关的流程定义
        for (Long taskId : taskIds) {
            flowNodeDefinitionMapper.deleteByTaskId(taskId);
            flowEdgeDefinitionMapper.deleteByTaskId(taskId);
        }

        // 删除任务
        dataCollectionTaskMapper.deleteBatchByIds(taskIds);

        log.info("数据采集任务删除成功: taskIds={}", taskIds);
    }

    @Override
    public void updateTaskStatus(Long taskId, Integer status) {
        log.info("更新任务状态: taskId={}, status={}", taskId, status);

        int result = dataCollectionTaskMapper.updateTaskStatus(taskId, status);
        if (result == 0) {
            throw new RuntimeException("任务不存在或状态更新失败: " + taskId);
        }

        log.info("任务状态更新成功: taskId={}, status={}", taskId, status);
    }

    @Override
    public String executeTask(Long taskId) {
        log.info("执行数据采集任务: taskId={}", taskId);

        // 查询任务信息
        DataCollectionTaskEntity task = dataCollectionTaskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        if (task.getStatus() != 1) {
            throw new RuntimeException("任务未启用，无法执行: " + taskId);
        }

        try {
            // 解析流程定义
            FlowDefinition flowDefinition = parseFlowDefinition(task.getFlowDefinition());
            
            // 创建执行上下文
            FlowContext context = new FlowContext();
            context.setTaskId(taskId);
            context.setFlowDefinition(flowDefinition);
            
            // 生成执行ID
            String executionId = UUID.randomUUID().toString();
            context.setExecutionId(executionId);

            // 记录执行历史
            recordExecutionStart(executionId, task);

            // 异步执行流程
            executeFlowAsync(flowDefinition, context);

            log.info("数据采集任务执行启动成功: taskId={}, executionId={}", taskId, executionId);
            return executionId;

        } catch (Exception e) {
            log.error("数据采集任务执行失败: taskId={}", taskId, e);
            throw new RuntimeException("任务执行失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void stopTask(Long taskId) {
        log.info("停止数据采集任务: taskId={}", taskId);

        // TODO: 实现任务停止逻辑
        // 1. 查找正在执行的任务
        // 2. 调用流程引擎停止执行
        // 3. 更新执行状态

        log.info("数据采集任务停止成功: taskId={}", taskId);
    }

    @Override
    public ValidationResult validateFlowDefinition(String flowDefinition) {
        log.info("验证流程定义");

        try {
            if (!StringUtils.hasText(flowDefinition)) {
                return new ValidationResult(false, "流程定义不能为空");
            }

            // 解析流程定义
            FlowDefinition flow = parseFlowDefinition(flowDefinition);

            List<String> errors = new ArrayList<>();

            // 验证节点
            if (flow.getNodes() == null || flow.getNodes().isEmpty()) {
                errors.add("流程必须包含至少一个节点");
            } else {
                // 检查开始节点
                long startNodeCount = flow.getNodes().stream()
                        .filter(node -> "START".equals(node.getType()))
                        .count();
                if (startNodeCount == 0) {
                    errors.add("流程必须包含一个开始节点");
                } else if (startNodeCount > 1) {
                    errors.add("流程只能包含一个开始节点");
                }

                // 检查结束节点
                long endNodeCount = flow.getNodes().stream()
                        .filter(node -> "END".equals(node.getType()))
                        .count();
                if (endNodeCount == 0) {
                    errors.add("流程必须包含至少一个结束节点");
                }

                // 验证节点配置
                for (FlowDefinition.FlowNode node : flow.getNodes()) {
//                    validateNodeConfig(node, errors);
                }
            }

            // 验证连线
            if (flow.getEdges() != null) {
                for (FlowDefinition.FlowEdge edge : flow.getEdges()) {
                    validateEdgeConfig(edge, flow.getNodes(), errors);
                }
            }

            if (errors.isEmpty()) {
                return new ValidationResult(true, "验证通过");
            } else {
                return new ValidationResult(false, "验证失败", errors);
            }

        } catch (Exception e) {
            log.error("流程定义验证失败", e);
            return new ValidationResult(false, "验证失败: " + e.getMessage());
        }
    }

    @Override
    public Object previewExecution(Long taskId) {
        log.info("预览流程执行: taskId={}", taskId);

        DataCollectionTaskEntity task = dataCollectionTaskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        try {
            FlowDefinition flowDefinition = parseFlowDefinition(task.getFlowDefinition());

            Map<String, Object> preview = new HashMap<>();
            preview.put("taskId", taskId);
            preview.put("taskName", task.getTaskName());
            preview.put("flowDefinition", flowDefinition);
            preview.put("nodeCount", flowDefinition.getNodes() != null ? flowDefinition.getNodes().size() : 0);
            preview.put("edgeCount", flowDefinition.getEdges() != null ? flowDefinition.getEdges().size() : 0);

            // 生成执行计划
            List<Map<String, Object>> executionPlan = generateExecutionPlan(flowDefinition);
            preview.put("executionPlan", executionPlan);

            return preview;

        } catch (Exception e) {
            log.error("预览流程执行失败: taskId={}", taskId, e);
            throw new RuntimeException("预览失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Object> getExecutionHistory(Long taskId) {
        log.info("获取执行历史: taskId={}", taskId);

        List<CollectionExecutionHistoryEntity> histories = executionHistoryMapper.selectByTaskId(taskId);

        return histories.stream()
                .map(this::convertExecutionHistoryToMap)
                .collect(Collectors.toList());
    }

    @Override
    public Object getExecutionStatus(String executionId) {
        log.info("获取执行状态: executionId={}", executionId);

        CollectionExecutionHistoryEntity history = executionHistoryMapper.selectByExecutionId(executionId);
        if (history == null) {
            throw new RuntimeException("执行记录不存在: " + executionId);
        }

        Map<String, Object> status = new HashMap<>();
        status.put("executionId", executionId);
        status.put("taskId", history.getTaskId());
        status.put("taskName", history.getTaskName());
        status.put("status", history.getStatus());
        status.put("startTime", history.getStartTime());
        status.put("endTime", history.getEndTime());
        status.put("duration", history.getDuration());
        status.put("processedRecords", history.getProcessedRecords());
        status.put("errorRecords", history.getErrorRecords());
        status.put("errorMessage", history.getErrorMessage());

        // 计算进度
        if ("RUNNING".equals(history.getStatus())) {
            // TODO: 计算实际进度
            status.put("progress", 50);
        } else {
            status.put("progress", "SUCCESS".equals(history.getStatus()) ? 100 : 0);
        }

        return status;
    }

    @Override
    public String getExecutionLog(String executionId) {
        log.info("获取执行日志: executionId={}", executionId);

        CollectionExecutionHistoryEntity history = executionHistoryMapper.selectByExecutionId(executionId);
        if (history == null) {
            throw new RuntimeException("执行记录不存在: " + executionId);
        }

        return history.getExecutionLog();
    }

    /**
     * 转换为DTO
     */
    private DataCollectionTaskDTO convertToDTO(DataCollectionTaskEntity entity) {
        DataCollectionTaskDTO dto = new DataCollectionTaskDTO();
        BeanUtils.copyProperties(dto, entity);
        return dto;
    }

    /**
     * 填充执行统计信息
     */
    private void fillExecutionStatistics(List<DataCollectionTaskDTO> dtoList) {
        for (DataCollectionTaskDTO dto : dtoList) {
            try {
                // 获取最新执行记录
                CollectionExecutionHistoryEntity latestExecution = executionHistoryMapper.selectLatestByTaskId(dto.getTaskId());
                if (latestExecution != null) {
                    dto.setLastExecuteTime(latestExecution.getStartTime());
                    dto.setLastExecuteStatus(latestExecution.getStatus());
                }

                // 获取执行统计
                CollectionExecutionHistoryMapper.ExecutionStatistics statistics =
                        executionHistoryMapper.selectExecutionStatistics(dto.getTaskId());
                if (statistics != null) {
                    dto.setTotalExecuteCount(statistics.getTotalCount());
                    dto.setSuccessExecuteCount(statistics.getSuccessCount());
                    dto.setFailedExecuteCount(statistics.getFailedCount());
                }
            } catch (Exception e) {
                log.warn("填充执行统计信息失败: taskId={}", dto.getTaskId(), e);
            }
        }
    }

    /**
     * 解析流程定义
     */
    private FlowDefinition parseFlowDefinition(String flowDefinitionJson) {
        if (!StringUtils.hasText(flowDefinitionJson)) {
            return new FlowDefinition();
        }

        try {
            return objectMapper.readValue(flowDefinitionJson, FlowDefinition.class);
        } catch (JsonProcessingException e) {
            log.error("解析流程定义失败", e);
            throw new RuntimeException("流程定义格式错误: " + e.getMessage(), e);
        }
    }

    /**
     * 记录执行开始
     */
    private void recordExecutionStart(String executionId, DataCollectionTaskEntity task) {
        CollectionExecutionHistoryEntity history = new CollectionExecutionHistoryEntity();
        history.setExecutionId(executionId);
        history.setTaskId(task.getTaskId());
        history.setTaskName(task.getTaskName());
        history.setExecutionType("MANUAL");
        history.setStartTime(LocalDateTime.now());
        history.setStatus("RUNNING");
        history.setProcessedRecords(0L);
        history.setErrorRecords(0L);
        history.setCreateTime(LocalDateTime.now());

        executionHistoryMapper.insert(history);
    }

    /**
     * 异步执行流程
     */
    private void executeFlowAsync(FlowDefinition flowDefinition, FlowContext context) {
        // 这里应该使用线程池异步执行
        // 为了简化，这里使用同步执行
        try {
            FlowEngine.FlowExecutionResult result = flowEngine.execute(flowDefinition, context);

            // 更新执行结果
            updateExecutionResult(context.getExecutionId(), result);

        } catch (Exception e) {
            log.error("流程执行失败: executionId={}", context.getExecutionId(), e);
            updateExecutionError(context.getExecutionId(), e.getMessage());
        }
    }

    /**
     * 更新执行结果
     */
    private void updateExecutionResult(String executionId, FlowEngine.FlowExecutionResult result) {
        CollectionExecutionHistoryEntity history = new CollectionExecutionHistoryEntity();
        history.setExecutionId(executionId);
        history.setEndTime(LocalDateTime.now());
        history.setStatus(result.getStatus());

        if (result.getContext() != null) {
            history.setDuration(result.getContext().getEndTime() != null ?
                    java.time.Duration.between(result.getContext().getStartTime(), result.getContext().getEndTime()).toMillis() : null);
            history.setExecutionLog(result.getContext().getExecutionLog().toString());
        }

        executionHistoryMapper.updateById(history);
    }

    /**
     * 更新执行错误
     */
    private void updateExecutionError(String executionId, String errorMessage) {
        CollectionExecutionHistoryEntity history = new CollectionExecutionHistoryEntity();
        history.setExecutionId(executionId);
        history.setEndTime(LocalDateTime.now());
        history.setStatus("FAILED");
        history.setErrorMessage(errorMessage);

        executionHistoryMapper.updateById(history);
    }

    /**
     * 验证连线配置
     */
    private void validateEdgeConfig(FlowDefinition.FlowEdge edge, List<FlowDefinition.FlowNode> nodes, List<String> errors) {
        if (!StringUtils.hasText(edge.getSourceNodeId())) {
            errors.add("连线源节点不能为空: " + edge.getId());
        }
        if (!StringUtils.hasText(edge.getTargetNodeId())) {
            errors.add("连线目标节点不能为空: " + edge.getId());
        }

        // 检查节点是否存在
        boolean sourceExists = nodes.stream().anyMatch(node -> node.getId().equals(edge.getSourceNodeId()));
        boolean targetExists = nodes.stream().anyMatch(node -> node.getId().equals(edge.getTargetNodeId()));

        if (!sourceExists) {
            errors.add("连线源节点不存在: " + edge.getSourceNodeId());
        }
        if (!targetExists) {
            errors.add("连线目标节点不存在: " + edge.getTargetNodeId());
        }
    }

    /**
     * 验证API调用节点配置
     */
    private void validateApiCallNodeConfig(FlowDefinition.FlowNode node, List<String> errors) {
        Map<String, Object> config = node.getConfig();
        if (config == null) {
            errors.add("API调用节点配置不能为空: " + node.getId());
            return;
        }

        if (!StringUtils.hasText((String) config.get("url"))) {
            errors.add("API调用节点URL不能为空: " + node.getId());
        }
    }

    /**
     * 验证SQL查询节点配置
     */
    private void validateSqlQueryNodeConfig(FlowDefinition.FlowNode node, List<String> errors) {
        Map<String, Object> config = node.getConfig();
        if (config == null) {
            errors.add("SQL查询节点配置不能为空: " + node.getId());
            return;
        }

        if (!StringUtils.hasText((String) config.get("sql"))) {
            errors.add("SQL查询节点SQL语句不能为空: " + node.getId());
        }
        if (!StringUtils.hasText((String) config.get("dataSource"))) {
            errors.add("SQL查询节点数据源不能为空: " + node.getId());
        }
    }

    /**
     * 生成执行计划
     */
    private List<Map<String, Object>> generateExecutionPlan(FlowDefinition flowDefinition) {
        List<Map<String, Object>> plan = new ArrayList<>();

        if (flowDefinition.getNodes() != null) {
            for (FlowDefinition.FlowNode node : flowDefinition.getNodes()) {
                Map<String, Object> step = new HashMap<>();
                step.put("nodeId", node.getId());
                step.put("nodeName", node.getName());
                step.put("nodeType", node.getType());
                step.put("description", getNodeDescription(node));
                plan.add(step);
            }
        }

        return plan;
    }

    /**
     * 获取节点描述
     */
    private String getNodeDescription(FlowDefinition.FlowNode node) {
        switch (node.getType()) {
            case "START":
                return "流程开始";
            case "END":
                return "流程结束";
            case "API_CALL":
                return "调用API接口获取数据";
            case "SQL_QUERY":
                return "执行SQL查询获取数据";
            case "LOOP":
                return "循环执行子流程";
            case "CONDITION":
                return "条件判断分支";
            case "DATA_TRANSFORM":
                return "数据格式转换";
            case "DATA_WRITE":
                return "写入目标数据库";
            default:
                return "未知节点类型";
        }
    }

    /**
     * 转换执行历史为Map
     */
    private Map<String, Object> convertExecutionHistoryToMap(CollectionExecutionHistoryEntity history) {
        Map<String, Object> map = new HashMap<>();
        map.put("executionId", history.getExecutionId());
        map.put("taskId", history.getTaskId());
        map.put("taskName", history.getTaskName());
        map.put("executionType", history.getExecutionType());
        map.put("startTime", history.getStartTime());
        map.put("endTime", history.getEndTime());
        map.put("duration", history.getDuration());
        map.put("status", history.getStatus());
        map.put("processedRecords", history.getProcessedRecords());
        map.put("errorRecords", history.getErrorRecords());
        map.put("errorMessage", history.getErrorMessage());
        return map;
    }
}
