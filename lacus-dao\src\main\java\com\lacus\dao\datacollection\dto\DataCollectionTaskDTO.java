package com.lacus.dao.datacollection.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据采集任务DTO
 *
 * <AUTHOR>
 */
@Data
public class DataCollectionTaskDTO {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 采集类型：API、SQL
     */
    private String collectionType;

    /**
     * 数据源类型：MYSQL、SQLSERVER、API
     */
    private String sourceType;

    /**
     * 数据源配置JSON
     */
    private String sourceConfig;

    /**
     * 目标数据库
     */
    private String targetDatabase;

    /**
     * 目标表名
     */
    private String targetTable;

    /**
     * 写入模式：OVERWRITE、APPEND、UPSERT、AUTO_CREATE
     */
    private String writeMode;

    /**
     * 流程定义JSON
     */
    private String flowDefinition;

    /**
     * 调度类型：MANUAL、CRON、REALTIME
     */
    private String scheduleType;

    /**
     * Cron表达式
     */
    private String cronExpression;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 最后执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastExecuteTime;

    /**
     * 最后执行状态
     */
    private String lastExecuteStatus;

    /**
     * 总执行次数
     */
    private Long totalExecuteCount;

    /**
     * 成功执行次数
     */
    private Long successExecuteCount;

    /**
     * 失败执行次数
     */
    private Long failedExecuteCount;

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) {
            return "未知";
        }
        return status == 1 ? "启用" : "禁用";
    }

    /**
     * 获取采集类型描述
     */
    public String getCollectionTypeDesc() {
        if (collectionType == null) {
            return "未知";
        }
        switch (collectionType) {
            case "API":
                return "API接口";
            case "SQL":
                return "SQL查询";
            default:
                return collectionType;
        }
    }

    /**
     * 获取数据源类型描述
     */
    public String getSourceTypeDesc() {
        if (sourceType == null) {
            return "未知";
        }
        switch (sourceType) {
            case "MYSQL":
                return "MySQL数据库";
            case "SQLSERVER":
                return "SQL Server数据库";
            case "API":
                return "API接口";
            case "ORACLE":
                return "Oracle数据库";
            default:
                return sourceType;
        }
    }

    /**
     * 获取写入模式描述
     */
    public String getWriteModeDesc() {
        if (writeMode == null) {
            return "未知";
        }
        switch (writeMode) {
            case "OVERWRITE":
                return "覆盖写入";
            case "APPEND":
                return "追加写入";
            case "UPSERT":
                return "更新插入";
            case "AUTO_CREATE":
                return "自动建表";
            default:
                return writeMode;
        }
    }

    /**
     * 获取调度类型描述
     */
    public String getScheduleTypeDesc() {
        if (scheduleType == null) {
            return "未知";
        }
        switch (scheduleType) {
            case "MANUAL":
                return "手动执行";
            case "CRON":
                return "定时调度";
            case "REALTIME":
                return "实时同步";
            default:
                return scheduleType;
        }
    }
}
