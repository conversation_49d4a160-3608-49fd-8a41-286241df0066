package com.lacus.dao.datacollection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lacus.dao.datacollection.entity.CollectionExecutionHistoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采集执行历史Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CollectionExecutionHistoryMapper extends BaseMapper<CollectionExecutionHistoryEntity> {

    /**
     * 根据执行ID查询
     *
     * @param executionId 执行ID
     * @return 执行历史
     */
    CollectionExecutionHistoryEntity selectByExecutionId(@Param("executionId") String executionId);

    /**
     * 根据任务ID查询执行历史列表
     *
     * @param taskId 任务ID
     * @return 执行历史列表
     */
    List<CollectionExecutionHistoryEntity> selectByTaskId(@Param("taskId") Long taskId);

    /**
     * 查询任务的最新执行记录
     *
     * @param taskId 任务ID
     * @return 最新执行记录
     */
    CollectionExecutionHistoryEntity selectLatestByTaskId(@Param("taskId") Long taskId);

    /**
     * 统计任务执行次数
     *
     * @param taskId 任务ID
     * @return 执行次数统计
     */
    ExecutionStatistics selectExecutionStatistics(@Param("taskId") Long taskId);

    /**
     * 执行统计信息
     */
    class ExecutionStatistics {
        private Long totalCount;
        private Long successCount;
        private Long failedCount;

        // Getters and Setters
        public Long getTotalCount() { return totalCount; }
        public void setTotalCount(Long totalCount) { this.totalCount = totalCount; }
        public Long getSuccessCount() { return successCount; }
        public void setSuccessCount(Long successCount) { this.successCount = successCount; }
        public Long getFailedCount() { return failedCount; }
        public void setFailedCount(Long failedCount) { this.failedCount = failedCount; }
    }
}
