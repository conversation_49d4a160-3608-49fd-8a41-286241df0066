<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lacus.datacollection.mapper.DataCollectionTaskMapper">
    
    <resultMap type="DataCollectionTask" id="DataCollectionTaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="taskCode"    column="task_code"    />
        <result property="categoryId"    column="category_id"    />
        <result property="taskDesc"    column="task_desc"    />
        <result property="collectionType"    column="collection_type"    />
        <result property="sourceType"    column="source_type"    />
        <result property="targetDatabase"    column="target_database"    />
        <result property="targetTable"    column="target_table"    />
        <result property="writeMode"    column="write_mode"    />
        <result property="scheduleType"    column="schedule_type"    />
        <result property="cronExpression"    column="cron_expression"    />
        <result property="flowDefinition"    column="flow_definition"    />
        <result property="taskConfig"    column="task_config"    />
        <result property="priority"    column="priority"    />
        <result property="timeout"    column="timeout"    />
        <result property="retryCount"    column="retry_count"    />
        <result property="status"    column="status"    />
        <result property="version"    column="version"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="categoryName"    column="category_name"    />
    </resultMap>

    <sql id="selectDataCollectionTaskVo">
        select t.task_id, t.task_name, t.task_code, t.category_id, t.task_desc, t.collection_type, t.source_type, 
               t.target_database, t.target_table, t.write_mode, t.schedule_type, t.cron_expression, t.flow_definition, 
               t.task_config, t.priority, t.timeout, t.retry_count, t.status, t.version, t.create_by, t.create_time, 
               t.update_by, t.update_time, t.remark, c.category_name
        from data_collection_task t
        left join data_collection_category c on t.category_id = c.category_id
    </sql>

    <select id="selectDataCollectionTaskList" parameterType="DataCollectionTask" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        <where>  
            <if test="taskName != null  and taskName != ''"> and t.task_name like concat('%', #{taskName}, '%')</if>
            <if test="taskCode != null  and taskCode != ''"> and t.task_code = #{taskCode}</if>
            <if test="categoryId != null "> and t.category_id = #{categoryId}</if>
            <if test="collectionType != null  and collectionType != ''"> and t.collection_type = #{collectionType}</if>
            <if test="sourceType != null  and sourceType != ''"> and t.source_type = #{sourceType}</if>
            <if test="scheduleType != null  and scheduleType != ''"> and t.schedule_type = #{scheduleType}</if>
            <if test="status != null "> and t.status = #{status}</if>
        </where>
        order by t.create_time desc
    </select>
    
    <select id="selectDataCollectionTaskByTaskId" parameterType="Long" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.task_id = #{taskId}
    </select>
        
    <insert id="insertDataCollectionTask" parameterType="DataCollectionTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into data_collection_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="taskDesc != null">task_desc,</if>
            <if test="collectionType != null and collectionType != ''">collection_type,</if>
            <if test="sourceType != null and sourceType != ''">source_type,</if>
            <if test="targetDatabase != null and targetDatabase != ''">target_database,</if>
            <if test="targetTable != null and targetTable != ''">target_table,</if>
            <if test="writeMode != null and writeMode != ''">write_mode,</if>
            <if test="scheduleType != null and scheduleType != ''">schedule_type,</if>
            <if test="cronExpression != null">cron_expression,</if>
            <if test="flowDefinition != null">flow_definition,</if>
            <if test="taskConfig != null">task_config,</if>
            <if test="priority != null">priority,</if>
            <if test="timeout != null">timeout,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="status != null">status,</if>
            <if test="version != null">version,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="taskDesc != null">#{taskDesc},</if>
            <if test="collectionType != null and collectionType != ''">#{collectionType},</if>
            <if test="sourceType != null and sourceType != ''">#{sourceType},</if>
            <if test="targetDatabase != null and targetDatabase != ''">#{targetDatabase},</if>
            <if test="targetTable != null and targetTable != ''">#{targetTable},</if>
            <if test="writeMode != null and writeMode != ''">#{writeMode},</if>
            <if test="scheduleType != null and scheduleType != ''">#{scheduleType},</if>
            <if test="cronExpression != null">#{cronExpression},</if>
            <if test="flowDefinition != null">#{flowDefinition},</if>
            <if test="taskConfig != null">#{taskConfig},</if>
            <if test="priority != null">#{priority},</if>
            <if test="timeout != null">#{timeout},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="status != null">#{status},</if>
            <if test="version != null">#{version},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDataCollectionTask" parameterType="DataCollectionTask">
        update data_collection_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="taskDesc != null">task_desc = #{taskDesc},</if>
            <if test="collectionType != null and collectionType != ''">collection_type = #{collectionType},</if>
            <if test="sourceType != null and sourceType != ''">source_type = #{sourceType},</if>
            <if test="targetDatabase != null and targetDatabase != ''">target_database = #{targetDatabase},</if>
            <if test="targetTable != null and targetTable != ''">target_table = #{targetTable},</if>
            <if test="writeMode != null and writeMode != ''">write_mode = #{writeMode},</if>
            <if test="scheduleType != null and scheduleType != ''">schedule_type = #{scheduleType},</if>
            <if test="cronExpression != null">cron_expression = #{cronExpression},</if>
            <if test="flowDefinition != null">flow_definition = #{flowDefinition},</if>
            <if test="taskConfig != null">task_config = #{taskConfig},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="timeout != null">timeout = #{timeout},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="version != null">version = #{version},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteDataCollectionTaskByTaskId" parameterType="Long">
        delete from data_collection_task where task_id = #{taskId}
    </delete>

    <delete id="deleteDataCollectionTaskByTaskIds" parameterType="String">
        delete from data_collection_task where task_id in 
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>

    <!-- 分类相关查询 -->
    <select id="selectTaskCountByCategory" parameterType="Long" resultType="int">
        select count(1) from data_collection_task where category_id = #{categoryId}
    </select>

    <select id="selectTaskStatusStatsByCategory" parameterType="Long" resultType="java.util.Map">
        select 
            status,
            count(*) as count
        from data_collection_task 
        where category_id = #{categoryId}
        group by status
    </select>

    <select id="checkTaskCodeUnique" parameterType="String" resultMap="DataCollectionTaskResult">
        select task_id, task_name, task_code from data_collection_task where task_code = #{taskCode} limit 1
    </select>

    <select id="checkTaskNameUnique" resultMap="DataCollectionTaskResult">
        select task_id, task_name, task_code from data_collection_task 
        where task_name = #{taskName} and category_id = #{categoryId} limit 1
    </select>

    <update id="updateTaskStatus">
        update data_collection_task set status = #{status} where task_id = #{taskId}
    </update>

    <select id="selectTaskByCode" parameterType="String" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.task_code = #{taskCode} limit 1
    </select>

    <select id="selectTaskByName" parameterType="String" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.task_name = #{taskName} limit 1
    </select>

    <select id="selectEnabledTasks" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.status = 1
        order by t.priority desc, t.create_time desc
    </select>

    <select id="selectTasksByScheduleType" parameterType="String" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.schedule_type = #{scheduleType} and t.status = 1
        order by t.priority desc, t.create_time desc
    </select>

    <select id="selectTasksByCollectionType" parameterType="String" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.collection_type = #{collectionType} and t.status = 1
        order by t.create_time desc
    </select>

    <select id="selectTasksBySourceType" parameterType="String" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.source_type = #{sourceType} and t.status = 1
        order by t.create_time desc
    </select>

    <update id="batchUpdateTaskStatus">
        update data_collection_task set status = #{status} where task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>

    <update id="batchMoveTasksToCategory">
        update data_collection_task set category_id = #{categoryId} where task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>

    <select id="selectTaskStats" resultType="java.util.Map">
        select 
            count(*) as total_count,
            sum(case when status = 1 then 1 else 0 end) as enabled_count,
            sum(case when status = 0 then 1 else 0 end) as disabled_count,
            count(distinct category_id) as category_count,
            count(distinct collection_type) as collection_type_count
        from data_collection_task
    </select>

    <select id="selectTasksByCreateTime" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.create_time between #{startTime} and #{endTime}
        order by t.create_time desc
    </select>

    <select id="selectTaskCreateStats" resultType="java.util.Map">
        select DATE(create_time) as date, count(*) as count
        from data_collection_task
        where create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        group by DATE(create_time)
        order by date
    </select>

    <select id="selectTasksByTargetDatabase" parameterType="String" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.target_database = #{targetDatabase}
        order by t.create_time desc
    </select>

    <select id="selectTasksByTargetTable" parameterType="String" resultMap="DataCollectionTaskResult">
        <include refid="selectDataCollectionTaskVo"/>
        where t.target_table = #{targetTable}
        order by t.create_time desc
    </select>

    <update id="updateTaskVersion" parameterType="Long">
        update data_collection_task set version = version + 1 where task_id = #{taskId}
    </update>

</mapper>
