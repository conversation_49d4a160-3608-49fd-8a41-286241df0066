package com.lacus.service.datawarehouse.engine;

import com.lacus.dao.datawarehouse.entity.EtlTaskEntity;
import com.lacus.service.datawarehouse.EtlTaskService;
import com.lacus.service.datawarehouse.model.EtlTaskModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * ETL调度启动监听器
 * 在应用启动时恢复已启用的ETL任务调度
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class EtlScheduleStartupListener implements ApplicationRunner {
    
    @Autowired
    private EtlTaskService etlTaskService;
    
    @Autowired
    private EtlScheduleManager etlScheduleManager;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始恢复ETL任务调度...");
        
        try {
            // 查询所有已启用的ETL任务
            List<EtlTaskModel> enabledTasks = etlTaskService.queryTasksByStatus(1);
            
            log.info("发现{}个已启用的ETL任务", enabledTasks.size());
            
            int cronTaskCount = 0;
            int realtimeTaskCount = 0;
            
            for (EtlTaskModel taskModel : enabledTasks) {
                try {
                    // 转换为实体对象
                    EtlTaskEntity taskEntity = convertToEntity(taskModel);
                    
                    // 根据调度类型启动相应的调度
                    String scheduleType = taskEntity.getScheduleType();
                    
                    if ("CRON".equals(scheduleType)) {
                        etlScheduleManager.startSchedule(taskEntity);
                        cronTaskCount++;
                        log.info("恢复定时ETL任务: taskId={}, taskName={}, cron={}", 
                                taskEntity.getTaskId(), taskEntity.getTaskName(), taskEntity.getCronExpression());
                                
                    } else if ("REALTIME".equals(scheduleType)) {
                        etlScheduleManager.startSchedule(taskEntity);
                        realtimeTaskCount++;
                        log.info("恢复实时ETL任务: taskId={}, taskName={}", 
                                taskEntity.getTaskId(), taskEntity.getTaskName());
                    }
                    // MANUAL类型的任务不需要启动调度
                    
                } catch (Exception e) {
                    log.error("恢复ETL任务调度失败: taskId={}, taskName={}", 
                            taskModel.getTaskId(), taskModel.getTaskName(), e);
                }
            }
            
            log.info("ETL任务调度恢复完成: 定时任务{}个, 实时任务{}个", cronTaskCount, realtimeTaskCount);
            
        } catch (Exception e) {
            log.error("恢复ETL任务调度异常", e);
        }
    }
    
    /**
     * 转换模型为实体
     */
    private EtlTaskEntity convertToEntity(EtlTaskModel model) {
        EtlTaskEntity entity = new EtlTaskEntity();
        entity.setTaskId(model.getTaskId());
        entity.setTaskName(model.getTaskName());
        entity.setDescription(model.getDescription());
        entity.setSourceLayer(model.getSourceLayer());
        entity.setTargetLayer(model.getTargetLayer());
        entity.setScheduleType(model.getScheduleType());
        entity.setCronExpression(model.getCronExpression());
        entity.setTimezone(model.getTimezone());
        entity.setTargetTable(model.getTargetTable());
        entity.setWriteMode(model.getWriteMode());
        entity.setStatus(model.getStatus());
        return entity;
    }
}
