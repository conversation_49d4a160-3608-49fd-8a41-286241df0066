package com.lacus.dao.datacollection.vo;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lacus.dao.flink.entity.FlinkJobEntity;
import com.lacus.dao.system.query.AbstractPageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 数据采集任务分页查询VO
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataCollectionTaskPageQuery extends AbstractPageQuery {

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务编码
     */
    private String taskCode;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 采集类型
     */
    private String collectionType;

    /**
     * 数据源类型
     */
    private String sourceType;

    /**
     * 调度类型
     */
    private String scheduleType;

    /**
     * 状态(0:禁用 1:启用)
     */
    private Integer status;

    /**
     * 目标数据库
     */
    private String targetDatabase;

    /**
     * 目标表
     */
    private String targetTable;

    /**
     * 写入模式
     */
    private String writeMode;

    /**
     * 创建时间范围 - 开始时间
     */
    private String createTimeStart;

    /**
     * 创建时间范围 - 结束时间
     */
    private String createTimeEnd;

    /**
     * 最后执行时间范围 - 开始时间
     */
    private String lastExecuteTimeStart;

    /**
     * 最后执行时间范围 - 结束时间
     */
    private String lastExecuteTimeEnd;

    /**
     * 最后执行状态
     */
    private String lastExecuteStatus;

    /**
     * 是否只查询启用状态
     */
    private Boolean onlyEnabled;

    /**
     * 是否只查询正在运行的任务
     */
    private Boolean onlyRunning;

    /**
     * 优先级范围 - 最小值
     */
    private Integer priorityMin;

    /**
     * 优先级范围 - 最大值
     */
    private Integer priorityMax;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向
     */
    private String orderDirection;

    /**
     * 关键词搜索（任务名称或编码）
     */
    private String keyword;

    @Override
    public QueryWrapper toQueryWrapper() {
        QueryWrapper<FlinkJobEntity> wrapper = new QueryWrapper<>();
        wrapper.like(ObjectUtils.isNotEmpty(taskName), "task_name", taskName);
        wrapper.eq(ObjectUtils.isNotEmpty(status), "status", status);
        wrapper.orderByDesc("create_time");
        return wrapper;
    }
}
