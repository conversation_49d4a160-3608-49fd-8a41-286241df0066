# 数据仓库ETL后端接口实现

## 概述

本文档描述了数据仓库ETL系统的后端接口实现，基于SpringBoot + DDD架构模式，支持ODS→DWD→DWS→ADS四层数据仓库的ETL任务管理、数据质量监控和调度管理。

## 重要说明

### JDK版本兼容性
- 本项目基于 **JDK 1.8** 开发
- 所有代码已经过JDK 1.8兼容性检查和修改
- 避免使用JDK 9+的新特性（如var关键字、Map.of()等）

### 控制器命名调整
- 由于框架已存在 `TableController.java`，元数据管理相关接口已移至：
  - `NewTableController.java` - 表元数据管理
  - `NewDatasourceController.java` - 数据源管理
  - `NewDatabaseController.java` - 数据库管理

## 架构设计

### 1. 分层架构
```
lacus-admin/          # 控制层 - REST API接口
├── controller/
│   ├── datawarehouse/
│   │   ├── EtlTaskController.java        # ETL任务管理
│   │   ├── QualityMonitorController.java # 数据质量监控
│   │   └── ScheduleController.java       # 调度管理
│   └── metadata/
│       └── TableController.java          # 元数据管理

lacus-domain/         # 领域层 - 业务逻辑
├── datawarehouse/
│   ├── etl/
│   │   ├── model/                        # 领域模型
│   │   ├── command/                      # 命令对象
│   │   ├── query/                        # 查询对象
│   │   ├── dto/                          # 数据传输对象
│   │   ├── EtlTaskBusiness.java          # 业务逻辑
│   │   ├── EtlTaskRepository.java        # 仓储接口
│   │   └── EtlExecutionService.java      # 执行服务接口
│   ├── quality/                          # 数据质量模块
│   └── schedule/                         # 调度管理模块
```

### 2. 核心组件

#### ETL任务管理
- **EtlTaskModel**: ETL任务领域模型，包含任务配置、状态管理等
- **EtlTaskBusiness**: ETL任务业务逻辑，处理CRUD操作和执行控制
- **EtlExecutionService**: ETL执行服务，负责任务提交、监控等

#### 数据质量监控
- **QualityMonitorModel**: 质量监控领域模型
- **QualityMonitorBusiness**: 质量监控业务逻辑
- **QualityCheckService**: 质量检查服务

#### 调度管理
- **ScheduleJobModel**: 调度任务领域模型
- **ScheduleController**: 调度管理控制器

## API接口说明

### 1. ETL任务管理接口

#### 基础CRUD操作
```http
GET    /datawarehouse/etl/list              # 查询ETL任务列表
GET    /datawarehouse/etl/{taskId}          # 获取ETL任务详情
POST   /datawarehouse/etl                   # 新增ETL任务
PUT    /datawarehouse/etl                   # 更新ETL任务
DELETE /datawarehouse/etl/{taskIds}         # 删除ETL任务
```

#### 任务执行控制
```http
POST   /datawarehouse/etl/run/{taskId}      # 执行ETL任务
POST   /datawarehouse/etl/stop/{taskId}     # 停止ETL任务
PUT    /datawarehouse/etl/status            # 更新任务状态
POST   /datawarehouse/etl/batch-run         # 批量执行任务
```

#### 辅助功能
```http
GET    /datawarehouse/etl/history/{taskId}  # 获取执行历史
GET    /datawarehouse/etl/log/{executionId} # 获取执行日志
GET    /datawarehouse/etl/stats             # 获取统计信息
POST   /datawarehouse/etl/preview           # 预览ETL结果
POST   /datawarehouse/etl/mapping-suggestions # 获取字段映射建议
```

### 2. 数据质量监控接口

#### 质量监控管理
```http
GET    /datawarehouse/quality/monitor/list  # 查询质量监控列表
GET    /datawarehouse/quality/monitor/{id}  # 获取质量监控详情
POST   /datawarehouse/quality/check/{id}    # 执行质量检查
POST   /datawarehouse/quality/batch-check   # 批量质量检查
```

#### 质量分析
```http
GET    /datawarehouse/quality/stats         # 获取质量统计
GET    /datawarehouse/quality/trend         # 获取质量趋势
GET    /datawarehouse/quality/report/{id}   # 获取质量报告
GET    /datawarehouse/quality/score/{id}    # 获取质量评分详情
```

#### 异常处理
```http
GET    /datawarehouse/quality/issues/{id}   # 获取质量异常
POST   /datawarehouse/quality/issues/{id}/handle # 处理质量异常
```

### 3. 调度管理接口

#### 调度任务管理
```http
GET    /datawarehouse/schedule/list         # 查询调度任务列表
GET    /datawarehouse/schedule/{id}         # 获取调度任务详情
POST   /datawarehouse/schedule              # 新增调度任务
PUT    /datawarehouse/schedule              # 更新调度任务
DELETE /datawarehouse/schedule/{ids}        # 删除调度任务
```

#### 调度控制
```http
POST   /datawarehouse/schedule/trigger/{id} # 触发调度任务
POST   /datawarehouse/schedule/pause/{id}   # 暂停调度任务
POST   /datawarehouse/schedule/resume/{id}  # 恢复调度任务
```

#### 调度监控
```http
GET    /datawarehouse/schedule/stats        # 获取调度统计
GET    /datawarehouse/schedule/today        # 获取今日调度
GET    /datawarehouse/schedule/calendar     # 获取日历调度数据
```

### 4. 元数据管理接口

#### 表元数据
```http
GET    /metadata/table/list                 # 查询表列表
GET    /metadata/table/by-layer             # 根据层级获取表列表
GET    /metadata/table/fields/{tableName}   # 获取表字段信息
GET    /metadata/table/stats/{tableName}    # 获取表统计信息
```

#### 数据源管理
```http
GET    /metadata/datasource/list            # 获取数据源列表
GET    /metadata/database/list              # 获取数据库列表
```

## 数据模型设计

### 1. ETL任务模型
```java
public class EtlTaskModel {
    private Long taskId;                    // 任务ID
    private String taskName;                // 任务名称
    private String sourceLayer;             // 源层级 (ODS/DWD/DWS)
    private String targetLayer;             // 目标层级 (DWD/DWS/ADS)
    private String scheduleType;            // 调度类型 (MANUAL/CRON/REALTIME)
    private String targetTable;             // 目标表名
    private String writeMode;               // 写入模式 (OVERWRITE/APPEND/UPSERT)
    private String sourceTablesConfig;      // 源表配置(JSON)
    private String fieldMappingsConfig;     // 字段映射配置(JSON)
    private String qualityRulesConfig;      // 质量规则配置(JSON)
    private Integer status;                 // 任务状态
    private LocalDateTime lastRunTime;      // 最后执行时间
    // ... 其他字段
}
```

### 2. 质量监控模型
```java
public class QualityMonitorModel {
    private Long monitorId;                 // 监控ID
    private String tableName;               // 表名
    private String dataLayer;               // 数据层级
    private Integer qualityScore;           // 质量得分
    private String qualityStatus;           // 质量状态 (PASSED/WARNING/FAILED)
    private Integer ruleCount;              // 规则数量
    private Integer failedRuleCount;        // 异常规则数量
    private String rulesConfig;             // 质量规则配置(JSON)
    // ... 其他字段
}
```

## 权限控制

所有接口都使用Spring Security进行权限控制：

```java
@PreAuthorize("@permission.has('datawarehouse:etl:list')")    // ETL任务查询权限
@PreAuthorize("@permission.has('datawarehouse:etl:add')")     // ETL任务新增权限
@PreAuthorize("@permission.has('datawarehouse:etl:edit')")    // ETL任务编辑权限
@PreAuthorize("@permission.has('datawarehouse:etl:remove')")  // ETL任务删除权限
@PreAuthorize("@permission.has('datawarehouse:etl:run')")     // ETL任务执行权限
```

## 实现要点

### 1. 配置存储
- 使用JSON格式存储复杂配置（源表配置、字段映射、质量规则等）
- 运行时解析JSON为对象进行业务处理

### 2. 状态管理
- ETL任务状态：启用/禁用
- 执行状态：等待/运行中/成功/失败
- 调度状态：启用/禁用/暂停

### 3. 异步执行
- ETL任务执行采用异步模式
- 返回执行ID供前端查询状态和日志

### 4. 扩展性设计
- 使用接口定义核心服务，便于不同实现
- 支持多种数据源和执行引擎
- 配置化的质量规则和调度策略

## 部署说明

### 1. 数据库表结构
需要创建以下核心表：
- `etl_task`: ETL任务表
- `etl_execution_history`: ETL执行历史表
- `quality_monitor`: 质量监控表
- `quality_check_history`: 质量检查历史表
- `schedule_job`: 调度任务表

### 2. 依赖服务
- Flink集群：用于实时ETL处理
- Spark集群：用于批量ETL处理
- Doris集群：数据仓库存储
- 调度引擎：如Quartz或XXL-Job

### 3. 配置项
```yaml
datawarehouse:
  flink:
    cluster-url: http://flink-cluster:8081
  spark:
    cluster-url: spark://spark-master:7077
  doris:
    fe-nodes: doris-fe1:9030,doris-fe2:9030
```

## 后续扩展

1. **实现Repository具体类**：基于MyBatis或JPA实现数据访问
2. **实现ExecutionService**：集成Flink/Spark任务提交
3. **实现QualityCheckService**：基于SQL的质量检查逻辑
4. **添加监控告警**：集成监控系统和告警通知
5. **性能优化**：添加缓存、连接池等优化措施
