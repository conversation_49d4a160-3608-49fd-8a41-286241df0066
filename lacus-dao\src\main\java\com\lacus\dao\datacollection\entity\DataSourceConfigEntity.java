package com.lacus.dao.datacollection.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lacus.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据源配置实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("data_source_config")
public class DataSourceConfigEntity extends BaseEntity {

    /**
     * 数据源ID
     */
    @TableId(value = "source_id", type = IdType.AUTO)
    private Long sourceId;

    /**
     * 数据源名称
     */
    @TableField("source_name")
    private String sourceName;

    /**
     * 数据源类型：MYSQL、SQLSERVER、API、ORACLE
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 连接配置JSON
     */
    @TableField("connection_config")
    private String connectionConfig;

    /**
     * 测试SQL
     */
    @TableField("test_sql")
    private String testSql;

    /**
     * 状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;
}
