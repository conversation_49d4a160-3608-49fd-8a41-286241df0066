package com.lacus.dao.datacollection.vo;

import lombok.Data;

import java.util.List;

/**
 * 数据源表VO
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class DataSourceTableVO {

    /**
     * 表名
     */
    private String tableName;

    /**
     * 表注释
     */
    private String tableComment;

    /**
     * 表类型
     */
    private String tableType;

    /**
     * 数据库名
     */
    private String databaseName;

    /**
     * 模式名
     */
    private String schemaName;

    /**
     * 表大小
     */
    private Long tableSize;

    /**
     * 行数
     */
    private Long rowCount;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 字符集
     */
    private String charset;

    /**
     * 排序规则
     */
    private String collation;

    /**
     * 存储引擎
     */
    private String engine;

    /**
     * 表字段列表
     */
    private List<TableColumnVO> columns;

    /**
     * 索引列表
     */
    private List<TableIndexVO> indexes;

    /**
     * 表字段VO
     */
    @Data
    public static class TableColumnVO {
        /**
         * 字段名
         */
        private String columnName;

        /**
         * 字段注释
         */
        private String columnComment;

        /**
         * 数据类型
         */
        private String dataType;

        /**
         * 字段类型
         */
        private String columnType;

        /**
         * 字段长度
         */
        private Integer columnLength;

        /**
         * 小数位数
         */
        private Integer decimalDigits;

        /**
         * 是否可为空
         */
        private Boolean nullable;

        /**
         * 默认值
         */
        private String defaultValue;

        /**
         * 是否主键
         */
        private Boolean primaryKey;

        /**
         * 是否自增
         */
        private Boolean autoIncrement;

        /**
         * 字段位置
         */
        private Integer ordinalPosition;

        /**
         * 字符集
         */
        private String charset;

        /**
         * 排序规则
         */
        private String collation;

        /**
         * 额外信息
         */
        private String extra;
    }

    /**
     * 表索引VO
     */
    @Data
    public static class TableIndexVO {
        /**
         * 索引名
         */
        private String indexName;

        /**
         * 索引类型
         */
        private String indexType;

        /**
         * 是否唯一
         */
        private Boolean unique;

        /**
         * 字段列表
         */
        private List<String> columns;

        /**
         * 索引注释
         */
        private String indexComment;
    }
}
