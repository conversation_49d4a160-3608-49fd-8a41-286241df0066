<template>
  <div class="data-collection-container">
    <!-- 左侧分类树 -->
    <div class="category-panel">
      <div class="category-header">
        <h3>任务分类目录</h3>
        <el-button 
          type="primary" 
          size="small" 
          icon="Plus" 
          @click="handleAddCategory"
          v-hasPermi="['datacollection:category:add']"
        >
          新增分类
        </el-button>
      </div>
      
      <div class="category-tree">
        <el-tree
          ref="categoryTreeRef"
          :data="categoryTree"
          :props="treeProps"
          :expand-on-click-node="false"
          :default-expand-all="true"
          node-key="categoryId"
          highlight-current
          @node-click="handleCategoryClick"
          @node-contextmenu="handleCategoryRightClick"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <el-icon class="node-icon">
                <component :is="getCategoryIcon(data.categoryIcon)" />
              </el-icon>
              <span class="node-label">{{ node.label }}</span>
              <span class="node-count">({{ data.taskCount || 0 }})</span>
            </div>
          </template>
        </el-tree>
      </div>
    </div>

    <!-- 右侧任务列表 -->
    <div class="task-panel">
      <div class="task-header">
        <div class="header-left">
          <h3>{{ currentCategoryName }}</h3>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item 
              v-for="item in breadcrumbItems" 
              :key="item.categoryId"
              @click="handleBreadcrumbClick(item)"
            >
              {{ item.categoryName }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <el-button 
            type="primary" 
            icon="Plus" 
            @click="handleAddTask"
            v-hasPermi="['datacollection:task:add']"
          >
            新增任务
          </el-button>
          <el-button 
            icon="Refresh" 
            @click="refreshTaskList"
          >
            刷新
          </el-button>
        </div>
      </div>

      <!-- 搜索条件 -->
      <div class="search-panel" v-show="showSearch">
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
          <el-form-item label="任务名称" prop="taskName">
            <el-input
              v-model="queryParams.taskName"
              placeholder="请输入任务名称"
              clearable
              @keyup.enter="handleQuery"
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item label="采集类型" prop="collectionType">
            <el-select v-model="queryParams.collectionType" placeholder="请选择采集类型" clearable style="width: 150px;">
              <el-option label="API接口" value="API" />
              <el-option label="SQL查询" value="SQL" />
              <el-option label="文件采集" value="FILE" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 任务列表 -->
      <div class="task-list">
        <el-table 
          v-loading="loading" 
          :data="taskList" 
          @selection-change="handleSelectionChange"
          height="calc(100vh - 280px)"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="编号" align="center" prop="taskId" width="80" />
          <el-table-column label="任务名称" align="center" prop="taskName" :show-overflow-tooltip="true" min-width="200" />
          <el-table-column label="任务描述" align="center" prop="taskDesc" :show-overflow-tooltip="true" min-width="200" />
          <el-table-column label="采集类型" align="center" prop="collectionType" width="100">
            <template #default="scope">
              <el-tag :type="getCollectionTypeTag(scope.row.collectionType)">
                {{ getCollectionTypeText(scope.row.collectionType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="写入模式" align="center" prop="writeMode" width="100">
            <template #default="scope">
              <el-tag :type="getWriteModeTag(scope.row.writeMode)">
                {{ getWriteModeText(scope.row.writeMode) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="调度类型" align="center" prop="scheduleType" width="100">
            <template #default="scope">
              <el-tag :type="getScheduleTypeTag(scope.row.scheduleType)">
                {{ getScheduleTypeText(scope.row.scheduleType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status" width="80">
            <template #default="scope">
              <el-switch
                v-model="scope.row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="160">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220">
            <template #default="scope">
              <el-button
                type="text"
                icon="View"
                @click="handleDetail(scope.row)"
                v-hasPermi="['datacollection:task:query']"
              >详情</el-button>
              <el-button
                type="text"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['datacollection:task:edit']"
              >修改</el-button>
              <el-button
                type="text"
                icon="Setting"
                @click="handleDesign(scope.row)"
                v-hasPermi="['datacollection:task:edit']"
              >设计</el-button>
              <el-button
                type="text"
                icon="VideoPlay"
                @click="handleExecute(scope.row)"
                v-hasPermi="['datacollection:task:execute']"
                :disabled="scope.row.status !== 1"
              >执行</el-button>
              <el-dropdown @command="(command) => handleCommand(command, scope.row)">
                <el-button type="text" icon="More">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="copy" icon="CopyDocument">复制</el-dropdown-item>
                    <el-dropdown-item command="history" icon="Clock">执行历史</el-dropdown-item>
                    <el-dropdown-item command="move" icon="Folder">移动分类</el-dropdown-item>
                    <el-dropdown-item command="delete" icon="Delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getTaskList"
        />
      </div>
    </div>

    <!-- 分类右键菜单 -->
    <el-dropdown
      ref="categoryContextMenuRef"
      trigger="contextmenu"
      @command="handleCategoryCommand"
    >
      <span></span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="addSubCategory" icon="Plus">新增子分类</el-dropdown-item>
          <el-dropdown-item command="editCategory" icon="Edit">编辑分类</el-dropdown-item>
          <el-dropdown-item command="deleteCategory" icon="Delete" divided>删除分类</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 分类编辑对话框 -->
    <CategoryEditDialog 
      v-model="categoryDialogVisible"
      :category-data="currentCategory"
      :parent-options="categoryOptions"
      @success="handleCategorySuccess"
    />

    <!-- 任务移动对话框 -->
    <!-- <TaskMoveDialog
      v-model="moveDialogVisible"
      :task-data="currentTask"
      :category-options="categoryOptions"
      @success="handleMoveSuccess"
    /> -->
  </div>
</template>

<script setup name="DataCollectionIndex">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  listDataCollectionTask,
  getDataCollectionTask,
  delDataCollectionTask,
  addDataCollectionTask,
  updateDataCollectionTask,
  changeTaskStatus,
  executeTask
} from '@/api/datacollection/task'
import {
  listDataCollectionCategory,
  getDataCollectionCategory,
  delDataCollectionCategory,
  addDataCollectionCategory,
  updateDataCollectionCategory
} from '@/api/datacollection/category'
import CategoryEditDialog from './CategoryEditDialog.vue'
// import TaskMoveDialog from './TaskMoveDialog.vue'
import { parseTime } from '@/utils/ruoyi'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)
const taskList = ref([])
const categoryTree = ref([])
const categoryOptions = ref([])
const breadcrumbItems = ref([])
const currentCategoryId = ref(null)
const currentCategoryName = ref('请选择分类')
const currentCategory = ref({})
const currentTask = ref({})
const categoryDialogVisible = ref(false)
const moveDialogVisible = ref(false)
const selectedTasks = ref([])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  taskName: null,
  collectionType: null,
  status: null,
  categoryId: null
})

// 树形组件属性
const treeProps = {
  children: 'children',
  label: 'categoryName',
  value: 'categoryId'
}

// 组件引用
const categoryTreeRef = ref()
const categoryContextMenuRef = ref()
const queryRef = ref()

// 生命周期
onMounted(() => {
  getCategoryTree()
  getTaskList()
})

// 获取分类树
const getCategoryTree = async () => {
  try {
    const response = await listDataCollectionCategory()
    categoryTree.value = handleTree(response.data, 'categoryId', 'parentId')
    categoryOptions.value = buildCategoryOptions(response.data)
  } catch (error) {
    console.error('获取分类树失败:', error)
  }
}

// 获取任务列表
const getTaskList = async () => {
  loading.value = true
  try {
    const response = await listDataCollectionTask(queryParams)
    taskList.value = response.rows
    total.value = response.total
  } catch (error) {
    console.error('获取任务列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理分类点击
const handleCategoryClick = (data) => {
  currentCategoryId.value = data.categoryId
  currentCategoryName.value = data.categoryName
  queryParams.categoryId = data.categoryId
  queryParams.pageNum = 1

  // 构建面包屑
  buildBreadcrumb(data)

  // 刷新任务列表
  getTaskList()
}

// 构建面包屑
const buildBreadcrumb = (categoryData) => {
  const items = []
  let current = categoryData

  while (current) {
    items.unshift({
      categoryId: current.categoryId,
      categoryName: current.categoryName
    })
    current = findParentCategory(current.parentId)
  }

  breadcrumbItems.value = items
}

// 查找父分类
const findParentCategory = (parentId) => {
  if (!parentId || parentId === 0) return null

  const findInTree = (nodes) => {
    for (const node of nodes) {
      if (node.categoryId === parentId) {
        return node
      }
      if (node.children) {
        const found = findInTree(node.children)
        if (found) return found
      }
    }
    return null
  }

  return findInTree(categoryTree.value)
}

// 面包屑点击
const handleBreadcrumbClick = (item) => {
  const node = categoryTreeRef.value.getNode(item.categoryId)
  if (node) {
    categoryTreeRef.value.setCurrentKey(item.categoryId)
    handleCategoryClick(node.data)
  }
}

// 分类右键菜单
const handleCategoryRightClick = (event, data) => {
  event.preventDefault()
  currentCategory.value = data

  nextTick(() => {
    categoryContextMenuRef.value.handleOpen()
  })
}

// 分类命令处理
const handleCategoryCommand = (command) => {
  switch (command) {
    case 'addSubCategory':
      handleAddSubCategory()
      break
    case 'editCategory':
      handleEditCategory()
      break
    case 'deleteCategory':
      handleDeleteCategory()
      break
  }
}

// 新增分类
const handleAddCategory = () => {
  currentCategory.value = {
    parentId: currentCategoryId.value || 0
  }
  categoryDialogVisible.value = true
}

// 新增子分类
const handleAddSubCategory = () => {
  currentCategory.value = {
    parentId: currentCategory.value.categoryId
  }
  categoryDialogVisible.value = true
}

// 编辑分类
const handleEditCategory = () => {
  categoryDialogVisible.value = true
}

// 删除分类
const handleDeleteCategory = async () => {
  try {
    await ElMessageBox.confirm(
      `是否确认删除分类"${currentCategory.value.categoryName}"？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await delDataCollectionCategory(currentCategory.value.categoryId)
    ElMessage.success('删除成功')
    getCategoryTree()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
    }
  }
}

// 分类操作成功回调
const handleCategorySuccess = () => {
  getCategoryTree()
  categoryDialogVisible.value = false
}

// 获取分类图标
const getCategoryIcon = (iconName) => {
  const iconMap = {
    folder: 'Folder',
    database: 'Database',
    api: 'Connection',
    file: 'Document',
    sync: 'Refresh',
    mysql: 'Database',
    sqlserver: 'Database',
    oracle: 'Database',
    water: 'Drizzling',
    hydrology: 'Drizzling',
    quality: 'MagicStick',
    rain: 'Drizzling',
    facility: 'OfficeBuilding'
  }
  return iconMap[iconName] || 'Folder'
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getTaskList()
}

// 重置搜索
const resetQuery = () => {
  queryRef.value.resetFields()
  handleQuery()
}

// 刷新任务列表
const refreshTaskList = () => {
  getTaskList()
}

// 新增任务
const handleAddTask = () => {
  if (!currentCategoryId.value) {
    ElMessage.warning('请先选择分类')
    return
  }

  router.push({
    path: '/datacollection/designer',
    query: {
      categoryId: currentCategoryId.value,
      mode: 'add'
    }
  })
}

// 任务详情
const handleDetail = (row) => {
  router.push({
    path: '/datacollection/designer',
    query: {
      taskId: row.taskId,
      mode: 'view'
    }
  })
}

// 修改任务
const handleUpdate = (row) => {
  router.push({
    path: '/datacollection/designer',
    query: {
      taskId: row.taskId,
      mode: 'edit'
    }
  })
}

// 设计任务
const handleDesign = (row) => {
  router.push({
    path: '/datacollection/designer',
    query: {
      taskId: row.taskId,
      mode: 'design'
    }
  })
}

// 执行任务
const handleExecute = async (row) => {
  try {
    await ElMessageBox.confirm(
      `是否确认执行任务"${row.taskName}"？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    await executeTask(row.taskId)
    ElMessage.success('任务执行成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('执行任务失败:', error)
    }
  }
}

// 状态变更
const handleStatusChange = async (row) => {
  try {
    const text = row.status === 1 ? '启用' : '停用'
    await ElMessageBox.confirm(
      `确认要${text}"${row.taskName}"任务吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await changeTaskStatus(row.taskId, row.status)
    ElMessage.success(`${text}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      row.status = row.status === 0 ? 1 : 0
      console.error('状态变更失败:', error)
    }
  }
}

// 更多操作
const handleCommand = (command, row) => {
  currentTask.value = row

  switch (command) {
    case 'copy':
      handleCopyTask()
      break
    case 'history':
      handleTaskHistory()
      break
    case 'move':
      handleMoveTask()
      break
    case 'delete':
      handleDeleteTask()
      break
  }
}

// 复制任务
const handleCopyTask = () => {
  router.push({
    path: '/datacollection/designer',
    query: {
      taskId: currentTask.value.taskId,
      categoryId: currentCategoryId.value,
      mode: 'copy'
    }
  })
}

// 任务历史
const handleTaskHistory = () => {
  router.push({
    path: '/datacollection/history',
    query: {
      taskId: currentTask.value.taskId
    }
  })
}

// 移动任务
const handleMoveTask = () => {
  moveDialogVisible.value = true
}

// 删除任务
const handleDeleteTask = async () => {
  try {
    await ElMessageBox.confirm(
      `是否确认删除任务"${currentTask.value.taskName}"？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await delDataCollectionTask(currentTask.value.taskId)
    ElMessage.success('删除成功')
    getTaskList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除任务失败:', error)
    }
  }
}

// 移动成功回调
const handleMoveSuccess = () => {
  getTaskList()
  moveDialogVisible.value = false
}

// 多选变化
const handleSelectionChange = (selection) => {
  selectedTasks.value = selection
}

// 获取采集类型标签
const getCollectionTypeTag = (type) => {
  const tagMap = {
    'API': 'success',
    'SQL': 'primary',
    'FILE': 'warning'
  }
  return tagMap[type] || 'info'
}

// 获取采集类型文本
const getCollectionTypeText = (type) => {
  const textMap = {
    'API': 'API接口',
    'SQL': 'SQL查询',
    'FILE': '文件采集'
  }
  return textMap[type] || type
}

// 获取写入模式标签
const getWriteModeTag = (mode) => {
  const tagMap = {
    'OVERWRITE': 'danger',
    'APPEND': 'success',
    'UPSERT': 'warning',
    'AUTO_CREATE': 'info'
  }
  return tagMap[mode] || 'info'
}

// 获取写入模式文本
const getWriteModeText = (mode) => {
  const textMap = {
    'OVERWRITE': '覆盖',
    'APPEND': '追加',
    'UPSERT': '更新插入',
    'AUTO_CREATE': '自动建表'
  }
  return textMap[mode] || mode
}

// 获取调度类型标签
const getScheduleTypeTag = (type) => {
  const tagMap = {
    'MANUAL': 'info',
    'CRON': 'success',
    'REALTIME': 'warning'
  }
  return tagMap[type] || 'info'
}

// 获取调度类型文本
const getScheduleTypeText = (type) => {
  const textMap = {
    'MANUAL': '手动',
    'CRON': '定时',
    'REALTIME': '实时'
  }
  return textMap[type] || type
}

// 构建树形结构
const handleTree = (data, id, parentId, children = 'children') => {
  const config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children'
  }

  const childrenListMap = {}
  const nodeIds = {}
  const tree = []

  for (const d of data) {
    const parentId = d[config.parentId]
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = []
    }
    nodeIds[d[config.id]] = d
    childrenListMap[parentId].push(d)
  }

  for (const d of data) {
    const parentId = d[config.parentId]
    if (nodeIds[parentId] == null) {
      tree.push(d)
    }
  }

  for (const t of tree) {
    adaptToChildrenList(t)
  }

  function adaptToChildrenList(o) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]]
    }
    if (o[config.childrenList]) {
      for (const c of o[config.childrenList]) {
        adaptToChildrenList(c)
      }
    }
  }
  return tree
}

// 构建分类选项
const buildCategoryOptions = (data) => {
  const options = []

  const buildOptions = (nodes, level = 0) => {
    for (const node of nodes) {
      options.push({
        value: node.categoryId,
        label: '　'.repeat(level) + node.categoryName,
        disabled: false
      })

      if (node.children && node.children.length > 0) {
        buildOptions(node.children, level + 1)
      }
    }
  }

  buildOptions(data)
  return options
}
</script>

<style scoped>
.data-collection-container {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
}

.category-panel {
  width: 300px;
  background: white;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
}

.category-header {
  padding: 16px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.category-tree {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  flex: 1;
}

.node-icon {
  margin-right: 8px;
  color: #606266;
}

.node-label {
  flex: 1;
}

.node-count {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

.task-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  margin-left: 8px;
  margin-right: 8px;
  margin-top: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-header {
  padding: 16px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 500;
}

.header-right {
  display: flex;
  gap: 8px;
}

.search-panel {
  padding: 16px;
  border-bottom: 1px solid #e6e6e6;
  background: #fafafa;
}

.task-list {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

:deep(.el-tree-node__content) {
  height: 36px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-breadcrumb__item) {
  cursor: pointer;
}

:deep(.el-breadcrumb__item:hover) {
  color: #409eff;
}
</style>
