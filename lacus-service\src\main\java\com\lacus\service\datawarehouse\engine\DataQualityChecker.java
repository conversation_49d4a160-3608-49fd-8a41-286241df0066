package com.lacus.service.datawarehouse.engine;

import com.lacus.service.datawarehouse.dto.EtlQualityRulesDTO;
import com.lacus.service.datawarehouse.model.EtlTaskModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据质量检查器
 * 负责执行ETL任务的数据质量检查
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DataQualityChecker {
    
    @Autowired
    private JdbcTemplate dorisJdbcTemplate;
    
    /**
     * 执行数据质量检查
     */
    public QualityCheckResult executeQualityCheck(EtlTaskModel taskModel, EtlExecutionResult executionResult) {
        log.info("开始执行数据质量检查: taskId={}", taskModel.getTaskId());
        
        QualityCheckResult result = new QualityCheckResult();
        result.setTaskId(taskModel.getTaskId());
        result.setTargetTable(taskModel.getTargetTable());
        result.setCheckTime(System.currentTimeMillis());
        
        List<QualityRuleResult> ruleResults = new ArrayList<>();
        
        try {
            // 执行每个质量规则
            for (EtlQualityRulesDTO rule : taskModel.getQualityRules()) {
                QualityRuleResult ruleResult = executeQualityRule(taskModel, rule);
                ruleResults.add(ruleResult);
                
                // 如果有严重错误，记录但继续执行其他规则
                if (!ruleResult.isPassed() && "ERROR".equals(rule.getSeverity())) {
                    log.error("数据质量检查失败: taskId={}, rule={}, message={}", 
                            taskModel.getTaskId(), rule.getRuleName(), ruleResult.getMessage());
                }
            }
            
            // 计算总体质量分数
            double qualityScore = calculateQualityScore(ruleResults);
            result.setQualityScore(qualityScore);
            result.setRuleResults(ruleResults);
            result.setPassed(qualityScore >= 80.0); // 80分以上认为通过
            
            log.info("数据质量检查完成: taskId={}, score={}, passed={}", 
                    taskModel.getTaskId(), qualityScore, result.isPassed());
            
        } catch (Exception e) {
            log.error("数据质量检查异常: taskId={}", taskModel.getTaskId(), e);
            result.setPassed(false);
            result.setErrorMessage(e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 执行单个质量规则
     */
    private QualityRuleResult executeQualityRule(EtlTaskModel taskModel, EtlQualityRulesDTO rule) {
        QualityRuleResult result = new QualityRuleResult();
        result.setRuleName(rule.getRuleName());
        result.setRuleType(rule.getRuleType());
        result.setSeverity(rule.getSeverity());
        
        try {
            String ruleType = rule.getRuleType();
            
            switch (ruleType) {
                case "NOT_NULL":
                    result = checkNotNull(taskModel, rule);
                    break;
                case "UNIQUE":
                    result = checkUnique(taskModel, rule);
                    break;
                case "RANGE":
                    result = checkRange(taskModel, rule);
                    break;
                case "PATTERN":
                    result = checkPattern(taskModel, rule);
                    break;
                case "CUSTOM_SQL":
                    result = checkCustomSql(taskModel, rule);
                    break;
                default:
                    result.setPassed(false);
                    result.setMessage("不支持的规则类型: " + ruleType);
            }
            
        } catch (Exception e) {
            log.error("执行质量规则失败: rule={}", rule.getRuleName(), e);
            result.setPassed(false);
            result.setMessage("规则执行异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查非空规则
     */
    private QualityRuleResult checkNotNull(EtlTaskModel taskModel, EtlQualityRulesDTO rule) {
        QualityRuleResult result = new QualityRuleResult();
        result.setRuleName(rule.getRuleName());
        result.setRuleType(rule.getRuleType());
        result.setSeverity(rule.getSeverity());
        
        String sql = String.format(
                "SELECT COUNT(*) as null_count FROM %s.%s WHERE %s IS NULL",
                taskModel.getTargetLayer().toLowerCase(),
                taskModel.getTargetTable(),
                rule.getCheckField()
        );
        
        try {
            Long nullCount = dorisJdbcTemplate.queryForObject(sql, Long.class);
            result.setActualValue(nullCount.toString());
            result.setExpectedValue("0");
            result.setPassed(nullCount == 0);
            result.setMessage(nullCount == 0 ? "检查通过" : 
                    String.format("发现%d条空值记录", nullCount));
        } catch (Exception e) {
            result.setPassed(false);
            result.setMessage("执行检查失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查唯一性规则
     */
    private QualityRuleResult checkUnique(EtlTaskModel taskModel, EtlQualityRulesDTO rule) {
        QualityRuleResult result = new QualityRuleResult();
        result.setRuleName(rule.getRuleName());
        result.setRuleType(rule.getRuleType());
        result.setSeverity(rule.getSeverity());
        
        String sql = String.format(
                "SELECT COUNT(*) - COUNT(DISTINCT %s) as duplicate_count FROM %s.%s",
                rule.getCheckField(),
                taskModel.getTargetLayer().toLowerCase(),
                taskModel.getTargetTable()
        );
        
        try {
            Long duplicateCount = dorisJdbcTemplate.queryForObject(sql, Long.class);
            result.setActualValue(duplicateCount.toString());
            result.setExpectedValue("0");
            result.setPassed(duplicateCount == 0);
            result.setMessage(duplicateCount == 0 ? "检查通过" : 
                    String.format("发现%d条重复记录", duplicateCount));
        } catch (Exception e) {
            result.setPassed(false);
            result.setMessage("执行检查失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查范围规则
     */
    private QualityRuleResult checkRange(EtlTaskModel taskModel, EtlQualityRulesDTO rule) {
        QualityRuleResult result = new QualityRuleResult();
        result.setRuleName(rule.getRuleName());
        result.setRuleType(rule.getRuleType());
        result.setSeverity(rule.getSeverity());
        
        // 解析规则参数
        Map<String, String> params = parseRuleParams(rule.getRuleConfig());
        String minValue = params.get("min");
        String maxValue = params.get("max");
        
        String sql = String.format(
                "SELECT COUNT(*) as out_of_range_count FROM %s.%s WHERE %s < %s OR %s > %s",
                taskModel.getTargetLayer().toLowerCase(),
                taskModel.getTargetTable(),
                rule.getCheckField(), minValue,
                rule.getCheckField(), maxValue
        );
        
        try {
            Long outOfRangeCount = dorisJdbcTemplate.queryForObject(sql, Long.class);
            result.setActualValue(outOfRangeCount.toString());
            result.setExpectedValue("0");
            result.setPassed(outOfRangeCount == 0);
            result.setMessage(outOfRangeCount == 0 ? "检查通过" : 
                    String.format("发现%d条超出范围的记录", outOfRangeCount));
        } catch (Exception e) {
            result.setPassed(false);
            result.setMessage("执行检查失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查模式规则
     */
    private QualityRuleResult checkPattern(EtlTaskModel taskModel, EtlQualityRulesDTO rule) {
        QualityRuleResult result = new QualityRuleResult();
        result.setRuleName(rule.getRuleName());
        result.setRuleType(rule.getRuleType());
        result.setSeverity(rule.getSeverity());
        
        // 解析规则参数
        Map<String, String> params = parseRuleParams(rule.getRuleConfig());
        String pattern = params.get("pattern");
        
        String sql = String.format(
                "SELECT COUNT(*) as invalid_pattern_count FROM %s.%s WHERE %s NOT REGEXP '%s'",
                taskModel.getTargetLayer().toLowerCase(),
                taskModel.getTargetTable(),
                rule.getCheckField(), pattern
        );
        
        try {
            Long invalidCount = dorisJdbcTemplate.queryForObject(sql, Long.class);
            result.setActualValue(invalidCount.toString());
            result.setExpectedValue("0");
            result.setPassed(invalidCount == 0);
            result.setMessage(invalidCount == 0 ? "检查通过" : 
                    String.format("发现%d条不符合模式的记录", invalidCount));
        } catch (Exception e) {
            result.setPassed(false);
            result.setMessage("执行检查失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查自定义SQL规则
     */
    private QualityRuleResult checkCustomSql(EtlTaskModel taskModel, EtlQualityRulesDTO rule) {
        QualityRuleResult result = new QualityRuleResult();
        result.setRuleName(rule.getRuleName());
        result.setRuleType(rule.getRuleType());
        result.setSeverity(rule.getSeverity());
        
        try {
            // 执行自定义SQL，期望返回数值结果
            String customSql = rule.getRuleConfig();
            Long checkResult = dorisJdbcTemplate.queryForObject(customSql, Long.class);
            
            result.setActualValue(checkResult.toString());
            result.setExpectedValue("0");
            result.setPassed(checkResult == 0);
            result.setMessage(checkResult == 0 ? "检查通过" : 
                    String.format("自定义检查发现%d个问题", checkResult));
        } catch (Exception e) {
            result.setPassed(false);
            result.setMessage("执行自定义SQL失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 解析规则参数
     */
    private Map<String, String> parseRuleParams(String ruleParams) {
        Map<String, String> params = new HashMap<>();
        if (ruleParams != null && !ruleParams.trim().isEmpty()) {
            // 简单的键值对解析，实际可以使用JSON
            String[] pairs = ruleParams.split(",");
            for (String pair : pairs) {
                String[] kv = pair.split("=");
                if (kv.length == 2) {
                    params.put(kv[0].trim(), kv[1].trim());
                }
            }
        }
        return params;
    }
    
    /**
     * 计算质量分数
     */
    private double calculateQualityScore(List<QualityRuleResult> ruleResults) {
        if (ruleResults.isEmpty()) {
            return 100.0;
        }
        
        int passedCount = 0;
        int totalWeight = 0;
        
        for (QualityRuleResult result : ruleResults) {
            int weight = getWeight(result.getSeverity());
            totalWeight += weight;
            if (result.isPassed()) {
                passedCount += weight;
            }
        }
        
        return totalWeight == 0 ? 100.0 : (double) passedCount / totalWeight * 100.0;
    }
    
    /**
     * 获取严重程度权重
     */
    private int getWeight(String severity) {
        switch (severity) {
            case "ERROR":
                return 3;
            case "WARNING":
                return 2;
            case "INFO":
                return 1;
            default:
                return 1;
        }
    }
    
    /**
     * 质量检查结果
     */
    public static class QualityCheckResult {
        private Long taskId;
        private String targetTable;
        private long checkTime;
        private double qualityScore;
        private boolean passed;
        private String errorMessage;
        private List<QualityRuleResult> ruleResults;
        
        // getters and setters
        public Long getTaskId() { return taskId; }
        public void setTaskId(Long taskId) { this.taskId = taskId; }
        
        public String getTargetTable() { return targetTable; }
        public void setTargetTable(String targetTable) { this.targetTable = targetTable; }
        
        public long getCheckTime() { return checkTime; }
        public void setCheckTime(long checkTime) { this.checkTime = checkTime; }
        
        public double getQualityScore() { return qualityScore; }
        public void setQualityScore(double qualityScore) { this.qualityScore = qualityScore; }
        
        public boolean isPassed() { return passed; }
        public void setPassed(boolean passed) { this.passed = passed; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public List<QualityRuleResult> getRuleResults() { return ruleResults; }
        public void setRuleResults(List<QualityRuleResult> ruleResults) { this.ruleResults = ruleResults; }
    }
    
    /**
     * 质量规则结果
     */
    public static class QualityRuleResult {
        private String ruleName;
        private String ruleType;
        private String severity;
        private boolean passed;
        private String message;
        private String expectedValue;
        private String actualValue;
        
        // getters and setters
        public String getRuleName() { return ruleName; }
        public void setRuleName(String ruleName) { this.ruleName = ruleName; }
        
        public String getRuleType() { return ruleType; }
        public void setRuleType(String ruleType) { this.ruleType = ruleType; }
        
        public String getSeverity() { return severity; }
        public void setSeverity(String severity) { this.severity = severity; }
        
        public boolean isPassed() { return passed; }
        public void setPassed(boolean passed) { this.passed = passed; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getExpectedValue() { return expectedValue; }
        public void setExpectedValue(String expectedValue) { this.expectedValue = expectedValue; }
        
        public String getActualValue() { return actualValue; }
        public void setActualValue(String actualValue) { this.actualValue = actualValue; }
    }
}
