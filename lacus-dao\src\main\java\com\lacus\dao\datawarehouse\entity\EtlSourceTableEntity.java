package com.lacus.dao.datawarehouse.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lacus.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * ETL数据源配置表
 */
@Getter
@Setter
@TableName("etl_source_table")
@ApiModel(value = "EtlSourceTableEntity对象", description = "ETL数据源配置表")
public class EtlSourceTableEntity extends BaseEntity<EtlSourceTableEntity> {

    @ApiModelProperty("任务ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("源表名称")
    @TableField("table_name")
    private String tableName;

    @ApiModelProperty("别名")
    @TableField("alias")
    private String alias;

    @ApiModelProperty("过滤条件")
    @TableField("where_condition")
    private String whereCondition;

    @ApiModelProperty("关联的任务ID")
    @TableField("etl_task_id")
    private Long etlTaskId;


    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
