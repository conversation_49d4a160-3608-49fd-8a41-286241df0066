package com.lacus.common.constant;

public class TimeConstants {

    private TimeConstants() {
        throw new IllegalStateException("Utility class");
    }

    // comma ,
    public static final String COMMA = ",";

    // hyphen
    public static final String HYPHEN = "-";

    // date format of yyyyMMddHHmmss
    public static final String PARAMETER_FORMAT_TIME = "yyyyMMddHHmmss";

    // month_begin
    public static final String MONTH_BEGIN = "month_begin";

    // add_months
    public static final String ADD_MONTHS = "add_months";

    // month_end
    public static final String MONTH_END = "month_end";

    // week_begin
    public static final String WEEK_BEGIN = "week_begin";

    // week_end
    public static final String WEEK_END = "week_end";

    // this_day
    public static final String THIS_DAY = "this_day";

    // last_day
    public static final String LAST_DAY = "last_day";

    // month_first_day
    public static final String MONTH_FIRST_DAY = "month_first_day";

    // month_last_day
    public static final String MONTH_LAST_DAY = "month_last_day";

    // week_first_day
    public static final String WEEK_FIRST_DAY = "week_first_day";

    // week_last_day
    public static final String WEEK_LAST_DAY = "week_last_day";

    // year_week
    public static final String YEAR_WEEK = "year_week";

    // timestamp
    public static final String TIMESTAMP = "timestamp";

    public static final char SUBTRACT_CHAR = '-';
    public static final char ADD_CHAR = '+';
    public static final char MULTIPLY_CHAR = '*';
    public static final char DIVISION_CHAR = '/';
    public static final char LEFT_BRACE_CHAR = '(';
    public static final char RIGHT_BRACE_CHAR = ')';
    public static final String ADD_STRING = "+";
    public static final String MULTIPLY_STRING = "*";
    public static final String DIVISION_STRING = "/";
    public static final String LEFT_BRACE_STRING = "(";
    public static final char P = 'P';
    public static final char N = 'N';
    public static final String SUBTRACT_STRING = "-";
}
