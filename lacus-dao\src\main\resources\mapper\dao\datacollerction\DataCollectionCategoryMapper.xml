<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lacus.dao.datacollection.mapper.DataCollectionCategoryMapper">
    
    <resultMap type="com.lacus.dao.datacollection.entity.DataCollectionCategoryEntity" id="DataCollectionCategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="categoryCode"    column="category_code"    />
        <result property="parentId"    column="parent_id"    />
        <result property="categoryPath"    column="category_path"    />
        <result property="categoryLevel"    column="category_level"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="categoryIcon"    column="category_icon"    />
        <result property="categoryDesc"    column="category_desc"    />
        <result property="status"    column="status"    />
        <result property="creatorId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updaterId"    column="update_id"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDataCollectionCategoryVo">
        select category_id, category_name, category_code, parent_id, category_path, category_level, 
               sort_order, category_icon, category_desc, status, create_id, create_time,
        update_id, update_time, remark
        from data_collection_category
    </sql>

    <!-- 分页查询分类列表 -->
    <select id="selectCategoryPage" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        <where>
            <if test="categoryName != null and categoryName != ''">
                and category_name like concat('%', #{categoryName}, '%')
            </if>
            <if test="categoryCode != null and categoryCode != ''">
                and category_code = #{categoryCode}
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="createTimeStart != null and createTimeStart != ''">
                and create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                and create_time &lt;= #{createTimeEnd}
            </if>
        </where>
        order by parent_id, sort_order, category_id
    </select>

    <!-- 查询分类树形结构 -->
    <select id="selectCategoryTree" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        <where>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        order by parent_id, sort_order, category_id
    </select>

    <!-- 根据父级ID查询子分类列表 -->
    <select id="selectCategoryByParentId" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where parent_id = #{parentId}
        <if test="status != null">
            and status = #{status}
        </if>
        order by sort_order, category_id
    </select>

    <!-- 检查分类编码是否唯一 -->
    <select id="checkCategoryCodeUnique" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where category_code = #{categoryCode}
        <if test="categoryId != null">
            and category_id != #{categoryId}
        </if>
        limit 1
    </select>

    <!-- 检查分类名称是否唯一 -->
    <select id="checkCategoryNameUnique" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where category_name = #{categoryName} and parent_id = #{parentId}
        <if test="categoryId != null">
            and category_id != #{categoryId}
        </if>
        limit 1
    </select>

    <!-- 检查是否存在子分类 -->
    <select id="hasChildCategory" resultType="int">
        select count(1) from data_collection_category where parent_id = #{categoryId}
    </select>

    <!-- 根据分类路径查询分类 -->
    <select id="selectCategoryByPath" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where category_path = #{categoryPath} limit 1
    </select>

    <!-- 根据分类编码查询分类 -->
    <select id="selectCategoryByCode" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where category_code = #{categoryCode} limit 1
    </select>

    <!-- 更新分类路径 -->
    <update id="updateCategoryPath">
        update data_collection_category set category_path = #{categoryPath} where category_id = #{categoryId}
    </update>

    <!-- 获取分类的最大排序值 -->
    <select id="getMaxSortOrder" resultType="Integer">
        select max(sort_order) from data_collection_category where parent_id = #{parentId}
    </select>

    <!-- 批量更新分类状态 -->
    <update id="batchUpdateStatus">
        update data_collection_category set status = #{status} where category_id in
        <foreach item="categoryId" collection="categoryIds" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </update>

    <!-- 根据分类名称模糊查询 -->
    <select id="selectCategoryByNameLike" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where category_name like concat('%', #{categoryName}, '%')
        <if test="status != null">
            and status = #{status}
        </if>
        order by category_level, sort_order
    </select>

    <!-- 获取分类层级统计 -->
    <select id="selectCategoryLevelStats" resultType="java.util.Map">
        select category_level as level, count(*) as count
        from data_collection_category
        where status = 1
        group by category_level
        order by category_level
    </select>

    <!-- 获取分类状态统计 -->
    <select id="selectCategoryStatusStats" resultType="java.util.Map">
        select status, count(*) as count
        from data_collection_category
        group by status
    </select>

    <!-- 查询指定层级的分类 -->
    <select id="selectCategoryByLevel" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where category_level = #{categoryLevel}
        <if test="status != null">
            and status = #{status}
        </if>
        order by sort_order, category_id
    </select>

    <!-- 检查分类是否存在循环引用 -->
    <select id="checkCircularReference" resultType="boolean">
        WITH RECURSIVE category_hierarchy AS (
            SELECT category_id, parent_id, 1 as level
            FROM data_collection_category
            WHERE category_id = #{parentId}
            
            UNION ALL
            
            SELECT c.category_id, c.parent_id, ch.level + 1
            FROM data_collection_category c
            INNER JOIN category_hierarchy ch ON c.parent_id = ch.category_id
            WHERE ch.level &lt; 10
        )
        SELECT COUNT(*) > 0 FROM category_hierarchy WHERE category_id = #{categoryId}
    </select>

    <!-- 获取分类的所有祖先分类ID -->
    <select id="selectAncestorCategoryIds" resultType="Long">
        WITH RECURSIVE category_ancestors AS (
            SELECT category_id, parent_id
            FROM data_collection_category
            WHERE category_id = #{categoryId}
            
            UNION ALL
            
            SELECT c.category_id, c.parent_id
            FROM data_collection_category c
            INNER JOIN category_ancestors ca ON c.category_id = ca.parent_id
        )
        SELECT category_id FROM category_ancestors WHERE category_id != #{categoryId}
    </select>

    <!-- 获取分类的所有后代分类ID -->
    <select id="selectDescendantCategoryIds" resultType="Long">
        WITH RECURSIVE category_descendants AS (
            SELECT category_id, parent_id
            FROM data_collection_category
            WHERE parent_id = #{categoryId}
            
            UNION ALL
            
            SELECT c.category_id, c.parent_id
            FROM data_collection_category c
            INNER JOIN category_descendants cd ON c.parent_id = cd.category_id
        )
        SELECT category_id FROM category_descendants
    </select>

    <!-- 根据创建时间范围查询分类 -->
    <select id="selectCategoryByCreateTime" resultMap="DataCollectionCategoryResult">
        <include refid="selectDataCollectionCategoryVo"/>
        where create_time between #{startTime} and #{endTime}
        order by create_time desc
    </select>

    <!-- 获取分类创建统计（按日期分组） -->
    <select id="selectCategoryCreateStats" resultType="java.util.Map">
        select DATE(create_time) as date, count(*) as count
        from data_collection_category
        where create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        group by DATE(create_time)
        order by date
    </select>

    <!-- 查询热门分类（按任务数量排序） -->
    <select id="selectHotCategories" resultMap="DataCollectionCategoryResult">
        select c.*, COALESCE(t.task_count, 0) as task_count
        from data_collection_category c
        left join (
            select category_id, count(*) as task_count
            from data_collection_task
            where status = 1
            group by category_id
        ) t on c.category_id = t.category_id
        where c.status = 1
        order by task_count desc, c.category_name
        limit #{limit}
    </select>

    <!-- 查询空分类（没有任务的分类） -->
    <select id="selectEmptyCategories" resultMap="DataCollectionCategoryResult">
        select c.*
        from data_collection_category c
        left join data_collection_task t on c.category_id = t.category_id
        where c.status = 1 and t.category_id is null
        order by c.category_level, c.sort_order
    </select>

    <!-- 获取分类使用情况统计 -->
    <select id="selectCategoryUsageStats" resultType="java.util.Map">
        select 
            c.category_id,
            c.category_name,
            c.category_level,
            COALESCE(t.task_count, 0) as task_count,
            COALESCE(t.enabled_task_count, 0) as enabled_task_count,
            COALESCE(t.disabled_task_count, 0) as disabled_task_count
        from data_collection_category c
        left join (
            select 
                category_id,
                count(*) as task_count,
                sum(case when status = 1 then 1 else 0 end) as enabled_task_count,
                sum(case when status = 0 then 1 else 0 end) as disabled_task_count
            from data_collection_task
            group by category_id
        ) t on c.category_id = t.category_id
        where c.status = 1
        order by c.category_level, c.sort_order
    </select>

    <!-- 获取分类的完整路径名称 -->
    <select id="selectCategoryFullName" resultType="String">
        WITH RECURSIVE category_path AS (
            SELECT category_id, category_name, parent_id, category_name as full_path
            FROM data_collection_category
            WHERE category_id = #{categoryId}
            
            UNION ALL
            
            SELECT c.category_id, c.category_name, c.parent_id, 
                   CONCAT(c.category_name, ' > ', cp.full_path) as full_path
            FROM data_collection_category c
            INNER JOIN category_path cp ON c.category_id = cp.parent_id
        )
        SELECT full_path FROM category_path WHERE parent_id = 0 OR parent_id IS NULL
    </select>

    <!-- 批量删除分类 -->
    <delete id="batchDeleteCategory">
        delete from data_collection_category where category_id in
        <foreach item="categoryId" collection="categoryIds" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

</mapper>
