<template>
  <el-dialog
    title="ETL任务执行日志"
    v-model="dialogVisible"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="log-container">
      <!-- 日志筛选 -->
      <div class="log-filter">
        <el-form :model="queryParams" :inline="true" size="small">
          <el-form-item label="执行状态">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="getExecutionHistory">
              <el-option label="全部" value=""/>
              <el-option label="运行中" value="RUNNING"/>
              <el-option label="成功" value="SUCCESS"/>
              <el-option label="失败" value="FAILED"/>
              <el-option label="已取消" value="CANCELLED"/>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="queryParams.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="getExecutionHistory"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Refresh" @click="refreshData">刷新</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 执行历史列表 -->
      <el-table :data="executionHistory" v-loading="historyLoading" style="width: 100%" @row-click="handleRowClick" max-height="400px">
        <el-table-column label="执行ID" prop="executionId" width="120"/>
        <el-table-column label="执行状态" prop="status" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="开始时间" prop="startTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" prop="endTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="执行时长" prop="duration" width="100">
          <template #default="scope">
            <span>{{ formatDuration(scope.row.duration) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="处理记录数" prop="processedRecords" width="120"/>
        <el-table-column label="错误信息" prop="errorMessage" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="primary" size="small" @click.stop="handleViewLog(scope.row)">查看日志</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getExecutionHistory"
        style="margin-top: 16px;"
      />

      <!-- 详细日志显示 -->
      <div v-if="selectedExecution" class="log-detail" style="margin-top: 20px;">
        <el-divider content-position="left">
          <span>执行日志 - {{ selectedExecution.executionId }}</span>
        </el-divider>

        <!-- 日志工具栏 -->
        <div class="log-toolbar">
          <el-button-group>
            <el-button size="small" @click="refreshLog">
              <el-icon><Refresh/></el-icon>
              刷新日志
            </el-button>
            <el-button size="small" @click="downloadLog">
              <el-icon><Download/></el-icon>
              下载日志
            </el-button>
            <el-button size="small" @click="clearLogDisplay">
              <el-icon><Delete/></el-icon>
              清空显示
            </el-button>
          </el-button-group>

          <div class="log-info">
            <el-tag size="small" :type="getStatusType(selectedExecution.status)">
              {{ getStatusText(selectedExecution.status) }}
            </el-tag>
            <span style="margin-left: 10px;">
              处理记录: {{ selectedExecution.processedRecords || 0 }}
            </span>
          </div>
        </div>

        <!-- 日志内容 -->
        <div class="log-content" v-loading="logLoading">
          <pre v-if="executionLog">{{ executionLog }}</pre>
          <div v-else class="no-log">
            <el-empty description="暂无日志内容" />
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts" name="EtlTaskLog">
import { ref, computed, watch, getCurrentInstance } from 'vue'
import type { Ref } from 'vue'
import { getExecutionHistory as fetchExecutionHistory, getExecutionLog,getHistoryList } from '@/api/datawarehouse/etl';

// 定义接口类型
interface ExecutionHistory {
  executionId: string
  status: string
  startTime: string
  endTime: string
  duration: number
  processedRecords: number
  errorMessage: string
}

interface QueryParams {
  pageNum: number
  pageSize: number
  status: string
  dateRange: Date[] | null
  taskId?: string | number
  startTime?: Date
  endTime?: Date
}

// 组件属性
interface Props {
  visible: boolean
  taskId: string | number | null
}

const props = defineProps<Props>();

// 组件事件
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>();

const { proxy } = getCurrentInstance()!;

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const executionHistory: Ref<ExecutionHistory[]> = ref([]);
const historyLoading = ref(false);
const logLoading = ref(false);
const total = ref(0);
const selectedExecution: Ref<ExecutionHistory | null> = ref(null);
const executionLog = ref('');

const queryParams: Ref<QueryParams> = ref({
  pageNum: 1,
  pageSize: 10,
  status: '',
  dateRange: null
});

// 工具函数
const parseTime = (time: string | number | Date | null | undefined): string => {
  if (!time) return '';
  const date = new Date(time);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

/** 获取执行历史 */
function getExecutionHistory(): void {
  if (!props.taskId) return;

  historyLoading.value = true;
  const params: any = {
    ...queryParams.value,
    taskId: props.taskId
  };

  // 处理时间范围
  if (queryParams.value.dateRange && queryParams.value.dateRange.length === 2) {
    params.startTime = queryParams.value.dateRange[0];
    params.endTime = queryParams.value.dateRange[1];
  }
  // delete params.dateRange;
  console.log(params);
  getHistoryList(params).then((response: any) => {
    executionHistory.value = response.rows || response.data?.rows || [];
    total.value = response.total || response.data?.total || 0;
  }).finally(() => {
    historyLoading.value = false;
  });
}

/** 查看执行日志 */
function handleViewLog(row: ExecutionHistory): void {
  selectedExecution.value = row;
  loadExecutionLog(row.executionId);
}

/** 加载执行日志 */
function loadExecutionLog(executionId: string): void {
  logLoading.value = true;
  getExecutionLog(executionId).then((response: any) => {
    executionLog.value = response.data || response || '暂无日志内容';
  }).catch(() => {
    executionLog.value = '获取日志失败';
  }).finally(() => {
    logLoading.value = false;
  });
}

/** 行点击事件 */
function handleRowClick(row: ExecutionHistory): void {
  handleViewLog(row);
}

/** 刷新数据 */
function refreshData(): void {
  getExecutionHistory();
}

/** 刷新日志 */
function refreshLog(): void {
  if (selectedExecution.value) {
    loadExecutionLog(selectedExecution.value.executionId);
  }
}

/** 下载日志 */
function downloadLog(): void {
  if (!executionLog.value || !selectedExecution.value) {
    (proxy as any).$modal.msgWarning('没有可下载的日志内容');
    return;
  }

  const blob = new Blob([executionLog.value], { type: 'text/plain' });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `etl_log_${selectedExecution.value.executionId}.txt`;
  link.click();
  window.URL.revokeObjectURL(url);
}

/** 清空日志显示 */
function clearLogDisplay(): void {
  selectedExecution.value = null;
  executionLog.value = '';
}

/** 关闭弹窗 */
function handleClose(): void {
  dialogVisible.value = false;
  selectedExecution.value = null;
  executionLog.value = '';
}

/** 获取状态类型 */
function getStatusType(status: string): string {
  const typeMap: Record<string, string> = {
    'RUNNING': 'warning',
    'SUCCESS': 'success',
    'FAILED': 'danger',
    'CANCELLED': 'info'
  };
  return typeMap[status] || 'info';
}

/** 获取状态文本 */
function getStatusText(status: string): string {
  const textMap: Record<string, string> = {
    'RUNNING': '运行中',
    'SUCCESS': '成功',
    'FAILED': '失败',
    'CANCELLED': '已取消'
  };
  return textMap[status] || status;
}

/** 格式化执行时长 */
function formatDuration(duration: number): string {
  if (!duration) return '-';
  const seconds = Math.floor(duration / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

/** 监听弹窗显示状态 */
watch(() => props.visible, (newVal) => {
  if (newVal && props.taskId) {
    getExecutionHistory();
  }
});
</script>

<style scoped>
.log-container {
  max-height: 80vh;
  overflow-y: auto;
}

.log-filter {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.log-detail {
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.log-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.log-info {
  display: flex;
  align-items: center;
}

.log-content {
  max-height: 400px;
  overflow-y: auto;
  background-color: #1e1e1e;
  color: #d4d4d4;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.no-log {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background-color: #f5f7fa;
  color: #909399;
}
</style>
