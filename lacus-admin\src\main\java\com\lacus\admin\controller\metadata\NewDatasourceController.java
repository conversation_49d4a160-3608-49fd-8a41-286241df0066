//package com.lacus.admin.controller.metadata;
//
//import com.lacus.common.core.base.BaseController;
//import com.lacus.common.core.domain.R;
//import lombok.RequiredArgsConstructor;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 数据仓库数据源管理控制器
// */
//@RestController
//@RequestMapping("/metadata/datasource")
//@RequiredArgsConstructor
//public class NewDatasourceController extends BaseController {
//
//    /**
//     * 获取数据源列表
//     */
//    @GetMapping("/list")
//    @PreAuthorize("@permission.has('metadata:datasource:list')")
//    public R<Object> getDatasourceList() {
//        // 这里需要实现获取数据源列表的逻辑
//        List<Map<String, Object>> datasources = new ArrayList<Map<String, Object>>();
//
//        Map<String, Object> datasource1 = new HashMap<String, Object>();
//        datasource1.put("id", 1L);
//        datasource1.put("name", "Doris集群");
//        datasource1.put("type", "DORIS");
//        datasource1.put("host", "*************");
//        datasource1.put("port", 9030);
//        datasources.add(datasource1);
//
//        Map<String, Object> datasource2 = new HashMap<String, Object>();
//        datasource2.put("id", 2L);
//        datasource2.put("name", "MySQL业务库");
//        datasource2.put("type", "MYSQL");
//        datasource2.put("host", "*************");
//        datasource2.put("port", 3306);
//        datasources.add(datasource2);
//
//        return R.ok(datasources);
//    }
//}
