<template>
  <div class="task-design-container">
    <!-- 顶部工具栏 -->
    <div class="design-header">
      <div class="header-left">
        <el-button 
          icon="ArrowLeft" 
          @click="goBack"
        >
          返回
        </el-button>
        <el-divider direction="vertical" />
        <h3>{{ pageTitle }}</h3>
      </div>
      <div class="header-right">
        <el-button 
          v-if="mode !== 'view'"
          type="primary" 
          icon="Check"
          @click="saveTask"
          :loading="saving"
        >
          保存
        </el-button>
        <el-button
          v-if="mode === 'edit'"
          type="success"
          icon="VideoPlay"
          @click="handleExecuteTask"
          :loading="executing"
        >
          执行
        </el-button>
        <el-button 
          type="info" 
          icon="View"
          @click="previewTask"
          :loading="previewing"
        >
          预览
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="design-content">
      <!-- 左侧工具面板 -->
      <div class="tools-panel">
        <div class="panel-header">
          <h4>组件工具箱</h4>
        </div>
        <div class="tools-list">
          <div class="tool-category">
            <h5>数据源</h5>
            <div 
              v-for="tool in dataSourceTools" 
              :key="tool.type"
              class="tool-item"
              draggable="true"
              @dragstart="handleDragStart($event, tool)"
            >
              <el-icon><component :is="tool.icon" /></el-icon>
              <span>{{ tool.name }}</span>
            </div>
          </div>
          
          <div class="tool-category">
            <h5>数据处理</h5>
            <div 
              v-for="tool in processTools" 
              :key="tool.type"
              class="tool-item"
              draggable="true"
              @dragstart="handleDragStart($event, tool)"
            >
              <el-icon><component :is="tool.icon" /></el-icon>
              <span>{{ tool.name }}</span>
            </div>
          </div>

          <div class="tool-category">
            <h5>流程控制</h5>
            <div 
              v-for="tool in controlTools" 
              :key="tool.type"
              class="tool-item"
              draggable="true"
              @dragstart="handleDragStart($event, tool)"
            >
              <el-icon><component :is="tool.icon" /></el-icon>
              <span>{{ tool.name }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间设计画布 -->
      <div class="design-canvas">
        <div class="canvas-header">
          <div class="canvas-tools">
            <el-button-group>
              <el-button icon="ZoomIn" @click="zoomIn" />
              <el-button icon="ZoomOut" @click="zoomOut" />
              <el-button icon="Refresh" @click="resetZoom" />
            </el-button-group>
            <el-divider direction="vertical" />
            <el-button icon="Delete" @click="clearCanvas">清空画布</el-button>
          </div>
        </div>
        
        <div 
          ref="canvasRef"
          class="canvas-area"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @click="handleCanvasClick"
        >
          <!-- 流程节点 -->
          <div 
            v-for="node in flowNodes" 
            :key="node.id"
            class="flow-node"
            :class="{ 'selected': selectedNodeId === node.id }"
            :style="{ 
              left: node.x + 'px', 
              top: node.y + 'px',
              transform: `scale(${zoomLevel})`
            }"
            @click.stop="selectNode(node)"
            @mousedown="startDrag($event, node)"
          >
            <div class="node-header">
              <el-icon><component :is="getNodeIcon(node.type)" /></el-icon>
              <span>{{ node.name }}</span>
              <el-button 
                type="danger" 
                size="small" 
                icon="Close"
                @click.stop="removeNode(node.id)"
                class="node-delete"
              />
            </div>
            <div class="node-content">
              <div class="node-status" :class="node.status">
                {{ getNodeStatusText(node.status) }}
              </div>
            </div>
          </div>

          <!-- 连接线 -->
          <svg class="connections-layer">
            <line 
              v-for="connection in connections" 
              :key="connection.id"
              :x1="connection.x1" 
              :y1="connection.y1"
              :x2="connection.x2" 
              :y2="connection.y2"
              stroke="#409EFF" 
              stroke-width="2"
              marker-end="url(#arrowhead)"
            />
            <defs>
              <marker 
                id="arrowhead" 
                markerWidth="10" 
                markerHeight="7" 
                refX="9" 
                refY="3.5" 
                orient="auto"
              >
                <polygon points="0 0, 10 3.5, 0 7" fill="#409EFF" />
              </marker>
            </defs>
          </svg>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="properties-panel" v-if="selectedNode">
        <div class="panel-header">
          <h4>节点配置</h4>
          <el-button 
            type="text" 
            icon="Close"
            @click="selectedNodeId = null"
          />
        </div>
        
        <div class="panel-content">
          <!-- 基本信息 -->
          <el-form :model="selectedNode" label-width="80px" size="small">
            <el-form-item label="节点名称">
              <el-input v-model="selectedNode.name" />
            </el-form-item>
            <el-form-item label="节点描述">
              <el-input 
                v-model="selectedNode.description" 
                type="textarea" 
                :rows="2"
              />
            </el-form-item>
          </el-form>

          <!-- 动态配置组件 -->
          <component 
            :is="getConfigComponent(selectedNode.type)"
            v-model="selectedNode.config"
            :node="selectedNode"
            :detailed="true"
          />
        </div>
      </div>
    </div>

    <!-- 任务基本信息对话框 -->
    <el-dialog 
      v-model="taskInfoDialogVisible" 
      title="任务基本信息" 
      width="600px"
    >
      <el-form :model="taskForm" :rules="taskRules" ref="taskFormRef" label-width="100px">
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="taskForm.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="任务编码" prop="taskCode">
          <el-input v-model="taskForm.taskCode" placeholder="请输入任务编码" />
        </el-form-item>
        <el-form-item label="任务分类" prop="categoryId">
          <el-select v-model="taskForm.categoryId" placeholder="请选择任务分类">
            <el-option 
              v-for="category in categoryOptions"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="调度类型" prop="scheduleType">
          <el-select v-model="taskForm.scheduleType" placeholder="请选择调度类型">
            <el-option label="手动执行" value="MANUAL" />
            <el-option label="定时调度" value="CRON" />
            <el-option label="实时采集" value="REALTIME" />
          </el-select>
        </el-form-item>
        <el-form-item label="Cron表达式" prop="cronExpression" v-if="taskForm.scheduleType === 'CRON'">
          <el-input v-model="taskForm.cronExpression" placeholder="请输入Cron表达式" />
        </el-form-item>
        <el-form-item label="任务描述" prop="taskDesc">
          <el-input 
            v-model="taskForm.taskDesc" 
            type="textarea" 
            :rows="3"
            placeholder="请输入任务描述" 
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="taskInfoDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmTaskInfo">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Connection, Database, Document, Refresh, 
  ArrowLeft, Check, VideoPlay, View,
  ZoomIn, ZoomOut, Delete, Close
} from '@element-plus/icons-vue'

// 导入节点配置组件
import ApiCallNodeConfig from '../designer/components/ApiCallNodeConfig.vue'
import SqlQueryNodeConfig from '../designer/components/SqlQueryNodeConfig.vue'
import DataTransformNodeConfig from '../designer/components/DataTransformNodeConfig.vue'
import DataWriteNodeConfig from '../designer/components/DataWriteNodeConfig.vue'
import LoopNodeConfig from '../designer/components/LoopNodeConfig.vue'
import ConditionNodeConfig from '../designer/components/ConditionNodeConfig.vue'
import StartNodeConfig from '../designer/components/StartNodeConfig.vue'
import EndNodeConfig from '../designer/components/EndNodeConfig.vue'

// 类型定义
interface FlowNode {
  id: string
  type: string
  name: string
  description: string
  x: number
  y: number
  status: 'ready' | 'running' | 'success' | 'error'
  config: Record<string, any>
}

interface Connection {
  id: string
  sourceId: string
  targetId: string
  x1: number
  y1: number
  x2: number
  y2: number
}

interface TaskForm {
  taskId?: number
  taskName: string
  taskCode: string
  categoryId: number | null
  scheduleType: string
  cronExpression: string
  taskDesc: string
}

const route = useRoute()
const router = useRouter()

// 响应式数据
const mode = ref<string>(route.query.mode as string || 'add')
const taskId = ref<number | null>(route.query.taskId ? Number(route.query.taskId) : null)
const categoryId = ref<number | null>(route.query.categoryId ? Number(route.query.categoryId) : null)

const saving = ref<boolean>(false)
const executing = ref<boolean>(false)
const previewing = ref<boolean>(false)
const taskInfoDialogVisible = ref<boolean>(false)

const flowNodes = ref<FlowNode[]>([])
const connections = ref<Connection[]>([])
const selectedNodeId = ref<string | null>(null)
const zoomLevel = ref<number>(1)
const categoryOptions = ref<any[]>([])

const canvasRef = ref<HTMLElement>()
const taskFormRef = ref()

// 任务表单
const taskForm = reactive<TaskForm>({
  taskName: '',
  taskCode: '',
  categoryId: categoryId.value,
  scheduleType: 'MANUAL',
  cronExpression: '',
  taskDesc: ''
})

// 表单验证规则
const taskRules = {
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  taskCode: [
    { required: true, message: '请输入任务编码', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择任务分类', trigger: 'change' }
  ]
}

// 工具箱配置
const dataSourceTools = ref([
  { type: 'api-call', name: 'API调用', icon: 'Connection' },
  { type: 'sql-query', name: 'SQL查询', icon: 'Database' },
  { type: 'file-read', name: '文件读取', icon: 'Document' }
])

const processTools = ref([
  { type: 'data-transform', name: '数据转换', icon: 'Refresh' },
  { type: 'data-write', name: '数据写入', icon: 'Database' },
  { type: 'data-filter', name: '数据过滤', icon: 'Filter' }
])

const controlTools = ref([
  { type: 'start', name: '开始节点', icon: 'VideoPlay' },
  { type: 'end', name: '结束节点', icon: 'CircleClose' },
  { type: 'condition', name: '条件判断', icon: 'QuestionFilled' },
  { type: 'loop', name: '循环节点', icon: 'Refresh' }
])

// 计算属性
const pageTitle = computed(() => {
  switch (mode.value) {
    case 'add': return '新增数据采集任务'
    case 'edit': return '编辑数据采集任务'
    case 'view': return '查看数据采集任务'
    default: return '数据采集任务设计'
  }
})

const selectedNode = computed(() => {
  return flowNodes.value.find(node => node.id === selectedNodeId.value) || null
})

// 生命周期
onMounted(() => {
  initializeCanvas()
  loadCategoryOptions()

  if (mode.value === 'edit' || mode.value === 'view') {
    loadTaskData()
  } else {
    // 新增模式，显示任务基本信息对话框
    taskInfoDialogVisible.value = true
  }
})

// 初始化画布
const initializeCanvas = (): void => {
  // 添加默认的开始节点
  if (flowNodes.value.length === 0) {
    addNode('start', 100, 100)
  }
}

// 加载分类选项
const loadCategoryOptions = async (): Promise<void> => {
  try {
    // TODO: 调用API获取分类列表
    categoryOptions.value = [
      { label: '水利工程', value: 1 },
      { label: '水文监测', value: 2 },
      { label: '水质监测', value: 3 }
    ]
  } catch (error) {
    console.error('加载分类选项失败:', error)
  }
}

// 加载任务数据
const loadTaskData = async (): Promise<void> => {
  if (!taskId.value) return

  try {
    // TODO: 调用API获取任务详情
    // const response = await getDataCollectionTask(taskId.value)
    // Object.assign(taskForm, response.data)
    // flowNodes.value = JSON.parse(response.data.flowDefinition || '[]')
  } catch (error) {
    console.error('加载任务数据失败:', error)
  }
}

// 拖拽开始
const handleDragStart = (event: DragEvent, tool: any): void => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify(tool))
  }
}

// 拖拽悬停
const handleDragOver = (event: DragEvent): void => {
  event.preventDefault()
}

// 拖拽放置
const handleDrop = (event: DragEvent): void => {
  event.preventDefault()

  if (!event.dataTransfer) return

  const toolData = JSON.parse(event.dataTransfer.getData('application/json'))
  const rect = canvasRef.value?.getBoundingClientRect()

  if (rect) {
    const x = (event.clientX - rect.left) / zoomLevel.value
    const y = (event.clientY - rect.top) / zoomLevel.value
    addNode(toolData.type, x, y)
  }
}

// 添加节点
const addNode = (type: string, x: number, y: number): void => {
  const nodeId = `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  const newNode: FlowNode = {
    id: nodeId,
    type,
    name: getDefaultNodeName(type),
    description: '',
    x,
    y,
    status: 'ready',
    config: getDefaultNodeConfig(type)
  }

  flowNodes.value.push(newNode)
  selectedNodeId.value = nodeId
}

// 获取默认节点名称
const getDefaultNodeName = (type: string): string => {
  const nameMap: Record<string, string> = {
    'start': '开始',
    'end': '结束',
    'api-call': 'API调用',
    'sql-query': 'SQL查询',
    'data-transform': '数据转换',
    'data-write': '数据写入',
    'condition': '条件判断',
    'loop': '循环处理'
  }
  return nameMap[type] || '未知节点'
}

// 获取默认节点配置
const getDefaultNodeConfig = (type: string): Record<string, any> => {
  const configMap: Record<string, Record<string, any>> = {
    'api-call': {
      url: '',
      method: 'GET',
      headers: [],
      params: [],
      authType: 'NONE'
    },
    'sql-query': {
      dataSource: '',
      sql: '',
      parameters: [],
      fetchSize: 1000
    },
    'data-transform': {
      transformRules: [],
      outputFormat: 'JSON'
    },
    'data-write': {
      targetType: 'DATABASE',
      targetConfig: {}
    }
  }
  return configMap[type] || {}
}

// 获取节点图标
const getNodeIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    'start': 'VideoPlay',
    'end': 'CircleClose',
    'api-call': 'Connection',
    'sql-query': 'Database',
    'data-transform': 'Refresh',
    'data-write': 'Database',
    'condition': 'QuestionFilled',
    'loop': 'Refresh'
  }
  return iconMap[type] || 'Document'
}

// 获取配置组件
const getConfigComponent = (type: string): any => {
  const componentMap: Record<string, any> = {
    'api-call': ApiCallNodeConfig,
    'sql-query': SqlQueryNodeConfig,
    'data-transform': DataTransformNodeConfig,
    'data-write': DataWriteNodeConfig,
    'condition': ConditionNodeConfig,
    'loop': LoopNodeConfig,
    'start': StartNodeConfig,
    'end': EndNodeConfig
  }
  return componentMap[type] || null
}

// 获取节点状态文本
const getNodeStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'ready': '就绪',
    'running': '运行中',
    'success': '成功',
    'error': '错误'
  }
  return statusMap[status] || '未知'
}

// 选择节点
const selectNode = (node: FlowNode): void => {
  selectedNodeId.value = node.id
}

// 画布点击
const handleCanvasClick = (): void => {
  selectedNodeId.value = null
}

// 删除节点
const removeNode = (nodeId: string): void => {
  const index = flowNodes.value.findIndex(node => node.id === nodeId)
  if (index > -1) {
    flowNodes.value.splice(index, 1)
    if (selectedNodeId.value === nodeId) {
      selectedNodeId.value = null
    }
  }
}

// 开始拖拽节点
const startDrag = (event: MouseEvent, node: FlowNode): void => {
  // TODO: 实现节点拖拽逻辑
}

// 缩放控制
const zoomIn = (): void => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.1, 2)
}

const zoomOut = (): void => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5)
}

const resetZoom = (): void => {
  zoomLevel.value = 1
}

// 清空画布
const clearCanvas = (): void => {
  ElMessageBox.confirm('确认清空画布吗？此操作不可恢复。', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    flowNodes.value = []
    connections.value = []
    selectedNodeId.value = null
    // 重新添加开始节点
    addNode('start', 100, 100)
  }).catch(() => {})
}

// 确认任务信息
const confirmTaskInfo = async (): Promise<void> => {
  try {
    await taskFormRef.value.validate()
    taskInfoDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 保存任务
const saveTask = async (): Promise<void> => {
  if (!taskForm.taskName) {
    taskInfoDialogVisible.value = true
    return
  }

  saving.value = true
  try {
    const taskData = {
      ...taskForm,
      flowDefinition: JSON.stringify(flowNodes.value),
      connections: JSON.stringify(connections.value)
    }

    if (mode.value === 'edit' && taskId.value) {
      // TODO: 调用更新API
      // await updateDataCollectionTask(taskData)
      ElMessage.success('任务更新成功')
    } else {
      // TODO: 调用新增API
      // await addDataCollectionTask(taskData)
      ElMessage.success('任务创建成功')
    }

    // 保存成功后返回列表页
    goBack()
  } catch (error) {
    console.error('保存任务失败:', error)
    ElMessage.error('保存任务失败')
  } finally {
    saving.value = false
  }
}

// 执行任务
const handleExecuteTask = async (): Promise<void> => {
  if (!taskId.value) return

  executing.value = true
  try {
    // TODO: 调用执行API
    // await executeTask(taskId.value)
    ElMessage.success('任务执行成功')
  } catch (error) {
    console.error('执行任务失败:', error)
    ElMessage.error('执行任务失败')
  } finally {
    executing.value = false
  }
}

// 预览任务
const previewTask = async (): Promise<void> => {
  previewing.value = true
  try {
    // TODO: 调用预览API
    ElMessage.success('预览功能开发中')
  } catch (error) {
    console.error('预览任务失败:', error)
  } finally {
    previewing.value = false
  }
}

// 返回
const goBack = (): void => {
  router.push('/datacollection')
}
</script>

<style scoped>
.task-design-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.design-header {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 12px;
}

.design-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.tools-panel {
  width: 250px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.panel-header {
  height: 50px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  padding: 0 16px;
  background: #fafafa;
}

.panel-header h4 {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.tools-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.tool-category {
  margin-bottom: 24px;
}

.tool-category h5 {
  margin: 0 0 12px 0;
  font-size: 13px;
  color: #909399;
  font-weight: 500;
}

.tool-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s;
}

.tool-item:hover {
  background: #e6f7ff;
  border-color: #409EFF;
  transform: translateY(-1px);
}

.tool-item:active {
  cursor: grabbing;
}

.tool-item span {
  font-size: 12px;
  color: #606266;
}

.design-canvas {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fafafa;
}

.canvas-header {
  height: 50px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.canvas-tools {
  display: flex;
  align-items: center;
  gap: 12px;
}

.canvas-area {
  flex: 1;
  position: relative;
  overflow: hidden;
  background:
    radial-gradient(circle, #ddd 1px, transparent 1px);
  background-size: 20px 20px;
}

.flow-node {
  position: absolute;
  width: 160px;
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.flow-node:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.flow-node.selected {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 6px 6px 0 0;
  position: relative;
}

.node-header span {
  flex: 1;
  font-size: 12px;
  font-weight: 500;
  color: #303133;
}

.node-delete {
  opacity: 0;
  transition: opacity 0.2s;
}

.flow-node:hover .node-delete {
  opacity: 1;
}

.node-content {
  padding: 8px 12px;
}

.node-status {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  text-align: center;
}

.node-status.ready {
  background: #f0f9ff;
  color: #1890ff;
}

.node-status.running {
  background: #fff7e6;
  color: #fa8c16;
}

.node-status.success {
  background: #f6ffed;
  color: #52c41a;
}

.node-status.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.properties-panel {
  width: 350px;
  background: white;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-size: 12px;
  color: #606266;
}

:deep(.el-input__inner) {
  font-size: 12px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-button-group) {
  display: flex;
}
</style>
