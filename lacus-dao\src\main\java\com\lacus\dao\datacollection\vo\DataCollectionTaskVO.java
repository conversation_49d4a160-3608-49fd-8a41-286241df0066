package com.lacus.dao.datacollection.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 数据采集任务VO
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class DataCollectionTaskVO {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    @Size(min = 2, max = 100, message = "任务名称长度必须在2-100个字符之间")
    private String taskName;

    /**
     * 任务编码
     */
    @NotBlank(message = "任务编码不能为空")
    @Size(min = 2, max = 50, message = "任务编码长度必须在2-50个字符之间")
    private String taskCode;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 采集类型(API/SQL/FILE)
     */
    private String collectionType;

    /**
     * 数据源类型(MYSQL/SQLSERVER/ORACLE/API)
     */
    private String sourceType;

    /**
     * 数据源配置JSON
     */
    private String sourceConfig;

    /**
     * 目标数据库
     */
    private String targetDatabase;

    /**
     * 目标表
     */
    private String targetTable;

    /**
     * 写入模式(OVERWRITE/APPEND/UPSERT/AUTO_CREATE)
     */
    private String writeMode;

    /**
     * 调度类型(MANUAL/CRON/REALTIME)
     */
    private String scheduleType;

    /**
     * Cron表达式
     */
    private String cronExpression;

    /**
     * 流程定义(JSON格式)
     */
    private String flowDefinition;

    /**
     * 任务配置(JSON格式)
     */
    private String taskConfig;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 超时时间(秒)
     */
    private Integer timeout;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 状态(0:禁用 1:启用)
     */
    private Integer status;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 最后执行时间
     */
    private String lastExecuteTime;

    /**
     * 最后执行状态
     */
    private String lastExecuteStatus;

    /**
     * 执行次数
     */
    private Integer executeCount;

    /**
     * 成功次数
     */
    private Integer successCount;

    /**
     * 失败次数
     */
    private Integer failureCount;

    /**
     * 平均执行时长
     */
    private Long avgExecuteTime;

    /**
     * 是否可以执行
     */
    private Boolean canExecute;

    /**
     * 是否正在执行
     */
    private Boolean isRunning;

    /**
     * 状态文本
     */
    private String statusText;

    /**
     * 调度类型文本
     */
    private String scheduleTypeText;

    /**
     * 采集类型文本
     */
    private String collectionTypeText;

    /**
     * 数据源类型文本
     */
    private String sourceTypeText;

    /**
     * 写入模式文本
     */
    private String writeModeText;

    /**
     * 下次执行时间
     */
    private String nextExecuteTime;

    /**
     * 执行成功率
     */
    private Double successRate;

    /**
     * 任务大小（数据量）
     */
    private String taskSize;

    /**
     * 依赖任务数量
     */
    private Integer dependencyCount;

    /**
     * 是否有依赖
     */
    private Boolean hasDependency;

    /**
     * 是否被依赖
     */
    private Boolean isDependedBy;
}
