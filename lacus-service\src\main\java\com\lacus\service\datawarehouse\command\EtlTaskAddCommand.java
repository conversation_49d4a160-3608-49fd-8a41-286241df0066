package com.lacus.service.datawarehouse.command;

import com.lacus.dao.datawarehouse.entity.EtlExecutionHistoryEntity;
import com.lacus.dao.datawarehouse.entity.EtlFieldMappingEntity;
import com.lacus.dao.datawarehouse.entity.EtlQualityRulesEntity;
import com.lacus.dao.datawarehouse.entity.EtlSourceTableEntity;
import com.lacus.service.datawarehouse.dto.EtlFieldMappingDTO;
import com.lacus.service.datawarehouse.dto.EtlQualityRulesDTO;
import com.lacus.service.datawarehouse.dto.EtlSourceTableDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * ETL任务命令
 */
@Data
public class EtlTaskAddCommand {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 源数据层级
     */
    @NotBlank(message = "源数据层级不能为空")
    private String sourceLayer;

    /**
     * 目标数据层级
     */
    @NotBlank(message = "目标数据层级不能为空")
    private String targetLayer;

    /**
     * 调度类型
     */
    @NotBlank(message = "调度类型不能为空")
    private String scheduleType;

    /**
     * Cron表达式
     */
    private String cronExpression;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 目标表名
     */
    @NotBlank(message = "目标表名不能为空")
    private String targetTable;

    /**
     * 写入模式
     */
    @NotBlank(message = "写入模式不能为空")
    private String writeMode;

    /**
     * 源表配置(JSON)
     */
    private  String sourceTablesConfig;

    private List<EtlSourceTableDTO> sourceTables;

    /**
     * 字段映射配置(JSON)
     */
    private String fieldMappingsConfig;

    private List<EtlFieldMappingDTO> fieldMappings;

    /**
     * 数据质量规则配置(JSON)
     */
    private String qualityRulesConfig;

    private List<EtlQualityRulesDTO> qualityRules;
}
