package com.lacus.admin.controller.datacollection;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lacus.admin.controller.datacollection.vo.DataCollectionCategoryPageQuery;
import com.lacus.admin.controller.datacollection.vo.DataCollectionCategoryTreeVO;
import com.lacus.admin.controller.datacollection.vo.DataCollectionCategoryVO;
import com.lacus.common.core.base.BaseController;
import com.lacus.admin.service.datacollection.IDataCollectionCategoryService;
import com.lacus.common.core.dto.ResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.lacus.core.security.AuthenticationUtils.getUsername;

/**
 * 数据采集分类Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/datacollection/category")
public class DataCollectionCategoryController extends BaseController {

    @Autowired
    private IDataCollectionCategoryService categoryService;

    /**
     * 分页查询数据采集分类列表
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:list')")
    @GetMapping("/list")
    public ResponseDTO<?> list(DataCollectionCategoryPageQuery pageQuery) {
        IPage<DataCollectionCategoryVO> page = categoryService.selectCategoryPage(pageQuery);
        return ResponseDTO.ok(page);
    }

    /**
     * 获取分类树形结构
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:list')")
    @GetMapping("/tree")
    public ResponseDTO<List<DataCollectionCategoryTreeVO>> tree(@RequestParam(required = false, defaultValue = "false") Boolean onlyEnabled) {
        return ResponseDTO.ok(categoryService.selectCategoryTree(onlyEnabled));
    }

    /**
     * 根据父级ID查询子分类列表
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:list')")
    @GetMapping("/children/{parentId}")
    public ResponseDTO<List<DataCollectionCategoryVO>> getChildren(@PathVariable Long parentId,
                                  @RequestParam(required = false, defaultValue = "false") Boolean onlyEnabled) {
        return ResponseDTO.ok(categoryService.selectCategoryByParentId(parentId, onlyEnabled));
    }

    /**
     * 获取所有启用的分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:list')")
    @GetMapping("/enabled")
    public ResponseDTO<List<DataCollectionCategoryVO>> getEnabledCategories() {
        List<DataCollectionCategoryVO> list = categoryService.selectEnabledCategories();
        return ResponseDTO.ok(list);
    }

    /**
     * 获取数据采集分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping(value = "/{categoryId}")
    public ResponseDTO<DataCollectionCategoryVO> getInfo(@PathVariable("categoryId") Long categoryId) {
        DataCollectionCategoryVO category = categoryService.selectCategoryById(categoryId);
        return ResponseDTO.ok(category);
    }

    /**
     * 新增数据采集分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:add')")
    @PostMapping
    public ResponseDTO<?> add(@Validated @RequestBody DataCollectionCategoryVO categoryVO) {
        // 检查分类编码是否唯一
        if (!categoryService.checkCategoryCodeUnique(categoryVO.getCategoryCode(), null)) {
            return ResponseDTO.fail("新增分类'" + categoryVO.getCategoryName() + "'失败，分类编码已存在");
        }

        // 检查分类名称在同一父级下是否唯一
        Long parentId = categoryVO.getParentId() != null ? categoryVO.getParentId() : 0L;
        if (!categoryService.checkCategoryNameUnique(categoryVO.getCategoryName(), parentId, null)) {
            return ResponseDTO.fail("新增分类'" + categoryVO.getCategoryName() + "'失败，同一父级下分类名称已存在");
        }

        // 设置创建者
        categoryVO.setCreateBy(getUsername());

        boolean result = categoryService.insertCategory(categoryVO);
        return ResponseDTO.ok(result);
    }

    /**
     * 修改数据采集分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:edit')")
    @PutMapping
    public ResponseDTO<?> edit(@Validated @RequestBody DataCollectionCategoryVO categoryVO) {
        // 检查分类编码是否唯一
        if (!categoryService.checkCategoryCodeUnique(categoryVO.getCategoryCode(), categoryVO.getCategoryId())) {
            return ResponseDTO.fail("修改分类'" + categoryVO.getCategoryName() + "'失败，分类编码已存在");
        }

        // 检查分类名称在同一父级下是否唯一
        Long parentId = categoryVO.getParentId() != null ? categoryVO.getParentId() : 0L;
        if (!categoryService.checkCategoryNameUnique(categoryVO.getCategoryName(), parentId, categoryVO.getCategoryId())) {
            return ResponseDTO.fail("修改分类'" + categoryVO.getCategoryName() + "'失败，同一父级下分类名称已存在");
        }

        // 检查是否存在循环引用
        if (categoryVO.getParentId() != null && categoryVO.getParentId() != 0) {
            if (categoryService.checkCircularReference(categoryVO.getCategoryId(), categoryVO.getParentId())) {
                return ResponseDTO.fail("修改分类失败，不能将分类移动到自己的子分类下");
            }
        }

        // 设置更新者
        categoryVO.setUpdateBy(getUsername());

        boolean result = categoryService.updateCategory(categoryVO);
        return ResponseDTO.ok(result);
    }

    /**
     * 删除数据采集分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:remove')")
    @DeleteMapping("/{categoryIds}")
    public ResponseDTO<?> remove(@PathVariable Long[] categoryIds) {
        List<Long> categoryIdList = Arrays.asList(categoryIds);

        // 检查每个分类是否可以删除
        for (Long categoryId : categoryIdList) {
            if (!categoryService.canDeleteCategory(categoryId)) {
                DataCollectionCategoryVO category = categoryService.selectCategoryById(categoryId);
                String categoryName = category != null ? category.getCategoryName() : "ID:" + categoryId;

                if (categoryService.hasChildCategory(categoryId)) {
                    return ResponseDTO.fail("删除失败，分类'" + categoryName + "'存在子分类，请先删除子分类");
                }
                if (categoryService.hasTask(categoryId)) {
                    return ResponseDTO.fail("删除失败，分类'" + categoryName + "'下存在任务，请先删除或移动任务");
                }
            }
        }

        boolean result = categoryService.deleteCategoryByIds(categoryIdList);
        return ResponseDTO.ok(result);
    }

    /**
     * 移动分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:edit')")
    @PutMapping("/move")
    public ResponseDTO<?> move(@RequestBody Map<String, Object> params) {
        Long categoryId = Long.valueOf(params.get("categoryId").toString());
        Long targetParentId = Long.valueOf(params.get("targetParentId").toString());

        // 检查是否存在循环引用
        if (targetParentId != null && targetParentId != 0) {
            if (categoryService.checkCircularReference(categoryId, targetParentId)) {
                return ResponseDTO.fail("移动失败，不能将分类移动到自己的子分类下");
            }
            if (categoryId.equals(targetParentId)) {
                return ResponseDTO.fail("移动失败，不能将分类移动到自己下面");
            }
        }

        boolean result = categoryService.moveCategory(categoryId, targetParentId);
        return ResponseDTO.ok(result);
    }

    /**
     * 批量更新分类状态
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:edit')")
    @PutMapping("/batchUpdateStatus")
    public ResponseDTO<?> batchUpdateStatus(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Integer> categoryIdInts = (List<Integer>) params.get("categoryIds");
        Integer status = (Integer) params.get("status");

        if (categoryIdInts == null || categoryIdInts.isEmpty()) {
            return ResponseDTO.fail("请选择要更新的分类");
        }

        List<Long> categoryIds = categoryIdInts.stream()
                .map(Integer::longValue)
                .collect(java.util.stream.Collectors.toList());

        boolean result = categoryService.batchUpdateStatus(categoryIds, status);
        return ResponseDTO.ok(result);
    }

    /**
     * 检查分类编码是否唯一
     */
    @GetMapping("/checkCode")
    public ResponseDTO<?> checkCategoryCode(@RequestParam String categoryCode,
                                       @RequestParam(required = false) Long categoryId) {
        if (StringUtils.isEmpty(categoryCode)) {
            return ResponseDTO.fail("分类编码不能为空");
        }

        boolean unique = categoryService.checkCategoryCodeUnique(categoryCode, categoryId);
        return ResponseDTO.ok(unique);
    }

    /**
     * 检查分类名称是否唯一
     */
    @GetMapping("/checkName")
    public ResponseDTO<?> checkCategoryName(@RequestParam String categoryName,
                                       @RequestParam Long parentId,
                                       @RequestParam(required = false) Long categoryId) {
        if (StringUtils.isEmpty(categoryName)) {
            return ResponseDTO.fail("分类名称不能为空");
        }

        boolean unique = categoryService.checkCategoryNameUnique(categoryName, parentId, categoryId);
        return ResponseDTO.ok(unique);
    }

    /**
     * 获取分类的完整路径名称
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/fullName/{categoryId}")
    public ResponseDTO<?> getCategoryFullName(@PathVariable Long categoryId) {
        String fullName = categoryService.getCategoryFullName(categoryId);
        return ResponseDTO.ok(fullName);
    }

    /**
     * 根据分类编码查询分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/code/{categoryCode}")
    public ResponseDTO<?> getCategoryByCode(@PathVariable String categoryCode) {
        DataCollectionCategoryVO category = categoryService.selectCategoryByCode(categoryCode);
        return ResponseDTO.ok(category);
    }

    /**
     * 获取分类统计信息
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/stats/{categoryId}")
    public ResponseDTO<?> getStats(@PathVariable Long categoryId) {
        Map<String, Object> stats = categoryService.getCategoryStats(categoryId);
        return ResponseDTO.ok(stats);
    }

    /**
     * 获取所有分类的统计信息
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/stats")
    public ResponseDTO<?> getAllStats() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("levelStats", categoryService.getCategoryLevelStats());
        stats.put("statusStats", categoryService.getCategoryStatusStats());
        stats.put("usageStats", categoryService.getCategoryUsageStats());
        return ResponseDTO.ok(stats);
    }

    /**
     * 批量删除分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:remove')")
    @DeleteMapping("/batch")
    public ResponseDTO<?> batchRemove(@RequestBody Long[] categoryIds) {
        if (categoryIds == null || categoryIds.length == 0) {
            return ResponseDTO.fail("请选择要删除的分类");
        }

        List<Long> categoryIdList = Arrays.asList(categoryIds);

        // 检查每个分类是否可以删除
        for (Long categoryId : categoryIdList) {
            if (!categoryService.canDeleteCategory(categoryId)) {
                DataCollectionCategoryVO category = categoryService.selectCategoryById(categoryId);
                String categoryName = category != null ? category.getCategoryName() : "ID:" + categoryId;

                if (categoryService.hasChildCategory(categoryId)) {
                    return ResponseDTO.fail("删除失败，分类'" + categoryName + "'存在子分类，请先删除子分类");
                }
                if (categoryService.hasTask(categoryId)) {
                    return ResponseDTO.fail("删除失败，分类'" + categoryName + "'下存在任务，请先删除或移动任务");
                }
            }
        }

        boolean result = categoryService.deleteCategoryByIds(categoryIdList);
        return ResponseDTO.ok(result);
    }

    /**
     * 根据分类名称模糊查询
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:list')")
    @GetMapping("/search")
    public ResponseDTO<?> searchByName(@RequestParam String categoryName,
                                  @RequestParam(required = false, defaultValue = "false") Boolean onlyEnabled) {
        if (StringUtils.isEmpty(categoryName)) {
            return ResponseDTO.fail("搜索关键词不能为空");
        }

        List<DataCollectionCategoryVO> list = categoryService.searchCategoryByName(categoryName, onlyEnabled);
        return ResponseDTO.ok(list);
    }

    /**
     * 获取分类的子分类ID列表
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/childIds/{categoryId}")
    public ResponseDTO<?> getChildCategoryIds(@PathVariable Long categoryId) {
        List<Long> childIds = categoryService.getChildCategoryIds(categoryId);
        return ResponseDTO.ok(childIds);
    }

    /**
     * 检查分类是否可以删除
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/canDelete/{categoryId}")
    public ResponseDTO<?> canDelete(@PathVariable Long categoryId) {
        boolean canDelete = categoryService.canDeleteCategory(categoryId);
        String message = "";

        if (!canDelete) {
            if (categoryService.hasChildCategory(categoryId)) {
                message = "该分类存在子分类，不能删除";
            } else if (categoryService.hasTask(categoryId)) {
                message = "该分类下存在任务，不能删除";
            }
        }

        Map<String, Object> result = new java.util.HashMap<>();
        result.put("canDelete", canDelete);
        result.put("message", message);
        return ResponseDTO.ok(result);
    }

    /**
     * 构建分类路径
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/path/{categoryId}")
    public ResponseDTO<?> getCategoryPath(@PathVariable Long categoryId) {
        String path = categoryService.buildCategoryPath(categoryId);
        return ResponseDTO.ok(path);
    }

    /**
     * 查询热门分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:list')")
    @GetMapping("/hot")
    public ResponseDTO<?> getHotCategories(@RequestParam(required = false, defaultValue = "10") Integer limit) {
        List<DataCollectionCategoryVO> list = categoryService.selectHotCategories(limit);
        return ResponseDTO.ok(list);
    }

    /**
     * 查询空分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:list')")
    @GetMapping("/empty")
    public ResponseDTO<?> getEmptyCategories() {
        List<DataCollectionCategoryVO> list = categoryService.selectEmptyCategories();
        return ResponseDTO.ok(list);
    }
}
