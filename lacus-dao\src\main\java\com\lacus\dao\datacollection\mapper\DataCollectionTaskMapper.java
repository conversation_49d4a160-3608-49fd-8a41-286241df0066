package com.lacus.dao.datacollection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lacus.dao.datacollection.entity.DataCollectionTaskEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 数据采集任务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Mapper
public interface DataCollectionTaskMapper extends BaseMapper<DataCollectionTaskEntity> {

    /**
     * 分页查询数据采集任务列表
     *
     * @param page 分页参数
     * @param taskName 任务名称
     * @param taskCode 任务编码
     * @param categoryId 分类ID
     * @param collectionType 采集类型
     * @param sourceType 数据源类型
     * @param scheduleType 调度类型
     * @param status 状态
     * @param targetDatabase 目标数据库
     * @param targetTable 目标表
     * @param createTimeStart 创建时间开始
     * @param createTimeEnd 创建时间结束
     * @param keyword 关键词
     * @return 任务列表
     */
    IPage<DataCollectionTaskEntity> selectTaskPage(
            Page<DataCollectionTaskEntity> page,
            @Param("taskName") String taskName,
            @Param("taskCode") String taskCode,
            @Param("categoryId") Long categoryId,
            @Param("collectionType") String collectionType,
            @Param("sourceType") String sourceType,
            @Param("scheduleType") String scheduleType,
            @Param("status") Integer status,
            @Param("targetDatabase") String targetDatabase,
            @Param("targetTable") String targetTable,
            @Param("createTimeStart") String createTimeStart,
            @Param("createTimeEnd") String createTimeEnd,
            @Param("keyword") String keyword
    );

    /**
     * 检查任务编码是否唯一
     *
     * @param taskCode 任务编码
     * @param taskId 任务ID（排除自己）
     * @return 任务信息
     */
    DataCollectionTaskEntity checkTaskCodeUnique(
            @Param("taskCode") String taskCode,
            @Param("taskId") Long taskId
    );

    /**
     * 检查任务名称是否唯一
     *
     * @param taskName 任务名称
     * @param categoryId 分类ID
     * @param taskId 任务ID（排除自己）
     * @return 任务信息
     */
    DataCollectionTaskEntity checkTaskNameUnique(
            @Param("taskName") String taskName,
            @Param("categoryId") Long categoryId,
            @Param("taskId") Long taskId
    );

    /**
     * 根据任务名称查询任务
     *
     * @param taskName 任务名称
     * @return 任务实体
     */
    DataCollectionTaskEntity selectByTaskName(@Param("taskName") String taskName);

    /**
     * 根据任务编码查询任务
     *
     * @param taskCode 任务编码
     * @return 任务实体
     */
    DataCollectionTaskEntity selectByTaskCode(@Param("taskCode") String taskCode);

    /**
     * 查询启用状态的任务列表
     *
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectEnabledTasks();

    /**
     * 根据调度类型查询任务列表
     *
     * @param scheduleType 调度类型
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectByScheduleType(@Param("scheduleType") String scheduleType);

    /**
     * 根据分类ID查询任务列表
     *
     * @param categoryId 分类ID
     * @param status 状态过滤
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectByCategoryId(
            @Param("categoryId") Long categoryId,
            @Param("status") Integer status
    );

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @return 更新行数
     */
    int updateTaskStatus(@Param("taskId") Long taskId, @Param("status") Integer status);

    /**
     * 批量更新任务状态
     *
     * @param taskIds 任务ID列表
     * @param status 状态
     * @return 更新行数
     */
    int batchUpdateTaskStatus(@Param("taskIds") List<Long> taskIds, @Param("status") Integer status);

    /**
     * 批量删除任务
     *
     * @param taskIds 任务ID列表
     * @return 删除行数
     */
    int deleteBatchByIds(@Param("taskIds") List<Long> taskIds);

    /**
     * 批量移动任务到指定分类
     *
     * @param taskIds 任务ID列表
     * @param categoryId 目标分类ID
     * @return 更新行数
     */
    int batchMoveTasksToCategory(@Param("taskIds") List<Long> taskIds, @Param("categoryId") Long categoryId);

    /**
     * 根据分类ID统计任务数量
     *
     * @param categoryId 分类ID
     * @return 任务数量
     */
    int countTasksByCategory(@Param("categoryId") Long categoryId);

    /**
     * 获取任务统计信息
     *
     * @param categoryId 分类ID（可选）
     * @return 统计信息
     */
    Map<String, Object> selectTaskStats(@Param("categoryId") Long categoryId);

    /**
     * 查询最近执行的任务
     *
     * @param limit 限制数量
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectRecentExecutedTasks(@Param("limit") Integer limit);

    /**
     * 查询执行失败的任务
     *
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectFailedTasks();

    /**
     * 查询长时间未执行的任务
     *
     * @param hours 小时数
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectLongTimeNoExecuteTasks(@Param("hours") Integer hours);

    /**
     * 查询正在运行的任务
     *
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectRunningTasks();

    /**
     * 根据目标数据库查询任务
     *
     * @param targetDatabase 目标数据库
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectByTargetDatabase(@Param("targetDatabase") String targetDatabase);

    /**
     * 根据目标表查询任务
     *
     * @param targetTable 目标表
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectByTargetTable(@Param("targetTable") String targetTable);

    /**
     * 更新任务版本号
     *
     * @param taskId 任务ID
     * @return 更新行数
     */
    int updateTaskVersion(@Param("taskId") Long taskId);

    /**
     * 获取任务的依赖关系
     *
     * @param taskId 任务ID
     * @return 依赖关系
     */
    List<Map<String, Object>> selectTaskDependencies(@Param("taskId") Long taskId);

    /**
     * 查询任务的前置依赖
     *
     * @param taskId 任务ID
     * @return 前置依赖任务列表
     */
    List<DataCollectionTaskEntity> selectTaskPreDependencies(@Param("taskId") Long taskId);

    /**
     * 查询任务的后置依赖
     *
     * @param taskId 任务ID
     * @return 后置依赖任务列表
     */
    List<DataCollectionTaskEntity> selectTaskPostDependencies(@Param("taskId") Long taskId);

    /**
     * 根据创建时间范围查询任务
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectTasksByCreateTime(
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );

    /**
     * 获取任务创建统计（按日期分组）
     *
     * @param days 统计天数
     * @return 创建统计
     */
    List<Map<String, Object>> selectTaskCreateStats(@Param("days") Integer days);

    /**
     * 查询热门任务（按执行次数排序）
     *
     * @param limit 限制数量
     * @return 热门任务列表
     */
    List<DataCollectionTaskEntity> selectHotTasks(@Param("limit") Integer limit);

    /**
     * 根据采集类型查询任务
     *
     * @param collectionType 采集类型
     * @param status 状态过滤
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectByCollectionType(
            @Param("collectionType") String collectionType,
            @Param("status") Integer status
    );

    /**
     * 根据数据源类型查询任务
     *
     * @param sourceType 数据源类型
     * @param status 状态过滤
     * @return 任务列表
     */
    List<DataCollectionTaskEntity> selectBySourceType(
            @Param("sourceType") String sourceType,
            @Param("status") Integer status
    );
}
