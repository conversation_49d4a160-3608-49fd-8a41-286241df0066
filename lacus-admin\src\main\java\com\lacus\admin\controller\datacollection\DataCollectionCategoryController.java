package com.lacus.admin.controller.datacollection;

import com.lacus.common.annotation.Log;
import com.lacus.common.core.base.BaseController;
import com.lacus.common.core.controller.BaseController;
import com.lacus.common.core.domain.AjaxResult;
import com.lacus.common.core.page.TableDataInfo;
import com.lacus.common.enums.BusinessType;
import com.lacus.common.utils.StringUtils;
import com.lacus.common.utils.poi.ExcelUtil;
import com.lacus.datacollection.domain.DataCollectionCategory;
import com.lacus.datacollection.service.IDataCollectionCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 数据采集分类Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/datacollection/category")
public class DataCollectionCategoryController extends BaseController
{
    @Autowired
    private IDataCollectionCategoryService dataCollectionCategoryService;

    /**
     * 查询数据采集分类列表
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataCollectionCategory dataCollectionCategory)
    {
        startPage();
        List<DataCollectionCategory> list = dataCollectionCategoryService.selectDataCollectionCategoryList(dataCollectionCategory);
        return getDataTable(list);
    }

    /**
     * 获取分类树形结构
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:list')")
    @GetMapping("/tree")
    public AjaxResult tree()
    {
        List<DataCollectionCategory> list = dataCollectionCategoryService.selectCategoryTree();
        return success(list);
    }

    /**
     * 根据父级ID查询子分类列表
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:list')")
    @GetMapping("/children/{parentId}")
    public AjaxResult getChildren(@PathVariable Long parentId)
    {
        List<DataCollectionCategory> list = dataCollectionCategoryService.selectCategoryByParentId(parentId);
        return success(list);
    }

    /**
     * 获取所有启用的分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:list')")
    @GetMapping("/enabled")
    public AjaxResult getEnabledCategories()
    {
        List<DataCollectionCategory> list = dataCollectionCategoryService.selectEnabledCategories();
        return success(list);
    }

    /**
     * 导出数据采集分类列表
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:export')")
    @Log(title = "数据采集分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataCollectionCategory dataCollectionCategory)
    {
        List<DataCollectionCategory> list = dataCollectionCategoryService.selectDataCollectionCategoryList(dataCollectionCategory);
        ExcelUtil<DataCollectionCategory> util = new ExcelUtil<DataCollectionCategory>(DataCollectionCategory.class);
        util.exportExcel(response, list, "数据采集分类数据");
    }

    /**
     * 获取数据采集分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping(value = "/{categoryId}")
    public AjaxResult getInfo(@PathVariable("categoryId") Long categoryId)
    {
        return success(dataCollectionCategoryService.selectDataCollectionCategoryByCategoryId(categoryId));
    }

    /**
     * 新增数据采集分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:add')")
    @Log(title = "数据采集分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody DataCollectionCategory dataCollectionCategory)
    {
        // 检查分类编码是否唯一
        if (!dataCollectionCategoryService.checkCategoryCodeUnique(dataCollectionCategory))
        {
            return error("新增分类'" + dataCollectionCategory.getCategoryName() + "'失败，分类编码已存在");
        }

        // 检查分类名称在同一父级下是否唯一
        if (!dataCollectionCategoryService.checkCategoryNameUnique(dataCollectionCategory))
        {
            return error("新增分类'" + dataCollectionCategory.getCategoryName() + "'失败，同一父级下分类名称已存在");
        }

        // 设置默认值
        if (dataCollectionCategory.getParentId() == null) {
            dataCollectionCategory.setParentId(0L);
        }
        if (dataCollectionCategory.getSortOrder() == null) {
            dataCollectionCategory.setSortOrder(0);
        }
        if (dataCollectionCategory.getStatus() == null) {
            dataCollectionCategory.setStatus(1);
        }
        if (StringUtils.isEmpty(dataCollectionCategory.getCategoryIcon())) {
            dataCollectionCategory.setCategoryIcon("folder");
        }

        dataCollectionCategory.setCreateBy(getUsername());
        return toAjax(dataCollectionCategoryService.insertDataCollectionCategory(dataCollectionCategory));
    }

    /**
     * 修改数据采集分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:edit')")
    @Log(title = "数据采集分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody DataCollectionCategory dataCollectionCategory)
    {
        // 检查分类编码是否唯一
        if (!dataCollectionCategoryService.checkCategoryCodeUnique(dataCollectionCategory))
        {
            return error("修改分类'" + dataCollectionCategory.getCategoryName() + "'失败，分类编码已存在");
        }

        // 检查分类名称在同一父级下是否唯一
        if (!dataCollectionCategoryService.checkCategoryNameUnique(dataCollectionCategory))
        {
            return error("修改分类'" + dataCollectionCategory.getCategoryName() + "'失败，同一父级下分类名称已存在");
        }

        // 检查是否存在循环引用
        if (dataCollectionCategory.getParentId() != null && dataCollectionCategory.getParentId() != 0) {
            List<Long> childIds = dataCollectionCategoryService.getChildCategoryIds(dataCollectionCategory.getCategoryId());
            if (childIds.contains(dataCollectionCategory.getParentId())) {
                return error("修改分类失败，不能将分类移动到自己的子分类下");
            }
        }

        dataCollectionCategory.setUpdateBy(getUsername());
        return toAjax(dataCollectionCategoryService.updateDataCollectionCategory(dataCollectionCategory));
    }

    /**
     * 删除数据采集分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:remove')")
    @Log(title = "数据采集分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{categoryIds}")
    public AjaxResult remove(@PathVariable Long[] categoryIds)
    {
        // 检查每个分类是否可以删除
        for (Long categoryId : categoryIds) {
            if (!dataCollectionCategoryService.canDeleteCategory(categoryId)) {
                DataCollectionCategory category = dataCollectionCategoryService.selectDataCollectionCategoryByCategoryId(categoryId);
                String categoryName = category != null ? category.getCategoryName() : "ID:" + categoryId;

                if (dataCollectionCategoryService.hasChildCategory(categoryId)) {
                    return error("删除失败，分类'" + categoryName + "'存在子分类，请先删除子分类");
                }
                if (dataCollectionCategoryService.hasTask(categoryId)) {
                    return error("删除失败，分类'" + categoryName + "'下存在任务，请先删除或移动任务");
                }
            }
        }

        return toAjax(dataCollectionCategoryService.deleteDataCollectionCategoryByCategoryIds(categoryIds));
    }

    /**
     * 移动分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:edit')")
    @Log(title = "移动分类", businessType = BusinessType.UPDATE)
    @PutMapping("/move")
    public AjaxResult move(@RequestBody DataCollectionCategory dataCollectionCategory)
    {
        // 检查是否存在循环引用
        if (dataCollectionCategory.getParentId() != null && dataCollectionCategory.getParentId() != 0) {
            List<Long> childIds = dataCollectionCategoryService.getChildCategoryIds(dataCollectionCategory.getCategoryId());
            if (childIds.contains(dataCollectionCategory.getParentId())) {
                return error("移动失败，不能将分类移动到自己的子分类下");
            }
            if (dataCollectionCategory.getCategoryId().equals(dataCollectionCategory.getParentId())) {
                return error("移动失败，不能将分类移动到自己下面");
            }
        }

        dataCollectionCategory.setUpdateBy(getUsername());
        return toAjax(dataCollectionCategoryService.moveCategory(dataCollectionCategory));
    }

    /**
     * 批量更新分类状态
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:edit')")
    @Log(title = "批量更新分类状态", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdateStatus")
    public AjaxResult batchUpdateStatus(@RequestBody Map<String, Object> params)
    {
        Long[] categoryIds = ((List<Integer>) params.get("categoryIds")).stream()
                .mapToLong(Integer::longValue).boxed().toArray(Long[]::new);
        Integer status = (Integer) params.get("status");

        if (categoryIds == null || categoryIds.length == 0) {
            return error("请选择要更新的分类");
        }

        // 这里可以调用Mapper的批量更新方法
        // return toAjax(dataCollectionCategoryMapper.batchUpdateStatus(categoryIds, status));

        // 临时实现：逐个更新
        int successCount = 0;
        for (Long categoryId : categoryIds) {
            DataCollectionCategory category = new DataCollectionCategory();
            category.setCategoryId(categoryId);
            category.setStatus(status);
            category.setUpdateBy(getUsername());
            successCount += dataCollectionCategoryService.updateDataCollectionCategory(category);
        }

        return toAjax(successCount);
    }

    /**
     * 检查分类编码是否唯一
     */
    @GetMapping("/checkCode")
    public AjaxResult checkCategoryCode(@RequestParam String categoryCode, @RequestParam(required = false) Long categoryId)
    {
        if (StringUtils.isEmpty(categoryCode)) {
            return error("分类编码不能为空");
        }

        DataCollectionCategory category = new DataCollectionCategory();
        category.setCategoryCode(categoryCode);
        category.setCategoryId(categoryId);
        boolean unique = dataCollectionCategoryService.checkCategoryCodeUnique(category);
        return success(unique);
    }

    /**
     * 检查分类名称是否唯一
     */
    @GetMapping("/checkName")
    public AjaxResult checkCategoryName(@RequestParam String categoryName,
                                        @RequestParam Long parentId,
                                        @RequestParam(required = false) Long categoryId)
    {
        if (StringUtils.isEmpty(categoryName)) {
            return error("分类名称不能为空");
        }

        DataCollectionCategory category = new DataCollectionCategory();
        category.setCategoryName(categoryName);
        category.setParentId(parentId);
        category.setCategoryId(categoryId);
        boolean unique = dataCollectionCategoryService.checkCategoryNameUnique(category);
        return success(unique);
    }

    /**
     * 获取分类的完整路径名称
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/fullName/{categoryId}")
    public AjaxResult getCategoryFullName(@PathVariable Long categoryId)
    {
        String fullName = dataCollectionCategoryService.getCategoryFullName(categoryId);
        return success(fullName);
    }

    /**
     * 根据分类编码查询分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/code/{categoryCode}")
    public AjaxResult getCategoryByCode(@PathVariable String categoryCode)
    {
        DataCollectionCategory category = dataCollectionCategoryService.selectCategoryByCode(categoryCode);
        return success(category);
    }

    /**
     * 获取分类统计信息
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/stats/{categoryId}")
    public AjaxResult getStats(@PathVariable Long categoryId)
    {
        Map<String, Object> stats = dataCollectionCategoryService.getCategoryStats(categoryId);
        return success(stats);
    }

    /**
     * 获取所有分类的统计信息
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/stats")
    public AjaxResult getAllStats()
    {
        // 可以返回分类层级统计、状态统计等
        return success("分类统计功能待实现");
    }

    /**
     * 批量删除分类
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:remove')")
    @Log(title = "批量删除分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch")
    public AjaxResult batchRemove(@RequestBody Long[] categoryIds)
    {
        if (categoryIds == null || categoryIds.length == 0) {
            return error("请选择要删除的分类");
        }

        // 检查每个分类是否可以删除
        for (Long categoryId : categoryIds) {
            if (!dataCollectionCategoryService.canDeleteCategory(categoryId)) {
                DataCollectionCategory category = dataCollectionCategoryService.selectDataCollectionCategoryByCategoryId(categoryId);
                String categoryName = category != null ? category.getCategoryName() : "ID:" + categoryId;

                if (dataCollectionCategoryService.hasChildCategory(categoryId)) {
                    return error("删除失败，分类'" + categoryName + "'存在子分类，请先删除子分类");
                }
                if (dataCollectionCategoryService.hasTask(categoryId)) {
                    return error("删除失败，分类'" + categoryName + "'下存在任务，请先删除或移动任务");
                }
            }
        }

        return toAjax(dataCollectionCategoryService.batchDeleteCategory(categoryIds));
    }

    /**
     * 根据分类名称模糊查询
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:list')")
    @GetMapping("/search")
    public AjaxResult searchByName(@RequestParam String categoryName)
    {
        if (StringUtils.isEmpty(categoryName)) {
            return error("搜索关键词不能为空");
        }

        DataCollectionCategory query = new DataCollectionCategory();
        query.setCategoryName(categoryName);
        List<DataCollectionCategory> list = dataCollectionCategoryService.selectDataCollectionCategoryList(query);
        return success(list);
    }

    /**
     * 获取分类的子分类ID列表
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/childIds/{categoryId}")
    public AjaxResult getChildCategoryIds(@PathVariable Long categoryId)
    {
        List<Long> childIds = dataCollectionCategoryService.getChildCategoryIds(categoryId);
        return success(childIds);
    }

    /**
     * 检查分类是否可以删除
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/canDelete/{categoryId}")
    public AjaxResult canDelete(@PathVariable Long categoryId)
    {
        boolean canDelete = dataCollectionCategoryService.canDeleteCategory(categoryId);
        String message = "";

        if (!canDelete) {
            if (dataCollectionCategoryService.hasChildCategory(categoryId)) {
                message = "该分类存在子分类，不能删除";
            } else if (dataCollectionCategoryService.hasTask(categoryId)) {
                message = "该分类下存在任务，不能删除";
            }
        }

        return success(Map.of("canDelete", canDelete, "message", message));
    }

    /**
     * 更新分类的任务数量
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:edit')")
    @Log(title = "更新分类任务数量", businessType = BusinessType.UPDATE)
    @PutMapping("/updateTaskCount/{categoryId}")
    public AjaxResult updateTaskCount(@PathVariable Long categoryId)
    {
        return toAjax(dataCollectionCategoryService.updateTaskCount(categoryId));
    }

    /**
     * 构建分类路径
     */
    @PreAuthorize("@ss.hasPermi('datacollection:category:query')")
    @GetMapping("/path/{categoryId}")
    public AjaxResult getCategoryPath(@PathVariable Long categoryId)
    {
        String path = dataCollectionCategoryService.buildCategoryPath(categoryId);
        return success(path);
    }
}
}
