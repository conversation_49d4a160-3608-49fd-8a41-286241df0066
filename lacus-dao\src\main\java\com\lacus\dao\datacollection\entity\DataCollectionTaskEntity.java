package com.lacus.dao.datacollection.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.lacus.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 数据采集任务实体类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("data_collection_task")
public class DataCollectionTaskEntity extends BaseEntity {

    /**
     * 任务ID
     */
    @TableId(value = "task_id", type = IdType.AUTO)
    private Long taskId;

    /**
     * 任务名称
     */
    @TableField("task_name")
    @NotBlank(message = "任务名称不能为空")
    @Size(min = 2, max = 100, message = "任务名称长度必须在2-100个字符之间")
    private String taskName;

    /**
     * 任务编码
     */
    @TableField("task_code")
    @NotBlank(message = "任务编码不能为空")
    @Size(min = 2, max = 50, message = "任务编码长度必须在2-50个字符之间")
    private String taskCode;

    /**
     * 分类ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 任务描述
     */
    @TableField("task_desc")
    private String taskDesc;

    /**
     * 采集类型(API/SQL/FILE)
     */
    @TableField("collection_type")
    private String collectionType;

    /**
     * 数据源类型(MYSQL/SQLSERVER/ORACLE/API)
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 数据源配置JSON
     */
    @TableField("source_config")
    private String sourceConfig;

    /**
     * 目标数据库
     */
    @TableField("target_database")
    private String targetDatabase;

    /**
     * 目标表
     */
    @TableField("target_table")
    private String targetTable;

    /**
     * 写入模式(OVERWRITE/APPEND/UPSERT/AUTO_CREATE)
     */
    @TableField("write_mode")
    private String writeMode;

    /**
     * 调度类型(MANUAL/CRON/REALTIME)
     */
    @TableField("schedule_type")
    private String scheduleType;

    /**
     * Cron表达式
     */
    @TableField("cron_expression")
    private String cronExpression;

    /**
     * 流程定义(JSON格式)
     */
    @TableField("flow_definition")
    private String flowDefinition;

    /**
     * 任务配置(JSON格式)
     */
    @TableField("task_config")
    private String taskConfig;

    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 超时时间(秒)
     */
    @TableField("timeout")
    private Integer timeout;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 状态(0:禁用 1:启用)
     */
    @TableField("status")
    private Integer status;

    /**
     * 版本号
     */
    @TableField("version")
    private Integer version;

    /**
     * 分类名称（非数据库字段）
     */
    @TableField(exist = false)
    private String categoryName;

    /**
     * 最后执行时间（非数据库字段）
     */
    @TableField(exist = false)
    private String lastExecuteTime;

    /**
     * 最后执行状态（非数据库字段）
     */
    @TableField(exist = false)
    private String lastExecuteStatus;

    /**
     * 执行次数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer executeCount;

    /**
     * 成功次数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer successCount;

    /**
     * 失败次数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer failureCount;

    /**
     * 平均执行时长（非数据库字段）
     */
    @TableField(exist = false)
    private Long avgExecuteTime;

    /**
     * 是否可以执行（非数据库字段）
     */
    @TableField(exist = false)
    private Boolean canExecute;

    /**
     * 是否正在执行（非数据库字段）
     */
    @TableField(exist = false)
    private Boolean isRunning;

    /**
     * 状态文本（非数据库字段）
     */
    @TableField(exist = false)
    private String statusText;

    /**
     * 调度类型文本（非数据库字段）
     */
    @TableField(exist = false)
    private String scheduleTypeText;

    /**
     * 采集类型文本（非数据库字段）
     */
    @TableField(exist = false)
    private String collectionTypeText;

    /**
     * 数据源类型文本（非数据库字段）
     */
    @TableField(exist = false)
    private String sourceTypeText;

    /**
     * 写入模式文本（非数据库字段）
     */
    @TableField(exist = false)
    private String writeModeText;
}
