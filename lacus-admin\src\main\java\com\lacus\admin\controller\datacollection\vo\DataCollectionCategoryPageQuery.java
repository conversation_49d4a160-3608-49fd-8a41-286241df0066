package com.lacus.admin.controller.datacollection.vo;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lacus.common.core.page.AbstractPageQuery;
import com.lacus.dao.flink.entity.FlinkJobEntity;
import com.lacus.dao.system.query.AbstractPageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 数据采集分类分页查询VO
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataCollectionCategoryPageQuery extends AbstractPageQuery {

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 父分类ID
     */
    private Long parentId;

    /**
     * 分类路径
     */
    private String categoryPath;

    /**
     * 分类层级
     */
    private Integer categoryLevel;

    /**
     * 分类图标
     */
    private String categoryIcon;

    /**
     * 状态(0:禁用 1:启用)
     */
    private Integer status;

    /**
     * 创建时间范围 - 开始时间
     */
    private String createTimeStart;

    /**
     * 创建时间范围 - 结束时间
     */
    private String createTimeEnd;

    /**
     * 是否包含子分类
     */
    private Boolean includeChildren;

    /**
     * 是否只查询启用状态
     */
    private Boolean onlyEnabled;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向
     */
    private String orderDirection;

    @Override
    public QueryWrapper<FlinkJobEntity> toQueryWrapper() {
        QueryWrapper<FlinkJobEntity> wrapper = new QueryWrapper<>();
        wrapper.like(ObjectUtils.isNotEmpty(categoryName), "category_name", categoryName);
        wrapper.eq(ObjectUtils.isNotEmpty(status), "status", status);
        wrapper.orderByDesc("create_time");
        return wrapper;
    }
}
