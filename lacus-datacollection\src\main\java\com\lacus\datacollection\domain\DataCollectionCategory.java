package com.lacus.datacollection.domain;

import com.lacus.common.annotation.Excel;
import com.lacus.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据采集分类对象 data_collection_category
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class DataCollectionCategory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 分类ID */
    private Long categoryId;

    /** 分类名称 */
    @Excel(name = "分类名称")
    @NotBlank(message = "分类名称不能为空")
    @Size(min = 2, max = 100, message = "分类名称长度必须在2-100个字符之间")
    private String categoryName;

    /** 分类编码 */
    @Excel(name = "分类编码")
    @NotBlank(message = "分类编码不能为空")
    @Size(min = 2, max = 50, message = "分类编码长度必须在2-50个字符之间")
    private String categoryCode;

    /** 父分类ID */
    @Excel(name = "父分类ID")
    private Long parentId;

    /** 分类路径 */
    @Excel(name = "分类路径")
    private String categoryPath;

    /** 分类层级 */
    @Excel(name = "分类层级")
    private Integer categoryLevel;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 分类图标 */
    @Excel(name = "分类图标")
    private String categoryIcon;

    /** 分类描述 */
    @Excel(name = "分类描述")
    private String categoryDesc;

    /** 状态(0:禁用 1:启用) */
    @Excel(name = "状态", readConverterExp = "0=禁用,1=启用")
    private Integer status;

    /** 子分类 */
    private List<DataCollectionCategory> children = new ArrayList<DataCollectionCategory>();

    /** 任务数量 */
    private Integer taskCount;

    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }
    public void setCategoryName(String categoryName) 
    {
        this.categoryName = categoryName;
    }

    public String getCategoryName() 
    {
        return categoryName;
    }
    public void setCategoryCode(String categoryCode) 
    {
        this.categoryCode = categoryCode;
    }

    public String getCategoryCode() 
    {
        return categoryCode;
    }
    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }
    public void setCategoryPath(String categoryPath) 
    {
        this.categoryPath = categoryPath;
    }

    public String getCategoryPath() 
    {
        return categoryPath;
    }
    public void setCategoryLevel(Integer categoryLevel) 
    {
        this.categoryLevel = categoryLevel;
    }

    public Integer getCategoryLevel() 
    {
        return categoryLevel;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    public void setCategoryIcon(String categoryIcon) 
    {
        this.categoryIcon = categoryIcon;
    }

    public String getCategoryIcon() 
    {
        return categoryIcon;
    }
    public void setCategoryDesc(String categoryDesc) 
    {
        this.categoryDesc = categoryDesc;
    }

    public String getCategoryDesc() 
    {
        return categoryDesc;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public List<DataCollectionCategory> getChildren()
    {
        return children;
    }

    public void setChildren(List<DataCollectionCategory> children)
    {
        this.children = children;
    }

    public Integer getTaskCount()
    {
        return taskCount;
    }

    public void setTaskCount(Integer taskCount)
    {
        this.taskCount = taskCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("categoryId", getCategoryId())
            .append("categoryName", getCategoryName())
            .append("categoryCode", getCategoryCode())
            .append("parentId", getParentId())
            .append("categoryPath", getCategoryPath())
            .append("categoryLevel", getCategoryLevel())
            .append("sortOrder", getSortOrder())
            .append("categoryIcon", getCategoryIcon())
            .append("categoryDesc", getCategoryDesc())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
