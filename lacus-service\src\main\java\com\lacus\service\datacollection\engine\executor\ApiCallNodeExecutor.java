package com.lacus.service.datacollection.engine.executor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lacus.service.datacollection.engine.NodeExecutor;
import com.lacus.service.datacollection.model.FlowContext;
import com.lacus.service.datacollection.model.FlowDefinition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API调用节点执行器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApiCallNodeExecutor implements NodeExecutor {

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String getSupportedNodeType() {
        return "API_CALL";
    }

    @Override
    public NodeExecutionResult execute(FlowDefinition.FlowNode node, FlowContext context) {
        try {
            log.info("开始执行API调用节点: {}", node.getName());
            context.addLog("开始执行API调用节点: " + node.getName());

            Map<String, Object> config = node.getConfig();
            
            // 获取配置参数
            String url = (String) config.get("url");
            String method = (String) config.getOrDefault("method", "GET");
            Map<String, Object> headers = (Map<String, Object>) config.getOrDefault("headers", new HashMap<>());
            Map<String, Object> params = (Map<String, Object>) config.getOrDefault("params", new HashMap<>());
            Integer timeout = (Integer) config.getOrDefault("timeout", 30000);

            // 替换变量
            url = replaceVariables(url, context);
            params = replaceVariablesInMap(params, context);

            context.addLog("API调用配置 - URL: " + url + ", Method: " + method);

            // 构建请求
            HttpHeaders httpHeaders = new HttpHeaders();
            headers.forEach((key, value) -> httpHeaders.add(key, String.valueOf(value)));

            HttpEntity<?> requestEntity;
            ResponseEntity<String> response;

            switch (method.toUpperCase()) {
                case "GET":
                    // GET请求，参数拼接到URL
                    String getUrl = buildUrlWithParams(url, params);
                    requestEntity = new HttpEntity<>(httpHeaders);
                    response = restTemplate.exchange(getUrl, HttpMethod.GET, requestEntity, String.class);
                    break;

                case "POST":
                    // POST请求，参数作为请求体
                    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                    requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(params), httpHeaders);
                    response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
                    break;

                case "PUT":
                    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                    requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(params), httpHeaders);
                    response = restTemplate.exchange(url, HttpMethod.PUT, requestEntity, String.class);
                    break;

                case "DELETE":
                    requestEntity = new HttpEntity<>(httpHeaders);
                    response = restTemplate.exchange(url, HttpMethod.DELETE, requestEntity, String.class);
                    break;

                default:
                    throw new RuntimeException("不支持的HTTP方法: " + method);
            }

            // 处理响应
            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                context.addLog("API调用成功，响应状态: " + response.getStatusCode());

                // 解析响应数据
                Object responseData = parseResponseData(responseBody);
                
                // 保存响应数据到上下文
                String outputVariable = (String) config.getOrDefault("outputVariable", "apiResponse");
                context.setGlobalVariable(outputVariable, responseData);
                context.setDataCache("lastApiResponse", responseData);

                // 如果是分页查询，处理分页数据
                handlePaginationResponse(responseData, context, config);

                context.addLog("API调用节点执行成功，数据已保存到变量: " + outputVariable);
                return new NodeExecutionResult("SUCCESS", "API调用成功", responseData);

            } else {
                String errorMsg = "API调用失败，状态码: " + response.getStatusCode();
                context.addLog(errorMsg);
                return new NodeExecutionResult("FAILED", errorMsg, null);
            }

        } catch (Exception e) {
            log.error("API调用节点执行失败: {}", e.getMessage(), e);
            context.addLog("API调用节点执行失败: " + e.getMessage());
            return new NodeExecutionResult("FAILED", "API调用失败: " + e.getMessage(), null);
        }
    }

    @Override
    public ValidationResult validate(FlowDefinition.FlowNode node) {
        Map<String, Object> config = node.getConfig();
        if (config == null) {
            return new ValidationResult(false, "API调用节点配置不能为空");
        }

        String url = (String) config.get("url");
        if (!StringUtils.hasText(url)) {
            return new ValidationResult(false, "API调用节点URL不能为空");
        }

        String method = (String) config.get("method");
        if (StringUtils.hasText(method)) {
            String upperMethod = method.toUpperCase();
            // 验证HTTP方法
            List<String> allowedMethods = new ArrayList<>();
            allowedMethods.add("GET");
            allowedMethods.add("POST");
            allowedMethods.add("PUT");
            allowedMethods.add("DELETE");
            if (!allowedMethods.contains(upperMethod)) {
                return new ValidationResult(false, "不支持的HTTP方法: " + method);
            }
        }

        return new ValidationResult(true, "验证通过");
    }

    /**
     * 替换字符串中的变量
     */
    private String replaceVariables(String text, FlowContext context) {
        if (text == null) {
            return null;
        }

        String result = text;
        
        // 替换全局变量 ${variableName}
        for (Map.Entry<String, Object> entry : context.getGlobalVariables().entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            if (result.contains(placeholder)) {
                result = result.replace(placeholder, String.valueOf(entry.getValue()));
            }
        }

        return result;
    }

    /**
     * 替换Map中的变量
     */
    private Map<String, Object> replaceVariablesInMap(Map<String, Object> map, FlowContext context) {
        Map<String, Object> result = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof String) {
                result.put(entry.getKey(), replaceVariables((String) value, context));
            } else {
                result.put(entry.getKey(), value);
            }
        }
        
        return result;
    }

    /**
     * 构建带参数的URL
     */
    private String buildUrlWithParams(String url, Map<String, Object> params) {
        if (params.isEmpty()) {
            return url;
        }

        StringBuilder urlBuilder = new StringBuilder(url);
        if (!url.contains("?")) {
            urlBuilder.append("?");
        } else {
            urlBuilder.append("&");
        }

        boolean first = true;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (!first) {
                urlBuilder.append("&");
            }
            urlBuilder.append(entry.getKey()).append("=").append(entry.getValue());
            first = false;
        }

        return urlBuilder.toString();
    }

    /**
     * 解析响应数据
     */
    private Object parseResponseData(String responseBody) {
        if (responseBody == null || responseBody.trim().isEmpty()) {
            return null;
        }

        try {
            // 尝试解析为JSON
            return objectMapper.readValue(responseBody, Object.class);
        } catch (Exception e) {
            // 如果不是JSON，返回原始字符串
            return responseBody;
        }
    }

    /**
     * 处理分页响应
     */
    private void handlePaginationResponse(Object responseData, FlowContext context, Map<String, Object> config) {
        try {
            if (responseData instanceof Map) {
                Map<String, Object> responseMap = (Map<String, Object>) responseData;
                
                // 检查是否包含分页信息
                if (responseMap.containsKey("data") && responseMap.containsKey("total")) {
                    Object data = responseMap.get("data");
                    Object total = responseMap.get("total");
                    
                    // 保存分页数据
                    context.setGlobalVariable("currentPageData", data);
                    context.setGlobalVariable("totalRecords", total);
                    
                    // 如果data是列表，保存记录数
                    if (data instanceof List) {
                        List<?> dataList = (List<?>) data;
                        context.setGlobalVariable("currentPageSize", dataList.size());
                        context.setGlobalVariable("resultCount", dataList.size());
                        
                        context.addLog("分页数据处理完成，当前页记录数: " + dataList.size() + "，总记录数: " + total);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("处理分页响应失败", e);
        }
    }
}
