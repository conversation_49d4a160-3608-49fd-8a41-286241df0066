<template>
  <div class="sql-query-node-config">
    <el-form :model="config" label-width="100px" size="small">
      <!-- 数据源配置 -->
      <el-form-item label="数据源" required>
        <el-select 
          v-model="config.dataSource" 
          placeholder="请选择数据源"
          @change="handleDataSourceChange"
        >
          <el-option 
            v-for="source in dataSources"
            :key="source.sourceId"
            :label="source.sourceName"
            :value="source.sourceName"
          >
            <div class="datasource-option">
              <span class="source-name">{{ source.sourceName }}</span>
              <span class="source-type">{{ source.sourceType }}</span>
            </div>
          </el-option>
        </el-select>
        <el-button 
          type="primary" 
          size="small" 
          @click="testConnection"
          :loading="testing"
          style="margin-left: 8px;"
        >
          测试连接
        </el-button>
      </el-form-item>

      <!-- SQL语句配置 -->
      <el-form-item label="SQL语句" required>
        <div class="sql-editor">
          <el-input 
            v-model="config.sql" 
            type="textarea"
            :rows="8"
            placeholder="请输入SQL查询语句，支持变量引用，如：SELECT * FROM users WHERE id > ${minId} LIMIT ${pageSize} OFFSET ${offset}"
          />
          <div class="sql-tools">
            <el-button size="small" @click="formatSql">格式化</el-button>
            <el-button size="small" @click="validateSql">验证语法</el-button>
            <el-button size="small" @click="showVariables">变量助手</el-button>
          </div>
        </div>
      </el-form-item>

      <!-- 参数配置 -->
      <el-form-item label="查询参数">
        <div class="parameters-config">
          <div 
            v-for="(param, index) in config.parameters" 
            :key="index"
            class="param-item"
          >
            <el-input 
              v-model="param.name" 
              placeholder="参数名"
              style="width: 120px; margin-right: 8px;"
            />
            <el-select 
              v-model="param.type" 
              placeholder="类型"
              style="width: 100px; margin-right: 8px;"
            >
              <el-option label="字符串" value="STRING" />
              <el-option label="数字" value="NUMBER" />
              <el-option label="日期" value="DATE" />
              <el-option label="布尔" value="BOOLEAN" />
              <el-option label="变量" value="VARIABLE" />
            </el-select>
            <el-input 
              v-model="param.value" 
              placeholder="参数值或变量引用"
              style="width: 150px; margin-right: 8px;"
            />
            <el-button 
              type="danger" 
              size="small" 
              icon="Delete"
              @click="removeParameter(index)"
            />
          </div>
          <el-button 
            type="primary" 
            size="small" 
            icon="Plus"
            @click="addParameter"
          >
            添加参数
          </el-button>
        </div>
      </el-form-item>

      <!-- 执行配置 -->
      <el-divider content-position="left">执行配置</el-divider>

      <el-form-item label="获取大小">
        <el-input-number 
          v-model="config.fetchSize" 
          :min="100"
          :max="10000"
          :step="100"
          placeholder="每次获取记录数"
        />
        <span class="form-tip">单次查询最大记录数</span>
      </el-form-item>

      <el-form-item label="查询超时">
        <el-input-number 
          v-model="config.queryTimeout" 
          :min="10"
          :max="3600"
          placeholder="秒"
        />
        <span class="form-tip">查询超时时间（秒）</span>
      </el-form-item>

      <el-form-item label="分页查询">
        <el-switch 
          v-model="config.pagination.enabled" 
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>

      <template v-if="config.pagination.enabled">
        <el-form-item label="分页方式">
          <el-select v-model="config.pagination.type" placeholder="选择分页方式">
            <el-option label="LIMIT OFFSET" value="LIMIT_OFFSET" />
            <el-option label="ROW_NUMBER" value="ROW_NUMBER" />
            <el-option label="游标分页" value="CURSOR" />
          </el-select>
        </el-form-item>
        <el-form-item label="页大小变量">
          <el-input 
            v-model="config.pagination.pageSizeVar" 
            placeholder="页大小变量名，如：pageSize"
          />
        </el-form-item>
        <el-form-item label="页码变量">
          <el-input 
            v-model="config.pagination.pageNumVar" 
            placeholder="页码变量名，如：pageNum"
          />
        </el-form-item>
      </template>

      <!-- 结果处理 -->
      <el-divider content-position="left">结果处理</el-divider>

      <el-form-item label="输出变量">
        <el-input 
          v-model="config.outputVariable" 
          placeholder="查询结果保存的变量名，如：queryResult"
        />
      </el-form-item>

      <el-form-item label="结果累积">
        <el-switch 
          v-model="config.accumulateResults" 
          active-text="启用"
          inactive-text="禁用"
        />
        <div class="form-tip">在循环中是否累积所有查询结果</div>
      </el-form-item>

      <el-form-item label="累积变量" v-if="config.accumulateResults">
        <el-input 
          v-model="config.accumulateVariable" 
          placeholder="累积结果变量名，如：allResults"
        />
      </el-form-item>

      <el-form-item label="字段映射">
        <el-switch 
          v-model="config.fieldMapping.enabled" 
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>

      <template v-if="config.fieldMapping.enabled">
        <el-form-item label="映射规则">
          <div class="field-mapping">
            <div 
              v-for="(mapping, index) in config.fieldMapping.rules" 
              :key="index"
              class="mapping-item"
            >
              <el-input 
                v-model="mapping.source" 
                placeholder="源字段"
                style="width: 120px; margin-right: 8px;"
              />
              <span style="margin-right: 8px;">→</span>
              <el-input 
                v-model="mapping.target" 
                placeholder="目标字段"
                style="width: 120px; margin-right: 8px;"
              />
              <el-select 
                v-model="mapping.transform" 
                placeholder="转换"
                style="width: 100px; margin-right: 8px;"
              >
                <el-option label="无" value="NONE" />
                <el-option label="大写" value="UPPER" />
                <el-option label="小写" value="LOWER" />
                <el-option label="日期格式" value="DATE_FORMAT" />
                <el-option label="数字格式" value="NUMBER_FORMAT" />
              </el-select>
              <el-button 
                type="danger" 
                size="small" 
                icon="Delete"
                @click="removeMapping(index)"
              />
            </div>
            <el-button 
              type="primary" 
              size="small" 
              icon="Plus"
              @click="addMapping"
            >
              添加映射
            </el-button>
          </div>
        </el-form-item>
      </template>

      <!-- 错误处理 -->
      <el-divider content-position="left">错误处理</el-divider>

      <el-form-item label="失败处理">
        <el-select v-model="config.onError" placeholder="请选择失败处理方式">
          <el-option label="停止流程" value="STOP" />
          <el-option label="跳过继续" value="SKIP" />
          <el-option label="重试" value="RETRY" />
        </el-select>
      </el-form-item>

      <el-form-item label="重试次数" v-if="config.onError === 'RETRY'">
        <el-input-number 
          v-model="config.retryCount" 
          :min="1"
          :max="10"
          placeholder="重试次数"
        />
      </el-form-item>

      <el-form-item label="重试间隔" v-if="config.onError === 'RETRY'">
        <el-input-number 
          v-model="config.retryDelay" 
          :min="1000"
          :max="60000"
          :step="1000"
          placeholder="毫秒"
        />
        <span class="form-tip">重试间隔时间（毫秒）</span>
      </el-form-item>
    </el-form>

    <!-- 配置预览 -->
    <el-divider content-position="left">配置预览</el-divider>
    <div class="config-preview">
      <el-alert 
        :title="getConfigSummary()" 
        type="info" 
        :closable="false"
        show-icon
      />
    </div>

    <!-- 测试按钮 -->
    <div class="test-section" v-if="detailed">
      <el-button type="success" @click="testQuery" :loading="testingQuery">
        <el-icon><Search /></el-icon>
        测试查询
      </el-button>
      <el-button type="info" @click="previewData" :loading="previewing">
        <el-icon><View /></el-icon>
        预览数据
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue'
import { Search, View } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  node: {
    type: Object,
    default: () => ({})
  },
  detailed: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

// 配置数据
const config = ref({
  dataSource: '',
  sql: '',
  parameters: [],
  fetchSize: 1000,
  queryTimeout: 300,
  pagination: {
    enabled: false,
    type: 'LIMIT_OFFSET',
    pageSizeVar: 'pageSize',
    pageNumVar: 'pageNum'
  },
  outputVariable: 'queryResult',
  accumulateResults: false,
  accumulateVariable: 'allResults',
  fieldMapping: {
    enabled: false,
    rules: []
  },
  onError: 'STOP',
  retryCount: 3,
  retryDelay: 5000,
  ...props.modelValue
})

const dataSources = ref([])
const testing = ref(false)
const testingQuery = ref(false)
const previewing = ref(false)

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', newConfig)
}, { deep: true })

// 组件挂载时加载数据源
onMounted(() => {
  loadDataSources()
})

// 加载数据源列表
function loadDataSources() {
  // 这里调用API获取数据源列表
  dataSources.value = [
    { sourceId: 1, sourceName: 'MySQL测试库', sourceType: 'MYSQL' },
    { sourceId: 2, sourceName: 'SQLServer测试库', sourceType: 'SQLSERVER' },
    { sourceId: 3, sourceName: 'Oracle测试库', sourceType: 'ORACLE' }
  ]
}

// 数据源变化处理
function handleDataSourceChange() {
  // 可以根据数据源类型调整SQL语法提示
}

// 测试连接
function testConnection() {
  testing.value = true
  setTimeout(() => {
    testing.value = false
  }, 2000)
}

// 添加参数
function addParameter() {
  config.value.parameters.push({ name: '', type: 'STRING', value: '' })
}

// 删除参数
function removeParameter(index) {
  config.value.parameters.splice(index, 1)
}

// 添加字段映射
function addMapping() {
  config.value.fieldMapping.rules.push({ source: '', target: '', transform: 'NONE' })
}

// 删除字段映射
function removeMapping(index) {
  config.value.fieldMapping.rules.splice(index, 1)
}

// 格式化SQL
function formatSql() {
  // 简单的SQL格式化
  if (config.value.sql) {
    config.value.sql = config.value.sql
      .replace(/\s+/g, ' ')
      .replace(/\s*,\s*/g, ',\n  ')
      .replace(/\s+FROM\s+/gi, '\nFROM ')
      .replace(/\s+WHERE\s+/gi, '\nWHERE ')
      .replace(/\s+ORDER\s+BY\s+/gi, '\nORDER BY ')
      .replace(/\s+GROUP\s+BY\s+/gi, '\nGROUP BY ')
      .replace(/\s+LIMIT\s+/gi, '\nLIMIT ')
  }
}

// 验证SQL语法
function validateSql() {
  // 简单的SQL验证
  if (!config.value.sql.trim()) {
    ElMessage.warning('SQL语句不能为空')
    return
  }
  
  const sql = config.value.sql.trim().toUpperCase()
  if (!sql.startsWith('SELECT')) {
    ElMessage.warning('只支持SELECT查询语句')
    return
  }
  
  ElMessage.success('SQL语法验证通过')
}

// 显示变量助手
function showVariables() {
  // 显示可用变量列表
  ElMessage.info('可用变量：${pageNum}, ${pageSize}, ${offset}, ${minId}, ${maxId}')
}

// 测试查询
function testQuery() {
  testingQuery.value = true
  setTimeout(() => {
    testingQuery.value = false
  }, 2000)
}

// 预览数据
function previewData() {
  previewing.value = true
  setTimeout(() => {
    previewing.value = false
  }, 2000)
}

// 配置摘要
const getConfigSummary = computed(() => {
  const dataSource = config.value.dataSource || '未选择'
  const hasParams = config.value.parameters.length > 0
  const pagination = config.value.pagination.enabled ? '，支持分页' : ''
  const accumulate = config.value.accumulateResults ? '，累积结果' : ''
  
  return `SQL查询：${dataSource}${hasParams ? '，含参数' : ''}${pagination}${accumulate}`
})
</script>

<style scoped>
.sql-query-node-config {
  max-height: 600px;
  overflow-y: auto;
}

.datasource-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.source-name {
  font-weight: 500;
}

.source-type {
  font-size: 12px;
  color: #909399;
}

.sql-editor {
  position: relative;
}

.sql-tools {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.parameters-config,
.field-mapping {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}

.param-item,
.mapping-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.param-item:last-child,
.mapping-item:last-child {
  margin-bottom: 12px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
  line-height: 1.4;
}

.config-preview {
  margin-top: 16px;
}

.test-section {
  margin-top: 16px;
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.el-divider {
  margin: 20px 0 16px 0;
}
</style>
