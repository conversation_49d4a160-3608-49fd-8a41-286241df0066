package com.lacus.dao.datacollection.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.lacus.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 数据采集分类实体类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("data_collection_category")
public class DataCollectionCategoryEntity extends BaseEntity<DataCollectionCategoryEntity> {

    /**
     * 分类ID
     */
    @TableId(value = "category_id", type = IdType.AUTO)
    private Long categoryId;

    /**
     * 分类名称
     */
    @TableField("category_name")
    @NotBlank(message = "分类名称不能为空")
    @Size(min = 2, max = 100, message = "分类名称长度必须在2-100个字符之间")
    private String categoryName;

    /**
     * 分类编码
     */
    @TableField("category_code")
    @NotBlank(message = "分类编码不能为空")
    @Size(min = 2, max = 50, message = "分类编码长度必须在2-50个字符之间")
    private String categoryCode;

    /**
     * 父分类ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 分类路径
     */
    @TableField("category_path")
    private String categoryPath;

    /**
     * 分类层级
     */
    @TableField("category_level")
    private Integer categoryLevel;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 分类图标
     */
    @TableField("category_icon")
    private String categoryIcon;

    /**
     * 分类描述
     */
    @TableField("category_desc")
    private String categoryDesc;

    /**
     * 状态(0:禁用 1:启用)
     */
    @TableField("status")
    private Integer status;


    /**
     * 子分类列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<DataCollectionCategoryEntity> children;

    /**
     * 任务数量（非数据库字段）
     */
    @TableField(exist = false)
    private Integer taskCount;

    /**
     * 分类完整名称（非数据库字段）
     */
    @TableField(exist = false)
    private String fullName;

    /**
     * 是否有子分类（非数据库字段）
     */
    @TableField(exist = false)
    private Boolean hasChildren;

    /**
     * 是否可以删除（非数据库字段）
     */
    @TableField(exist = false)
    private Boolean canDelete;
}
