package com.lacus.service.datawarehouse.engine;

import com.lacus.dao.datawarehouse.entity.EtlExecutionHistoryEntity;
import com.lacus.dao.datawarehouse.entity.EtlTaskEntity;
import com.lacus.dao.datawarehouse.mapper.EtlExecutionHistoryMapper;
import com.lacus.dao.datawarehouse.mapper.EtlTaskMapper;
import com.lacus.service.datawarehouse.model.EtlTaskModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * ETL执行引擎
 * 负责实际执行ETL任务，包括SQL生成、执行、监控等
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class EtlExecutionEngine {
    
    @Autowired
    private EtlTaskMapper etlTaskMapper;
    
    @Autowired
    private EtlExecutionHistoryMapper executionHistoryMapper;
    
    @Autowired
    private EtlSqlGenerator sqlGenerator;
    
    @Autowired
    private DorisExecutionEngine dorisEngine;
    
    @Autowired
    private DataQualityChecker qualityChecker;
    
    // 存储正在运行的任务
    private final ConcurrentMap<Long, String> runningTasks = new ConcurrentHashMap<>();
    
    /**
     * 执行ETL任务
     */
    public String executeTask(Long taskId) {
        // 检查任务是否已在运行
        if (isTaskRunning(taskId)) {
            throw new RuntimeException("任务正在运行中，请勿重复提交");
        }
        
        // 获取任务信息
        EtlTaskEntity task = etlTaskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("ETL任务不存在: " + taskId);
        }
        
        if (task.getStatus() != 1) {
            throw new RuntimeException("ETL任务未启用: " + taskId);
        }
        
        // 生成执行ID
        String executionId = UUID.randomUUID().toString();
        
        // 标记任务为运行中
        runningTasks.put(taskId, executionId);
        
        // 创建执行记录
        EtlExecutionHistoryEntity history = createExecutionHistory(task, executionId);
        
        // 异步执行任务
        executeTaskAsync(task, history);
        
        return executionId;
    }
    
    /**
     * 异步执行ETL任务
     */
    @Async("etlExecutor")
    public CompletableFuture<Void> executeTaskAsync(EtlTaskEntity task, EtlExecutionHistoryEntity history) {
        return CompletableFuture.runAsync(() -> {
            Long taskId = task.getTaskId();
            String executionId = history.getExecutionId();
            
            try {
                log.info("开始执行ETL任务: taskId={}, executionId={}", taskId, executionId);
                
                // 1. 解析任务配置
                EtlTaskModel taskModel = parseTaskConfig(task);
                
                // 2. 验证配置
                validateTaskConfig(taskModel);
                
                // 3. 生成SQL语句
                List<String> sqlStatements = sqlGenerator.generateSql(taskModel);
                log.info("生成SQL语句数量: {}, taskId={}", sqlStatements.size(), taskId);
                
                // 4. 执行SQL语句
                EtlExecutionResult result = dorisEngine.executeStatements(sqlStatements, taskModel);
                
                // 5. 执行数据质量检查
                if (hasQualityRules(taskModel)) {
                    qualityChecker.executeQualityCheck(taskModel, result);
                }
                
                // 6. 更新执行记录为成功
                updateExecutionHistory(history, result, "SUCCESS", null);
                
                // 7. 更新任务最后执行时间
                updateTaskLastRunTime(taskId, "SUCCESS");
                
                log.info("ETL任务执行成功: taskId={}, executionId={}, processedRows={}", 
                        taskId, executionId, result.getProcessedRows());
                
            } catch (Exception e) {
                log.error("ETL任务执行失败: taskId={}, executionId={}", taskId, executionId, e);
                
                // 更新执行记录为失败
                updateExecutionHistory(history, null, "FAILED", e.getMessage());
                
                // 更新任务最后执行状态
                updateTaskLastRunTime(taskId, "FAILED");
                
            } finally {
                // 移除运行中标记
                runningTasks.remove(taskId);
            }
        });
    }
    
    /**
     * 停止任务执行
     */
    public void stopTask(Long taskId) {
        String executionId = runningTasks.get(taskId);
        if (executionId == null) {
            log.warn("任务未在运行中: taskId={}", taskId);
            return;
        }
        
        log.info("停止ETL任务: taskId={}, executionId={}", taskId, executionId);
        
        try {
            // 停止Doris执行
            dorisEngine.stopExecution(executionId);
            
            // 更新执行记录
            EtlExecutionHistoryEntity history = executionHistoryMapper.selectByExecutionId(executionId);
            if (history != null && "RUNNING".equals(history.getStatus())) {
                updateExecutionHistory(history, null, "STOPPED", "用户手动停止");
            }
            
            // 更新任务状态
            updateTaskLastRunTime(taskId, "STOPPED");
            
        } catch (Exception e) {
            log.error("停止ETL任务失败: taskId={}", taskId, e);
        } finally {
            // 移除运行中标记
            runningTasks.remove(taskId);
        }
    }
    
    /**
     * 检查任务是否正在运行
     */
    public boolean isTaskRunning(Long taskId) {
        return runningTasks.containsKey(taskId);
    }
    
    /**
     * 创建执行历史记录
     */
    @Transactional(rollbackFor = Exception.class)
    private EtlExecutionHistoryEntity createExecutionHistory(EtlTaskEntity task, String executionId) {
        EtlExecutionHistoryEntity history = new EtlExecutionHistoryEntity();
        history.setExecutionId(executionId);
        history.setTaskId(task.getTaskId());
        history.setTaskName(task.getTaskName());
        history.setStatus("RUNNING");
        history.setExecutionStatus("RUNNING");
        history.setStartTime(new Date());
        history.setCreateTime(new Date());
        
        executionHistoryMapper.insert(history);
        return history;
    }
    
    /**
     * 更新执行历史记录
     */
    @Transactional(rollbackFor = Exception.class)
    private void updateExecutionHistory(EtlExecutionHistoryEntity history, 
                                      EtlExecutionResult result, 
                                      String status, 
                                      String errorMessage) {
        history.setStatus(status);
        history.setExecutionStatus(status);
        history.setEndTime(new Date());
        
        if (result != null) {
            history.setProcessedRecords(result.getProcessedRows());
            history.setErrorRecords(result.getErrorRows());
            history.setDuration(result.getDuration());
        }
        
        if (errorMessage != null) {
            history.setErrorMessage(errorMessage);
        }
        
        if (history.getStartTime() != null && history.getEndTime() != null) {
            history.setDuration(history.getEndTime().getTime() - history.getStartTime().getTime());
        }
        
        executionHistoryMapper.updateById(history);
    }
    
    /**
     * 更新任务最后执行时间
     */
    @Transactional(rollbackFor = Exception.class)
    private void updateTaskLastRunTime(Long taskId, String status) {
        etlTaskMapper.updateExecutionInfo(taskId, new Date(), status);
    }
    
    /**
     * 解析任务配置
     */
    private EtlTaskModel parseTaskConfig(EtlTaskEntity task) {
        EtlTaskModel model = new EtlTaskModel();
        BeanUtils.copyProperties(task, model);
        
        // 解析JSON配置
        // TODO: 实现JSON配置解析逻辑
        
        return model;
    }
    
    /**
     * 验证任务配置
     */
    private void validateTaskConfig(EtlTaskModel taskModel) {
        if (taskModel.getTargetTable() == null || taskModel.getTargetTable().trim().isEmpty()) {
            throw new IllegalArgumentException("目标表名不能为空");
        }
        
        if (taskModel.getSourceLayer() == null || taskModel.getTargetLayer() == null) {
            throw new IllegalArgumentException("源数据层级和目标数据层级不能为空");
        }
        
        // TODO: 添加更多验证逻辑
    }
    
    /**
     * 检查是否有质量规则
     */
    private boolean hasQualityRules(EtlTaskModel taskModel) {
        return taskModel.getQualityRules() != null && !taskModel.getQualityRules().isEmpty();
    }

    /**
     * 构建任务配置
     */
    private Map<String, Object> buildTaskConfig(EtlTaskModel model) {
        Map<String, Object> config = new HashMap<>();
        config.put("taskId", model.getTaskId());
        config.put("taskName", model.getTaskName());
        config.put("sourceLayer", model.getSourceLayer());
        config.put("targetLayer", model.getTargetLayer());
        config.put("targetTable", model.getTargetTable());
        config.put("writeMode", model.getWriteMode());
        config.put("sourceTablesConfig", model.getSourceTablesConfig());
        config.put("fieldMappingsConfig", model.getFieldMappingsConfig());
        return config;
    }
}
