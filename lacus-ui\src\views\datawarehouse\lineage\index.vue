<template>
  <div class="app-container">
    <!-- 顶部操作栏 -->
    <div class="top-bar">
      <div class="title-section">
        <h2 class="page-title">
          <el-icon class="title-icon"><Share /></el-icon>
          数据血缘关系
        </h2>
        <p class="page-desc">可视化展示ETL任务的数据流转关系和依赖链路</p>
      </div>
      <div class="action-section">
        <el-select
          v-model="selectedTask"
          placeholder="请选择ETL任务"
          @change="handleTaskChange"
          class="task-select"
          filterable
          clearable
        >
          <el-option
            v-for="task in etlTasks"
            :key="task.taskId"
            :label="task.taskName"
            :value="task.taskId"
          >
            <div class="task-option">
              <span class="task-name">{{ task.taskName }}</span>
              <span class="task-layer">{{ task.sourceLayer }} → {{ task.targetLayer }}</span>
            </div>
          </el-option>
        </el-select>
        <el-button type="primary" @click="refreshLineage" :loading="loading" class="refresh-btn">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 任务信息卡片 -->
    <el-card v-if="currentTaskInfo" class="task-info-card" shadow="hover">
      <div class="task-info">
        <div class="task-basic">
          <h3 class="task-title">{{ currentTaskInfo.taskName }}</h3>
          <div class="task-meta">
            <el-tag type="info" size="small">ID: {{ currentTaskInfo.taskId }}</el-tag>
            <el-tag :type="getLayerTagType(currentTaskInfo.sourceLayer)" size="small">
              源: {{ getLayerDisplayName(currentTaskInfo.sourceLayer) }}
            </el-tag>
            <el-tag :type="getLayerTagType(currentTaskInfo.targetLayer)" size="small">
              目标: {{ getLayerDisplayName(currentTaskInfo.targetLayer) }}
            </el-tag>
          </div>
        </div>
        <div class="task-stats">
          <div class="stat-item">
            <span class="stat-label">目标表</span>
            <span class="stat-value">{{ currentTaskInfo.targetTable }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">源表数量</span>
            <span class="stat-value">{{ currentTaskInfo.nodes?.filter(n => n.type === 'source').length || 0 }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 血缘图谱主体 -->
    <el-card class="lineage-main-card" shadow="hover">
      <template #header>
        <div class="graph-header">
          <span class="graph-title">
            <el-icon><Connection /></el-icon>
            血缘关系图谱
          </span>
          <div class="graph-controls">
            <el-button-group>
              <el-button size="small" @click="fitView">
                <el-icon><FullScreen /></el-icon>
                适应画布
              </el-button>
              <el-button size="small" @click="resetZoom">
                <el-icon><RefreshRight /></el-icon>
                重置缩放
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 空状态 -->
      <div v-if="!currentTaskInfo" class="empty-state">
        <el-empty description="请选择ETL任务查看血缘关系">
          <el-button type="primary" @click="getEtlTasks">加载任务列表</el-button>
        </el-empty>
      </div>

      <!-- 血缘图谱 -->
      <div v-else class="lineage-container">
        <div id="lineage-graph" ref="lineageGraph" class="lineage-graph"></div>
      </div>
    </el-card>

    <!-- 图例说明 -->
    <el-card class="legend-card" shadow="hover">
      <template #header>
        <span class="legend-title">
          <el-icon><Guide /></el-icon>
          图例说明
        </span>
      </template>
      <div class="legend-content">
        <div class="legend-item">
          <div class="legend-color ods-color"></div>
          <span>ODS层表</span>
          <small>操作数据存储层</small>
        </div>
        <div class="legend-item">
          <div class="legend-color dwd-color"></div>
          <span>DWD层表</span>
          <small>明细数据层</small>
        </div>
        <div class="legend-item">
          <div class="legend-color dws-color"></div>
          <span>DWS层表</span>
          <small>汇总数据层</small>
        </div>
        <div class="legend-item">
          <div class="legend-color ads-color"></div>
          <span>ADS层表</span>
          <small>应用数据层</small>
        </div>
        <div class="legend-item">
          <div class="legend-color etl-color"></div>
          <span>ETL转换</span>
          <small>数据处理流程</small>
        </div>
      </div>
    </el-card>

    <!-- 节点详情对话框 -->
    <el-dialog title="节点详情" v-model="nodeDetailVisible" width="60%">
      <el-descriptions :column="2" border v-if="selectedNode">
        <el-descriptions-item label="节点名称">{{ selectedNode.name }}</el-descriptions-item>
        <el-descriptions-item label="节点类型">{{ selectedNode.type }}</el-descriptions-item>
        <el-descriptions-item label="数据层级" v-if="selectedNode.layer">{{ selectedNode.layer }}</el-descriptions-item>
        <el-descriptions-item label="表名" v-if="selectedNode.tableName">{{ selectedNode.tableName }}</el-descriptions-item>
        <el-descriptions-item label="记录数" v-if="selectedNode.recordCount">{{ selectedNode.recordCount }}</el-descriptions-item>
        <el-descriptions-item label="更新时间" v-if="selectedNode.updateTime">{{ parseTime(selectedNode.updateTime) }}</el-descriptions-item>
        <el-descriptions-item label="描述" span="2" v-if="selectedNode.description">{{ selectedNode.description }}</el-descriptions-item>
      </el-descriptions>
      
      <!-- 字段信息 -->
      <el-card class="mt-4" v-if="selectedNode.fields && selectedNode.fields.length > 0">
        <template #header>字段信息</template>
        <el-table :data="selectedNode.fields" style="width: 100%">
          <el-table-column label="字段名" prop="fieldName"/>
          <el-table-column label="字段类型" prop="fieldType"/>
          <el-table-column label="是否主键" prop="isPrimaryKey">
            <template #default="scope">
              <el-tag v-if="scope.row.isPrimaryKey" type="warning" size="small">主键</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="注释" prop="comment" :show-overflow-tooltip="true"/>
        </el-table>
      </el-card>
    </el-dialog>
  </div>
</template>

<script setup name="DataLineage">
import { listEtlTask, getDataLineage } from '@/api/datawarehouse/etl';
import * as echarts from 'echarts';
import { Share, Refresh, Connection, FullScreen, RefreshRight, Guide } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

const etlTasks = ref([]);
const selectedTask = ref('');
const loading = ref(false);
const lineageGraph = ref(null);
const nodeDetailVisible = ref(false);
const selectedNode = ref(null);
const currentTaskInfo = ref(null);
let chartInstance = null;

/** 获取ETL任务列表 */
function getEtlTasks() {
  loading.value = true;
  listEtlTask({ pageNum: 1, pageSize: 1000 }).then(response => {
    etlTasks.value = response.rows || [];
    if (etlTasks.value.length > 0 && !selectedTask.value) {
      selectedTask.value = etlTasks.value[0].taskId;
      loadLineageData();
    }
  }).catch(error => {
    proxy.$modal.msgError('获取ETL任务列表失败');
    console.error(error);
  }).finally(() => {
    loading.value = false;
  });
}

/** 任务变化处理 */
function handleTaskChange() {
  if (selectedTask.value) {
    loadLineageData();
  } else {
    currentTaskInfo.value = null;
    if (chartInstance) {
      chartInstance.clear();
    }
  }
}

/** 刷新血缘关系 */
function refreshLineage() {
  if (selectedTask.value) {
    loadLineageData();
  } else {
    proxy.$modal.msgWarning('请先选择ETL任务');
  }
}

/** 获取数据层级显示名称 */
function getLayerDisplayName(layer) {
  const layerMap = {
    'ods_db': 'ODS层',
    'dwd_db': 'DWD层',
    'dws_db': 'DWS层',
    'ads_db': 'ADS层'
  };
  return layerMap[layer] || layer;
}

/** 获取数据层级标签类型 */
function getLayerTagType(layer) {
  const typeMap = {
    'ods_db': 'success',
    'dwd_db': 'primary',
    'dws_db': 'warning',
    'ads_db': 'danger'
  };
  return typeMap[layer] || 'info';
}

/** 加载血缘数据 */
function loadLineageData() {
  if (!selectedTask.value) return;

  loading.value = true;
  getDataLineage(selectedTask.value).then(response => {
    if (response.data) {
      currentTaskInfo.value = response.data;
      renderLineageGraph(response.data);
    } else {
      proxy.$modal.msgWarning('未获取到血缘关系数据');
      currentTaskInfo.value = null;
    }
  }).catch(error => {
    proxy.$modal.msgError('获取血缘关系失败');
    console.error(error);
    currentTaskInfo.value = null;
  }).finally(() => {
    loading.value = false;
  });
}

/** 渲染血缘图谱 */
function renderLineageGraph(data) {
  if (!data || !data.nodes || !data.edges) {
    console.warn('血缘数据格式不正确', data);
    return;
  }

  if (!chartInstance) {
    chartInstance = echarts.init(lineageGraph.value, null, {
      renderer: 'svg'
    });
  }

  // 处理节点数据
  const nodes = data.nodes.map(node => ({
    id: node.id,
    name: node.name,
    category: getNodeCategory(node.type, node.layer),
    symbolSize: getNodeSize(node.type),
    symbol: getNodeSymbol(node.type),
    itemStyle: {
      color: getNodeColor(node.type, node.layer),
      borderColor: '#fff',
      borderWidth: 2,
      shadowBlur: 10,
      shadowColor: 'rgba(0, 0, 0, 0.1)'
    },
    label: {
      show: true,
      fontSize: 12,
      fontWeight: 'bold',
      color: '#333'
    },
    emphasis: {
      itemStyle: {
        shadowBlur: 20,
        shadowColor: 'rgba(0, 0, 0, 0.3)'
      },
      label: {
        fontSize: 14
      }
    },
    ...node
  }));

  // 处理边数据
  const links = data.edges.map(edge => ({
    source: edge.source,
    target: edge.target,
    label: {
      show: true,
      formatter: edge.label || 'ETL转换',
      fontSize: 10,
      color: '#666'
    },
    lineStyle: {
      color: '#4CAF50',
      width: 3,
      type: 'solid',
      curveness: 0.2
    },
    emphasis: {
      lineStyle: {
        width: 5,
        color: '#2196F3'
      }
    }
  }));

  // 分类定义
  const categories = [
    { name: 'ODS层表', itemStyle: { color: '#67C23A' } },
    { name: 'DWD层表', itemStyle: { color: '#409EFF' } },
    { name: 'DWS层表', itemStyle: { color: '#E6A23C' } },
    { name: 'ADS层表', itemStyle: { color: '#F56C6C' } },
    { name: 'ETL转换', itemStyle: { color: '#9C27B0' } }
  ];

  // 图表配置
  const option = {
    backgroundColor: '#fafafa',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ddd',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      },
      formatter: function(params) {
        if (params.dataType === 'node') {
          const node = params.data;
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; font-size: 14px; margin-bottom: 8px;">
                ${node.name}
              </div>
              <div style="color: #666; font-size: 12px;">
                <div>类型: ${node.type === 'source' ? '源表' : '目标表'}</div>
                <div>层级: ${getLayerDisplayName(node.layer)}</div>
              </div>
            </div>
          `;
        } else if (params.dataType === 'edge') {
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold;">ETL数据转换</div>
              <div style="color: #666; font-size: 12px;">
                ${params.data.source} → ${params.data.target}
              </div>
            </div>
          `;
        }
        return '';
      }
    },
    legend: {
      data: categories.map(cat => cat.name),
      bottom: 20,
      itemGap: 20,
      textStyle: {
        fontSize: 12
      }
    },
    series: [{
      type: 'graph',
      layout: 'force',
      data: nodes,
      links: links,
      categories: categories,
      roam: true,
      focusNodeAdjacency: true,
      draggable: true,
      force: {
        repulsion: 2000,
        gravity: 0.05,
        edgeLength: [100, 300],
        layoutAnimation: true
      },
      emphasis: {
        focus: 'adjacency',
        lineStyle: {
          width: 6
        }
      },
      lineStyle: {
        opacity: 0.8
      }
    }]
  };

  chartInstance.setOption(option, true);

  // 添加点击事件
  chartInstance.off('click');
  chartInstance.on('click', function(params) {
    if (params.dataType === 'node') {
      selectedNode.value = params.data;
      nodeDetailVisible.value = true;
    }
  });

  // 窗口大小变化时重新调整
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  });
}

/** 获取节点分类 */
function getNodeCategory(type, layer) {
  if (type === 'source' || type === 'target') {
    const layerMap = {
      'ods_db': 'ODS层表',
      'dwd_db': 'DWD层表',
      'dws_db': 'DWS层表',
      'ads_db': 'ADS层表'
    };
    return layerMap[layer] || '未知层表';
  } else if (type === 'etl') {
    return 'ETL转换';
  }
  return '未知';
}

/** 获取节点大小 */
function getNodeSize(type) {
  if (type === 'etl') {
    return 80;
  } else if (type === 'target') {
    return 60;
  } else {
    return 50;
  }
}

/** 获取节点符号 */
function getNodeSymbol(type) {
  if (type === 'etl') {
    return 'diamond';
  } else if (type === 'target') {
    return 'rect';
  } else {
    return 'circle';
  }
}

/** 获取节点颜色 */
function getNodeColor(type, layer) {
  if (type === 'source' || type === 'target') {
    const colorMap = {
      'ods_db': '#67C23A',
      'dwd_db': '#409EFF',
      'dws_db': '#E6A23C',
      'ads_db': '#F56C6C'
    };
    return colorMap[layer] || '#909399';
  } else if (type === 'etl') {
    return '#9C27B0';
  }
  return '#909399';
}

/** 适应画布 */
function fitView() {
  if (chartInstance) {
    chartInstance.resize();
  }
}

/** 重置缩放 */
function resetZoom() {
  if (chartInstance) {
    chartInstance.setOption(chartInstance.getOption(), true);
  }
}

onMounted(() => {
  getEtlTasks();

  // 监听窗口大小变化
  nextTick(() => {
    window.addEventListener('resize', handleResize);
  });
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  window.removeEventListener('resize', handleResize);
});

/** 处理窗口大小变化 */
function handleResize() {
  if (chartInstance) {
    chartInstance.resize();
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 84px);
}

/* 顶部操作栏 */
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.title-section {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  color: #409EFF;
  font-size: 28px;
}

.page-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.action-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-select {
  width: 320px;
}

.task-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-name {
  font-weight: 500;
  color: #303133;
}

.task-layer {
  font-size: 12px;
  color: #909399;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 任务信息卡片 */
.task-info-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.task-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-basic {
  flex: 1;
}

.task-title {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.task-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.task-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* 血缘图谱主体 */
.lineage-main-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.graph-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.graph-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.graph-controls {
  display: flex;
  gap: 8px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.lineage-container {
  position: relative;
  background: white;
  border-radius: 4px;
}

.lineage-graph {
  width: 100%;
  height: 600px;
  border-radius: 4px;
}

/* 图例说明 */
.legend-card {
  border-radius: 8px;
}

.legend-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.legend-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.legend-item:hover {
  background: #e3f2fd;
  transform: translateY(-2px);
}

.legend-color {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.legend-item span {
  font-weight: 500;
  color: #303133;
}

.legend-item small {
  display: block;
  color: #909399;
  font-size: 12px;
  margin-top: 2px;
}

.ods-color {
  background: linear-gradient(135deg, #67C23A, #85ce61);
}

.dwd-color {
  background: linear-gradient(135deg, #409EFF, #66b1ff);
}

.dws-color {
  background: linear-gradient(135deg, #E6A23C, #ebb563);
}

.ads-color {
  background: linear-gradient(135deg, #F56C6C, #f78989);
}

.etl-color {
  background: linear-gradient(135deg, #9C27B0, #ba68c8);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-bar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: stretch;
  }

  .task-select {
    width: 100%;
  }

  .task-info {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .task-stats {
    justify-content: space-around;
  }

  .legend-content {
    grid-template-columns: 1fr;
  }

  .lineage-graph {
    height: 400px;
  }
}
</style>
